<?php

namespace App\Admin\Actions\Grid\Risk\Url;

use App\Models\MerchantUrl;
use Dcat\Admin\Admin;
use Dcat\Admin\Traits\LazyWidget;
use Dcat\Admin\Widgets\Form;
use Dcat\Admin\Contracts\LazyRenderable;

class RejectUrlNote extends Form implements LazyRenderable
{
    use LazyWidget;

    public function handle(array $input)
    {
        $id      = $this->payload['id'] ?? null;
        $urlInfo = MerchantUrl::find($id);

        if (!$urlInfo) {
            return $this->response()->error('网址信息不存在')->refresh();
        }
        
        if ($urlInfo->url_status == MerchantUrl::STATUS_TECHNICAL_REVIEW && !Admin::user()->isAdministrator()) {
            return $this->response()->error('没有权限操作')->refresh();
        }

        if (!in_array($urlInfo->url_status, [MerchantUrl::STATUS_CHECK, MerchantUrl::STATUS_TECHNICAL_REVIEW])) {
            return $this->response()->error('网址状态错误')->refresh();
        }

        $urlInfo->url_status     = MerchantUrl::STATUS_REJECT;
        $urlInfo->status_remarks = $input['status_remarks'] ?? '';

        if (!$urlInfo->save()) {
            return $this->response()->error('驳回失败')->refresh();
        }

        return $this->response()->success('驳回成功')->refresh();
    }

    public function form()
    {
        $this->confirm('您确定要驳回网址吗？', $this->payload['url_name']);
        $this->text('status_remarks', '驳回备注');
    }


    public function default()
    {
        return [
            'status_remarks' => '',
        ];
    }
}
