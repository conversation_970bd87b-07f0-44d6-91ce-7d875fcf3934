<?php

namespace App\Admin\Actions\Grid\Transfer;

use App\Models\Merchant;
use App\Models\TransferTicket;
use App\Services\MerchantService;
use Dcat\Admin\Actions\Response;
use Dcat\Admin\Contracts\LazyRenderable;
use Dcat\Admin\Form\Row;
use Dcat\Admin\Traits\LazyWidget;
use Dcat\Admin\Widgets\Form;
use Illuminate\Support\Facades\DB;

class CheckPass extends Form implements LazyRenderable
{
    use LazyWidget;

    /**
     * Handle the form request.
     *
     * @param array $input
     *
     * @return Response
     */
    public function handle(array $input)
    {
        $transfer = TransferTicket::find($input['id']);

        if (empty($transfer)
            || $transfer->applicant_id != $input['applicant_id']
            || $transfer->status != TransferTicket::TRANSFER_STATUS_CHECK) {
            return $this->response()->error('非法操作');
        }

        $merchant = Merchant::find($transfer->applicant_id);
        if (empty($merchant) || empty($merchant->transfer)) {
            return $this->response()->error('申请人数据错误');
        }

        //重新计算手续费
        foreach ($merchant->transfer as $val) {
            if ($val['transfer_currency'] == $transfer->convert_currency && $val['transfer_type'] == Merchant::RATIO_FEE) {
                $transfer->fee = amount_format($input['check_amount'] * $val['transfer_fee'] / 100);
            }
        }

        if (
            $transfer->fee_type == TransferTicket::INNER_BUCKLE
            && ($input['check_amount'] > $transfer->amount || $input['check_amount'] < $transfer->fee)
        ) {
            return $this->response()->error(sprintf('金额修改范围:%s ~ %s', $transfer->fee, $transfer->amount));
        }

        if (
            $transfer->fee_type == TransferTicket::EXTERNAL_BUCKLE
            && ($input['check_amount'] > $transfer->amount || $input['check_amount'] <= 0)
        ) {
            return $this->response()->error(sprintf('金额修改范围:%s ~ %s', 0, $transfer->amount));
        }

        // 更新
        $transfer->check_amount     = $input['check_amount'];
        $transfer->actual_amount    = $input['check_amount'];
        $transfer->deduction_amount = $input['check_amount'];
        $transfer->remarks          = $input['remarks'];
        $transfer->status           = TransferTicket::TRANSFER_STATUS_HANDLE;

        if ($transfer->fee_type == TransferTicket::EXTERNAL_BUCKLE) {
            $transfer->deduction_amount = amount_format($input['check_amount'] + $transfer->fee);
        }

        DB::beginTransaction();

        try {
            $transfer->save();
        } catch (Exception $exception) {
            DB::rollBack();
            return $this->response()->error('审批操作失败');
        }

        DB::commit();

        return $this->response()->success('审批操作成功')->refresh();
    }

    /**
     * Build a form here.
     */
    public function form()
    {
        // 提现信息
        $transfer = TransferTicket::find($this->payload['id']);

        // 获取结算信息
        $amountList = MerchantService::getAvailableAmountData($this->payload['merchant_id']);

        // 设置隐藏表单，传递merchant_id
        $this->hidden('id')->value($this->payload['id']);
        $this->hidden('applicant_id')->value($this->payload['merchant_id']);

        foreach ($amountList as $currency => $amount) {
            $this->row(function (Row $form) use ($currency, $amount) {
                $form->width(4)->display('currency', '货币')->default($currency);
                $form->width(4)->display('available_balance', '基本户余额')->default($amount['available_balance']);
                $form->width(4)->display('available_amount', '可提现金额')->default($amount['available_amount']);
            });
        }

        $this->row(function (Row $form) {
            $form->width(12)->divider();
        });

        $this->row(function (Row $form) use ($transfer) {
            $form->width(4)->display('currency', '提现货币')->default($transfer->currency);
            $form->width(4)->text('check_amount', '提现金额')->default($transfer->amount)->rules('required|numeric');
            $form->width(4)->text('remarks', '备注');
        });

        $this->row(function (Row $form) {
            $form->width(12)->divider();
        });

        $this->row(function (Row $form) use ($transfer) {
            $form->width(4)->display('convert_currency', '出款货币')->default($transfer->convert_currency);
            $form->width(4)->display('fee', '手续费')->default($transfer->fee);
            $form->width(4)->display('fee_currency', '手续费货币')->default($transfer->fee_currency);
        });

        $this->row(function (Row $form) use ($transfer) {
            $form->width(4)->display('fee_type', '提现扣款类型')->default(TransferTicket::$deductionMap[$transfer->fee_type] ?? '未知');
        });
    }
}
