<?php

namespace App\Admin\Controllers\CardVirtual;

use App\Models\CardBin;
use App\Models\Merchant;
use Dcat\Admin\Grid;
use App\Models\CardTransaction;
use DES3;

class CardTransactionsController extends CardBaseController
{
    protected $title = '卡交易';

    protected function grid()
    {
        return Grid::make(new CardTransaction(), function (Grid $grid) {
            $grid->model()->orderBy('id', 'desc');
            $grid->column('unique_id', 'UID');
            $grid->column('transaction_order_id', '渠道订单号');
            $grid->column('merchant_id', 'MID');
            $grid->column('cards_id', 'CID');
            $grid->column('bin_name', 'BIN段名称');
            $grid->column('card_number', '卡号')->display(function ($value) {
                return empty($value) ? '' : get_markcard(DES3::decrypt($value, env('DES3_CARD_VIRTUAL')));
            });
            $grid->column('amount', '交易金额');
            $grid->column('currency', '交易币种');
            $grid->column('settle_amount', '结算金额');
            $grid->column('settle_currency', '结算币种');
            $grid->column('transaction_type', '交易类型')->using(CardTransaction::$transactionTypeMap);
            $grid->column('transaction_status', '交易状态')->using(CardTransaction::$transactionStatusMap);
            $grid->column('fail_reason', '失败原因');
            $grid->column('transaction_description', '交易描述');
            $grid->column('transaction_mcc', 'mcc');
            $grid->column('created_at');
            $grid->column('updated_at');
            $grid->fixColumns(0, 0);
            $grid->disableActions();
            $grid->disableCreateButton();
            $grid->disableRowSelector();

            $grid->filter(function (Grid\Filter $filter) {
                $filter->equal('unique_id', 'UID');
                $filter->equal('transaction_order_id', '渠道订单号');
                $filter->equal('merchant_id', 'MID')->select(
                    Merchant::where('status', Merchant::STATUS_ENABLE)
                        ->pluck('merchant_name', 'merchant_id')
                        ->map(static function ($item, $key) {
                            return $item . ':' . $key;
                        })
                        ->toArray()
                );
                $filter->equal('cards_id', 'CID');
                $filter->equal('bin_name', 'BIN')->select(CardBin::get()->pluck('bin_name', 'bin_name'));
                $filter->where('card_number', function ($query) {
                    $query->where('card_number', DES3::encrypt($this->input, env('DES3_CARD_VIRTUAL')));
                });
                $filter->equal('transaction_type', '交易类型')->select(CardTransaction::$transactionTypeMap);
                $filter->equal('transaction_status', '交易状态')->select(CardTransaction::$transactionStatusMap);
                $filter->equal('transaction_settle_status', '渠道结算状态')->select(CardTransaction::$transactionChannelStatusMap);
                $filter->between('created_at')->datetime(['sideBySide'=>true]);
            });
        });
    }
}
