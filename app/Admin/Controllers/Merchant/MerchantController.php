<?php

namespace App\Admin\Controllers\Merchant;

use App\Admin\Actions\Grid\Merchant\CardBalance;
use App\Admin\Actions\Grid\Merchant\ResetAccount;
use App\Admin\Actions\Grid\Merchant\ResetPassword;
use App\Admin\Repositories\Merchant;
use App\Models\Merchant as MerchantModel;
use App\Models\OpenImUser;
use App\Models\User;
use App\Models\User as UserModel;
use App\Services\MerchantService;
use App\Services\OpenImService;
use Dcat\Admin\Admin;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Http\Controllers\AdminController;
use Illuminate\Validation\Rule;

class MerchantController extends AdminController
{
    protected $title = '商户信息';

    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        return Grid::make(new Merchant(), function (Grid $grid) {
            $grid->model()->orderBy('created_at', 'desc');
            $grid->merchant_id->sortable();
            $grid->merchant_name;
            $grid->phone;

            $grid->email;
            $grid->api_token;
            $grid->status->display(function ($value) {
                return MerchantModel::$statusMap[$value] ?? '未知';
            });
            $grid->created_at;
            $grid->updated_at->sortable();

            $grid->actions(function (Grid\Displayers\Actions $actions) {
                $url = admin_route('businesses.index', [$this->merchant_id]);

                if (Admin::user()->can('brp')) {
                    if (!$this->is_credit) {
                        $actions->append('<a class="disabled" style="color:#414750;" href="#">BID</a>');
                    } else {
                        $actions->append('<a href="' . $url . '">BID</a>');
                    }
                }
            });

            $grid->actions(function (Grid\Displayers\Actions $actions) {
                $url = admin_route('merchant_card.index', ['merchant_id' => $this->merchant_id]);

                if (Admin::user()->can('crp')) {
                    if (!$this->is_virtual) {
                        $actions->append('<a class="disabled" style="color:#414750;" href="#"> CID</a>');
                    } else {
                        $actions->append('<a href="' . $url . '"> CID</a>');
                    }
                }

                $actions->append(new CardBalance());
                $actions->append(new ResetAccount($this->merchant_id));
            });

            // 重置密码
            $grid->actions(function (Grid\Displayers\Actions $actions) {
                // 仅权限为 Risk Control Operation 可见
                if (Admin::user()->isRole('Risk Control Operation')) {
                    $actions->append(new ResetPassword($this->merchant_id));
                }
            });


            $grid->fixColumns(0, -1);

            $grid->disableEditButton();
            $grid->showQuickEditButton();
            $grid->enableDialogCreate();
            $grid->disableDeleteButton();
            $grid->disableViewButton();
            $grid->disableRowSelector();

            $grid->filter(function (Grid\Filter $filter) {
                $filter->equal('merchant_id')->select(
                    MerchantModel::where('status', MerchantModel::STATUS_ENABLE)
                        ->pluck('merchant_name', 'merchant_id')
                        ->map(static function ($item, $key) {
                            return $item . ':' . $key;
                        })
                        ->toArray()
                );
            });
        });
    }

    /**
     * Make a form builder.
     *
     * @return Form
     */
    protected function form()
    {
        return Form::make(new Merchant(['users', 'businesses']), function (Form $form) {
            if ($form->isCreating()) {
                $form->hidden('merchant_id');
                $form->hidden('users');
                $form->hidden('businesses');
                $form->hidden('api_token');
                $form->text('merchant_name')->rules('required|string|max:64')->setLabelClass(['asterisk']);
            } else {
                $form->display('merchant_id');
                $form->display('merchant_name');

                $data = [];
                if ($form->model()->is_credit) {
                    $data[] = 'is_credit';
                }

                if ($form->model()->is_virtual) {
                    $data[] = 'is_virtual';
                }

                $form->model()->merchant_type = $data;
                $form->radio('status')->options([MerchantModel::STATUS_DISABLE => '禁用', MerchantModel::STATUS_ENABLE => '启用'])->default(MerchantModel::STATUS_ENABLE);

                // 可提现币种处理
                $tempTransfer = $form->model()->transfer ?? [];
                $transfer     = empty($tempTransfer) ? $tempTransfer : array_column($form->model()->transfer, 'transfer_currency', 'transfer_currency');
                $form->select('transfer_account_type')->options(MerchantModel::$transferAccountTypeMap)
                    ->when(MerchantModel::TRANSFER_ACCOUNT_TYPE_BILLING, function (Form $form) use ($transfer) {
                        $form->select('transfer_currency')->options($transfer);
                    });
            }

            $form->text('phone')->required();
            $form->text('email')->required()
                ->creationRules([Rule::unique('merchants'), Rule::unique('users')])
                ->updateRules([Rule::unique('merchants')->ignore($form->getKey(), 'merchant_id')]);

            $options = [];
            $default = [];
            if (Admin::user()->can('brp')) {
                $options['is_credit'] = admin_trans_field('is_credit');
                $default[]            = 'is_credit';
            }

            if (Admin::user()->can('crp')) {
                $options['is_virtual'] = admin_trans_field('is_virtual');
                $default[]             = 'is_virtual';
            }

            // 仅管理员可修改
            if (Admin::user()->isAdministrator()) {
                $form->checkbox('merchant_type')->options($options)->default($default)->required();
            } else {
                $form->checkbox('merchant_type')->options($options)->default($default)->disable();
                if ($form->isCreating()) {
                    $form->is_credit  = 0;
                    $form->is_virtual = 0;
                    if (in_array('is_credit', $default)) {
                        $form->is_credit  = 1;
                    }

                    if (in_array('is_virtual', $default)) {
                        $form->is_virtual = 1;
                    }
                }
            }

            $form->hidden('is_credit');
            $form->hidden('is_virtual');

            $form->saving(function (Form $form) {
                if (Admin::user()->isAdministrator()) {
                    $form->is_credit  = 0;
                    $form->is_virtual = 0;

                    if (count($form->merchant_type)) {
                        foreach ($form->merchant_type as $item) {
                            if (!empty($item)) {
                                $form->{$item} = 1;
                            }
                        }
                    }
                }

                $form->deleteInput('merchant_type');
                unset($form->model()->merchant_type);

                if ($form->isCreating()) {
                    $form->merchant_id = MerchantModel::createMerchantId();
                    $form->api_token   = MerchantModel::getApiToken();

                    $form->users = [
                        [
                            'type'     => UserModel::TYPE_MAIN,
                            'name'     => 'admin',
                            'password' => bcrypt('12345678'),
                            'status'   => UserModel::STATUS_ENABLE,
                            'email'    => $form->input('email'),
                            '_remove_' => '0'
                        ]
                    ];

                    $form->businesses = [];
                    $merchant         = [
                        'business_id'   => $form->merchant_id . '001',
                        'merchant_id'   => $form->merchant_id,
                        'merchant_name' => $form->merchant_name
                    ];
                    $businessInfo     = MerchantService::getAddBusinessInfo($merchant);
                    if ($form->is_credit && !empty($businessInfo)) {
                        $form->businesses = [
                            $businessInfo
                        ];

                        // 默认生成国际卡收单计费 默认生成保底规则0-*-*
                        MerchantService::createChargeRateCc($form->merchant_id, $form->merchant_id . '001');
                    }
                }

                if ($form->isEditing()) {
                    $transferAccountType = $form->input('transfer_account_type');
                    $transferCurrency     = $form->input('transfer_currency');
                    if ($transferAccountType == MerchantModel::TRANSFER_ACCOUNT_TYPE_BILLING && empty($transferCurrency)) {
                        return $form->response()->error('默认提现币种不能为空！');
                    }

                    $merchant     = [
                        'business_id'   => $form->model()->merchant_id . '001',
                        'merchant_id'   => $form->model()->merchant_id,
                        'merchant_name' => $form->model()->merchant_name
                    ];
                    $businessInfo = MerchantService::getAddBusinessInfo($merchant);
                    if ($form->is_credit && !count($form->model()->businesses) && !empty($businessInfo)) {
                        // merchant更新时如果未有bid并且勾选了收单，创建bid

                        $form->model()->businesses()->create($businessInfo);

                        // 默认生成国际卡收单计费 默认生成保底规则0-*-*
                        MerchantService::createChargeRateCc($form->model()->merchant_id, $form->model()->merchant_id . '001');
                    }
                }
            })->saved(function (Form $form) {
                if ($form->isCreating()) {
                    if (config('open_im.open_im')) {
                        // 根据mid 查询商户的admin用户
                        $user = User::select('id', 'password')->where('merchant_id', $form->merchant_id)->where('type', UserModel::TYPE_MAIN)->first();
                        // 注册openIm
                        $openImUser = [
                            'userId'          => $user['id'],
                            'userType'        => OpenImUser::USER_TYPE_MERCHANT_MAIN,
                            'nickname'        => $form->merchant_name,
                            'password'        => $user['password'],
                            'addGroup'        => true,
                            'addFriend'       => false,
                            'createGroup'     => false,
                            'defaultAddGroup' => false,
                            'groupRoleLevel'  => OpenImUser::ROLE_LEVEL_MEMBER,
                            'userName'        => 'system',
                        ];
                        $openIm     = new OpenImService;
                        $userId     = $openIm->register($openImUser, $form->merchant_id, $form->merchant_name);
                        if ($userId == '') {
                            return $form->response()->error('openIm注册失败');
                        }
                        // 自动创建群组
                        if (!$openIm->createGroup($form->merchant_name, [$userId])) {
                            return $form->response()->error('openIm创建群组失败');
                        }
                    }
                }

                return true;
            });
        });
    }
}
