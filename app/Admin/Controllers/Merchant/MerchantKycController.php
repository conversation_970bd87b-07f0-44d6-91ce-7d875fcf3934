<?php

namespace App\Admin\Controllers\Merchant;

use App\Admin\Actions\Grid\Merchant\KycAdopt;
use App\Admin\Actions\Grid\Merchant\KycReject;
use App\Models\ChannelMerchant;
use App\Models\DirectoryCountry;
use App\Models\DirectoryCurrency;
use App\Models\LexisNexisScan;
use App\Models\Merchant;
use App\Models\Merchant as MerchantModel;
use App\Admin\Repositories\MerchantKyc;
use App\Models\MerchantKyc as MerchantKycModel;
use App\Models\MerchantKycBeneficial;
use App\Models\RiskCase;
use App\Services\LexisNexisScanService;
use Dcat\Admin\Admin;
use Dcat\Admin\Form;
use Dcat\Admin\Form\Row;
use Dcat\Admin\Grid;
use Dcat\Admin\Http\Controllers\AdminController;
use Dcat\Admin\Layout\Content;
use Dcat\Admin\Models\Administrator;
use Dcat\Admin\Show;
use Dcat\Admin\Widgets\Modal;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Storage;

class MerchantKycController extends AdminController
{
    protected $title = '商户KYC审核';

    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        return Grid::make(new MerchantKyc(), function (Grid $grid) {
            $grid->model()->where('created_at', '<', '2025-04-24')->orderByRaw("field(audit_result, '0') desc, created_at desc");
            $grid->model()->OfPermission();

            $grid->column('company_name');
            $grid->column('merchant_id');
            $grid->column('address');
            $grid->column('contacts_name');
            $grid->column('contacts_phone');
            $grid->column('contacts_email');
            $grid->column('contacts_position');
            $grid->column('type')->display(function ($value) {
                return MerchantKycModel::$typeMap[$value] ?? '未知';
            });

            $grid->column('audit_result')->display(function ($val) {
                return MerchantKycModel::$statusMap[$val] ?? '未知';
            })->dot(['0' => 'primary', '1' => 'success', '2' => 'danger'], 'primary');

            $grid->column('created_at', '提交时间');

            $grid->column('action')->display(function () {
                $url = admin_route('merchant_kyc.destroy', [$this->id]);
                $btn = '<a class="btn btn-primary" href="' . $url . '">详 情</a>&emsp;';

                if ($this->status == MerchantKycModel::STATUS_REJECT) {
                    $rejectContent = Modal::make()
                                          ->lg()
                                          ->scrollable()
                                          ->title('驳回备注')
                                          ->body($this->reject)
                                          ->button('<button class="btn btn-primary">查看驳回原因</button>');
                    $btn           .= $rejectContent;
                }

                if ($this->status == MerchantKycModel::STATUS_CHECK) {
                    $reject = Modal::make()
                                   ->title('Kyc驳回')
                                   ->xl()
                                   ->body(KycReject::make()->payload(['merchant_id' => $this->id, 'id' => $this->id]))
                                   ->button('<button class="btn btn-danger">驳回</button>');
                    $btn    .= new KycAdopt($this->id, $this->updated_at->toDateTimeString()) . '&emsp;';
                    $btn    .= $reject;
                }

                return $btn;
            });

            $grid->disableActions();
            $grid->disableCreateButton();

            $grid->filter(function (Grid\Filter $filter) {
                $filter->equal('merchant_id')->select(
                    MerchantModel::pluck('merchant_name', 'merchant_id')
                                 ->map(static function ($item, $key) {
                                     return $item . ':' . $key;
                                 })->toArray()
                );
                $filter->equal('type')->select(MerchantKycModel::$typeMap);
                $filter->equal('status')->select(MerchantKycModel::$statusMap);
            });
        });
    }

    /**
     * Make a show builder.
     *
     * @param mixed $id
     *
     * @return Show
     */
    protected function detail($id)
    {
        return Show::make($id, new MerchantKyc(), function (Show $show) {
            $url                      = config('filesystems.disks.merchant.url');
            $translate                = [
                admin_trans_field('mainland_type_info'),
                admin_trans_field('hongkong_type_info'),
                admin_trans_field('overseas_type_info'),
            ];
            $shareholderTranslate     = [
                admin_trans_field('mainland_shareholder_info'),
                admin_trans_field('other_shareholder_info'),
            ];
            $type                     = $show->model()->type;
            $shareholder_info         = $show->model()->holding_co;
            $translateType            = $translate[$type] ?? $translate[0];
            $shareholderTranslateType = $shareholderTranslate[$type] ?? $shareholderTranslate[1];

            $show->row(function (Show\Row $show) {
                $show->width(6)->company_name;
                $show->width(6)->merchant_id;
            });

            $show->row(function (Show\Row $show) {
                $show->width(6)->office_address;
                $show->width(6)->office_phone;
            });

            $show->row(function (Show\Row $show) use ($url, $translateType, $type, $shareholder_info, $shareholderTranslateType) {
                $show->width(12)->certificate_no($translateType['certificate_no']);
                $show->width(12)->comp_reg_cert($translateType['certificate_img'])->unescape()->as(function ($imgs) use ($url) {
                    return view('fileShow', ['imgs' => json_decode($imgs, true), 'url' => $url]);
                });
                if ($type == MerchantKycModel::TYPE_CHINESE_HONGKONG) {
                    $show->width(12)->business_reg_cert->unescape()->as(function ($imgs) use ($url) {
                        return view('fileShow', ['imgs' => json_decode($imgs, true), 'url' => $url]);
                    });
                }
                $show->width(12)->enforcer_name($translateType['enforcer_name']);
                $show->width(12)->fin_statement($translateType['enforcer_code'])->unescape()->as(function ($imgs) use ($url) {
                    return view('fileShow', ['imgs' => json_decode($imgs, true), 'url' => $url]);
                });
                $show->width(12)->reg_cert_no($translateType['enforcer_hold_code'])->unescape()->as(function ($imgs) use ($url) {
                    return view('fileShow', ['imgs' => json_decode($imgs, true), 'url' => $url]);
                });
                foreach ($shareholder_info as $shareholder) {
                    if (isset($shareholder['shareholder_name'], $shareholder['shareholder_code'])) {
                        $show->width(12)->shareholder_name($shareholderTranslateType['shareholder_name'])->as(function () use ($shareholder) {
                            return $shareholder['shareholder_name'];
                        });
                        $show->width(12)->shareholder_code($shareholderTranslateType['shareholder_code'])->unescape()->as(function () use ($url, $shareholder) {
                            return view('fileShow', ['imgs' => $shareholder['shareholder_code'], 'url' => $url]);
                        });
                    }
                }
                $show->width(12)->shareholder_structure()->unescape()->as(function ($imgs) use ($url) {
                    return view('fileShow', ['imgs' => json_decode($imgs, true), 'url' => $url]);
                });
                $show->width(12)->bank_statement()->unescape()->as(function ($imgs) use ($url) {
                    return view('fileShow', ['imgs' => json_decode($imgs, true), 'url' => $url]);
                });
                $show->width(12)->merchant_apply_file()->unescape()->as(function ($xlsx) {
                    $str = '';
                    foreach (json_decode($xlsx, true) as $key => $name) {
                        $str .= '<a href="' . Storage::disk('merchant')->url($name) . '" target="_blank">附件' . ($key + 1) . '</a>&emsp;';
                    }
                    return $str;
                });
            });

            $show->disableDeleteButton();
            $show->disableEditButton();
        });
    }

    /**
     * 获取kyc信息
     * @param $mid
     * @return array
     */
    public function getKycByMid($mid): array
    {
        $select = ["address", "company_name", "office_address"];
        $kyc    = MerchantKycModel::select($select)->where('merchant_id', $mid)->where('status', MerchantKycModel::STATUS_UP)->first() ?? new MerchantKycModel;
        $select = ["country", "state", "city", "street", "postcode"];
        $info   = ChannelMerchant::query()->select($select)->where('merchant_id', $mid)->first() ?? new MerchantKycModel;

        return [
            'kyc'  => $kyc,
            'info' => $info
        ];
    }

    protected function form(): Form
    {
        // 设定翻译文件
        app('admin.translator')->setPath('kyc');
        return Form::make(new MerchantKyc(['ubo' => function ($query) {
            $query->where('beneficial_type', MerchantKycBeneficial::BENEFICIAL_TYPE_UBO);
        }, 'director'                            => function ($query) {
            $query->where('beneficial_type', MerchantKycBeneficial::BENEFICIAL_TYPE_DIRECTOR);
        }, 'authorized'                          => function ($query) {
            $query->where('beneficial_type', MerchantKycBeneficial::BENEFICIAL_TYPE_AUTHORITY);
        }, 'case', 'merchant:merchant_id,source']), function (Form $form) {
            $form->ignore(['edd', 'edd_user_id']);

            $country  = DirectoryCountry::pluck('name', 'isoa2');
            $currency = DirectoryCurrency::pluck('code', 'code');

            Admin::script(
                <<<JS
                Dcat.ready(function (){
                    $('.field_ln_result a').off('click');
                })
                
                $('.field_ln_result span').off('click').on('click', function (){
                    $('.jumper-new-tab').attr('href',this.getAttribute('data-href'))

                    $('.jumper-new-tab').click()
                });
                JS
            );

            // cases 信息
            $form->tab(admin_trans_label('case'), function (Form $form) use ($country, $currency) {
                $form->row(function (Row $row) use ($country) {
                    $row->width(3)->text('case.case_number');
                    $row->width(3)->text('case.merchant_id');
                    $row->width(3)->select('case.case_type')->options(admin_trans('risk-case.options.case_type'));
                    $row->width(3)->text('case.auditor');
                    $row->width(3)->select('merchant.source')->options(admin_trans('kyc.options.source'));
                    $row->width(3)->text('case.created_at');
                    $row->width(3)->text('case.completion_at');
                });
            });

            // 企业基本信息
            $form->tab(admin_trans_label('company_details'), function (Form $form) use ($country, $currency) {
                $this->buildCompanyDetailsTab($form, $country, $currency);
            });

            // 审核信息
            $form->tab(admin_trans_label('audit_information'), function (Form $form) {
                $form->column(6, function (Form $form) {
                    $form->select('audit_result')->options(admin_trans('kyc.options.audit'))->required();
                    $form->select('edd')->options(admin_trans('kyc.options.confirm'))->when(1, function (Form $form) {
                        $user = Administrator::whereHas('roles', function ($query) {
                            $query->where('slug', RiskCase::getRole());
                        })
                                             ->pluck('name', 'id')
                                             ->toArray();
                        $form->select('edd_user_id')->options($user);
                    });
                });

                $form->column(6, function (Form $form) {
                    $form->textarea('audit_remark')->required();
                    $form->textarea('merchant_remark')->required();
                    $form->multipleFile('files')->autoUpload();
                });
            });

            if (request()->input('show')) {
                $form->disableHeader();
                $form->disableFooter();
                Admin::js('js/show.js');
            }

            $form->saving(function (Form $form) {
                if ($form->isEditing()) {
                    // 保存 cases 信息
                    $case     = RiskCase::query()->find($form->input('risk_case_id'));
                    $merchant = Merchant::where('merchant_id', $form->merchant_id)->first();

                    if (!$merchant) {
                        return;
                    }

                    $update = [
                        'audit_result' => $form->input('audit_result'),
                    ];

                    if ($form->input('audit_result') !== null && $form->input('audit_result') == RiskCase::CASE_AUDIT_APPROVE) {
                        $merchant->update(['status' => Merchant::STATUS_ENABLE]);
                    }

                    $update['completion_at'] = now();
                    $update['case_status']   = RiskCase::CASE_STATUS_COMPLETE;

                    // 更新 case
                    $case->update($update);
                }
            });
        });
    }

    public function getDetails($id)
    {
        $content = new Content();
        return $content->translation('kyc')->body($this->getDetailsTab()->edit($id));
    }

    public function getDetailsTab()
    {
        // 设定翻译文件
        app('admin.translator')->setPath('kyc');
        return Form::make(new MerchantKyc(['ubo' => function ($query) {
            $query->where('beneficial_type', MerchantKycBeneficial::BENEFICIAL_TYPE_UBO);
        }, 'director'                            => function ($query) {
            $query->where('beneficial_type', MerchantKycBeneficial::BENEFICIAL_TYPE_DIRECTOR);
        }, 'authorized'                          => function ($query) {
            $query->where('beneficial_type', MerchantKycBeneficial::BENEFICIAL_TYPE_AUTHORITY);
        }, 'case', 'merchant:merchant_id,source']), function (Form $form) {
            $form->ignore(['edd', 'edd_user_id']);
            $form->disableFooter();
            $form->disableHeader();

            $country  = DirectoryCountry::pluck('name', 'isoa2');
            $currency = DirectoryCurrency::pluck('code', 'code');

            Admin::script(
                <<<JS
                Dcat.ready(function (){
                    $('.field_ln_result a').off('click');
                })
                
                $('.field_ln_result span').off('click').on('click', function (){
                    $('.jumper-new-tab').attr('href',this.getAttribute('data-href'))

                    $('.jumper-new-tab').click()
                });
                JS
            );

            // 企业基本信息
            $form->tab("", function (Form $form) use ($country, $currency) {
                $this->buildCompanyDetailsTab($form, $country, $currency);
            });

            Admin::script(<<<'JS'
                        $('ul.nav-tabs.pl-1').first().hide();
                        $('.tab-content').find('form :input').prop('disabled', true);
                    JS
            );
        });
    }

    /**
     * 构建企业基本信息表单配置
     *
     * @param Form $form
     * @param Collection $country
     * @param Collection $currency
     * @return void
     */
    private function buildCompanyDetailsTab(Form $form, Collection $country, Collection $currency): void
    {
        $merchant_id = $form->model()->merchant_id;
        $form->hidden('id');
        $form->hidden('risk_case_id');
        $form->hidden('merchant_id');
        $form->row(function (Row $row) use ($country) {
            $row->width(3)->select('type', admin_trans_field('country'))->options(admin_trans('kyc.options.type'))->required();
            $row->width(3)->mobile('office_phone');
        });

        $form->row(function (Row $row) use ($country, $merchant_id) {
            $row->width(3)->text('company_name')->required();
            $row->width(3)->text('company_address')->required();
            $row->width(3)->date('cert_validity')->required();
            $row->width(3)->multipleSelect('export_country')->options($country)->required();
            $row->width(3)->text('reg_cert_no')->required();
            $row->width(3)->text('business_reg_no')->required();
            $row->width(3)->date('found_date')->required();
            $row->width(3)->file('fin_statement')->downloadable()->accept('jpg,png,gif,jpeg,pdf')->autoUpload();
            $row->width(3)->file('comp_reg_cert')->downloadable()->autoUpload()->required();
            $row->width(3)->file('business_reg_cert')->downloadable()->autoUpload()->required();
            $row->width(3)->file('shareholder_structure')->downloadable()->autoUpload()->required();
            $row->width(3)->file('annual_rpt')->downloadable()->autoUpload();
            $row->width(3)->file('bank_statement')->downloadable()->autoUpload()->required();
            $row->width(3)->file('comp_addr_cert')->downloadable()->autoUpload()->required();
            $row->width(3)->display('ln_result')->customFormat(function () use ($merchant_id) {
                return LexisNexisScanService::getLexisNexisScanResult([
                                                                          [
                                                                              'name' => $this->company_name,
                                                                              'identification' => $this->reg_cert_no,
                                                                          ],
                                                                          [
                                                                              'name' => $this->company_name,
                                                                              'identification' => $this->business_reg_no,
                                                                          ]
                                                                      ], $merchant_id, LexisNexisScan::ENTITY_TYPE_ENTERPRISE);
            });
        });

        $form->fieldset(admin_trans_label('business_info'), function (Form $form) use ($country, $currency) {
            $form->row(function (Row $row) use ($country, $currency) {
                $row->width(3)->text('nature_of_business')->required();
                $row->width(3)->select('business_model')->options(admin_trans('kyc.options.business_model'))->when(MerchantKycModel::BUSINESS_MODEL_ONLINE, function (Form $form) {
                    $form->width(3)->text('merchant_portal_url_app')->rules('required_if:business_model,=,' . MerchantKycModel::BUSINESS_MODEL_ONLINE, ['required']);
                })->when(MerchantKycModel::BUSINESS_MODEL_OFFLINE, function (Form $form) {
                    $form->array('store_info', function (Form\NestedForm $table) {
                        $table->width(3)->text('store_name_zh');
                        $table->width(3)->text('store_name_en');
                        $table->width(3)->select('store_address')->options(admin_trans('kyc.options.store_address'))->when(
                            MerchantKycModel::STORE_ADDRESS_TYPE_BUSINESS, function (Form\NestedForm $form) {
                            $form->textarea('store_business_address')->rules('required_if:store_address,=,' . MerchantKycModel::STORE_ADDRESS_TYPE_BUSINESS, ['required']);
                        }
                        )->when(MerchantKycModel::STORE_ADDRESS_TYPE_ES, function (Form\NestedForm $form) {
                            $form->textarea('store_es_address')->rules('required_if:store_address,=,' . MerchantKycModel::STORE_ADDRESS_TYPE_ES, ['required']);
                        })->required();
                    })->rules('required_if:business_model,=,' . MerchantKycModel::BUSINESS_MODEL_ONLINE, ['required'])->saveAsJson();
                })->required();

                $row->width(3)->text('mcc_code')->rules('min:4|max:4');
                $row->width(3)->select('payment_channel')->options(admin_trans('kyc.options.payment_channel'))->required();
                $row->width(3)->rate('transaction_fee_rate');
                $row->width(3)->rate('additional_fees');
                $row->width(3)->number('settlement_cycle');
                $row->width(3)->multipleSelect('transaction_currency')->options($currency);
                $row->width(3)->select('settlement_currency')->options($currency)->required();
            });

            $form->row(function (Row $row) {
                $row->width(3)->text('average_transaction_value');
                $row->width(3)->text('avg_daily_transaction_count');
                $row->width(3)->text('avg_daily_transaction_volume');
                $row->width(3)->text('avg_monthly_transaction_count');
                $row->width(3)->text('avg_monthly_transaction_volume');
            });
        });

        $form->fieldset(admin_trans_label('holding_co'), function (Form $form) use ($country, $merchant_id) {
            // 控股公司
            $form->table('holding_co', function (Form\NestedForm $table) use ($country, $merchant_id) {
                $table->text('company_name')->required();
                $table->text('reg_cert_no')->required();
                $table->rate('equity_ratio')->required();
                $table->select('country')->options($country)->required();
                $table->textarea('company_address')->required();
                $table->display('ln_result')->customFormat(function () use ($merchant_id) {
                    return LexisNexisScanService::getLexisNexisScanResult([
                                                                              [
                                                                                  'name' => $this->company_name,
                                                                                  'identification' => $this->reg_cert_no
                                                                              ]
                                                                          ], $merchant_id, LexisNexisScan::ENTITY_TYPE_ENTERPRISE);
                });
            })->width(12)->saveAsJson()->required();
        });

        $form->fieldset(admin_trans_label('ubo'), function (Form $form) use ($country, $merchant_id) {
            $form->hasMany('ubo', function (Form\NestedForm $form) use ($country, $merchant_id) {
                $form->column(6, function (Form\NestedForm $form) use ($country) {
                    $form->hidden('beneficial_type')->value(MerchantKycBeneficial::BENEFICIAL_TYPE_UBO);
                    $form->select('cert_type')->options(admin_trans('kyc.options.cert_type'))->required();
                    $form->select('nationality')->options($country)->required();
                    $form->text('name')->required();
                    $form->rate('share_ratio')->required();
                    $form->text('address')->required();
                    $form->multipleFile('cert_file')->downloadable()->autoUpload()->limit(2)->required();
                });

                $form->column(6, function (Form\NestedForm $form) use ($merchant_id) {
                    $form->date('birth_date')->required();
                    $form->text('cert_number')->required();
                    $form->file('residence_doc')->downloadable()->autoUpload()->required();
                    $form->radio('is_signatory')->options(admin_trans('kyc.options.confirm'))->when(true, function (Form\NestedForm $form) {
                        $form->file('hold_cert_photo')->downloadable()->autoUpload();
                    })->required();
                    $form->display('ln_result')->customFormat(function () use ($merchant_id) {
                        return LexisNexisScanService::getLexisNexisScanResult([
                                                                                  [
                                                                                      'name' => $this->name,
                                                                                      'identification' => $this->cert_number
                                                                                  ]
                                                                              ], $merchant_id, LexisNexisScan::ENTITY_TYPE_INDIVIDUAL);
                    });
                });
            });
        });

        $form->fieldset(admin_trans_label('director'), function (Form $form) use ($country, $merchant_id) {
            $form->hasMany('director', function (Form\NestedForm $form) use ($country, $merchant_id) {
                $form->column(6, function (Form\NestedForm $form) use ($country) {
                    $form->hidden('beneficial_type')->value(MerchantKycBeneficial::BENEFICIAL_TYPE_DIRECTOR);
                    $form->select('cert_type')->options(admin_trans('kyc.options.cert_type'))->required();
                    $form->select('nationality')->options($country)->required();
                    $form->text('name')->required();
                    $form->rate('share_ratio')->required();
                    $form->radio('is_listed_in_nar1')->options(admin_trans('kyc.options.confirm'))->required();
                    $form->multipleFile('cert_file')->downloadable()->autoUpload()->limit(2)->required();
                });

                $form->column(6, function (Form\NestedForm $form) use ($merchant_id) {
                    $form->date('birth_date')->required();
                    $form->text('cert_number')->required();
                    $form->text('address')->required();
                    $form->file('residence_doc')->downloadable()->autoUpload()->required();
                    $form->radio('is_signatory', admin_trans_field('is_signatory_b'))->options(admin_trans('kyc.options.confirm'))->when(true, function (Form\NestedForm $form) {
                        $form->file('hold_cert_photo')->downloadable()->autoUpload();
                    })->required();
                    $form->display('ln_result')->customFormat(function () use ($merchant_id) {
                        return LexisNexisScanService::getLexisNexisScanResult([['name' => $this->name, 'identification' => $this->cert_number]], $merchant_id, LexisNexisScan::ENTITY_TYPE_INDIVIDUAL);
                    });
                });
            });
        });

        $form->fieldset(admin_trans_label('authorized'), function (Form $form) use ($country) {
            $form->hasMany('authorized', function (Form\NestedForm $form) use ($country) {
                $form->hidden('beneficial_type')->value(MerchantKycBeneficial::BENEFICIAL_TYPE_AUTHORITY);
                $form->select('is_signatory', admin_trans_field('is_signatory_c'))
                     ->options(admin_trans('kyc.options.is_signatory'))
                     ->required()->when(0, function (Form\NestedForm $form) use ($country) {
                        $form->width(3)->select('cert_type')->options(admin_trans('kyc.options.cert_type'));
                        $form->width(3)->select('nationality')->options($country);
                        $form->width(3)->text('name');
                        $form->width(3)->text('address');
                        $form->width(3)->multipleFile('cert_file')->accept('jpg,png,gif,jpeg,pdf,xlsx')->downloadable()->autoUpload();
                        $form->width(3)->date('birth_date');
                        $form->width(3)->text('cert_number');
                        $form->width(3)->file('residence_doc')->accept('jpg,png,gif,jpeg,pdf,xlsx')->downloadable()->autoUpload()->help(admin_trans_field('help.residence_doc'));
                        $form->width(3)->file('hold_cert_photo', admin_trans_field('hold_cert_photo_c'))->accept('jpg,png,gif,jpeg,pdf,xlsx')->downloadable()->autoUpload();
                    });
            })->default(1)->disableDelete()->disableCreate();
        });

        // 业务联系人
        $form->fieldset(admin_trans_label('contacts'), function (Form $form) use ($country) {
            $form->row(function (Row $form) {
                $form->width(3)->text('contacts_name')->required();
                $form->width(3)->text('contacts_phone')->required();
                $form->width(3)->email('contacts_email')->required();
                $form->width(3)->text('contacts_position')->required();
            });
        });

        $form->file('merchant_apply_file')->accept('jpg,png,gif,jpeg,pdf,xlsx')->downloadable()->autoUpload();
    }
}
