<?php

namespace App\Admin\Controllers\RiskControl\Cases;

use App\Admin\Actions\Grid\RiskControl\Cases\Assign;
use App\Admin\Actions\Grid\RiskControl\Cases\Audit;
use App\Admin\Actions\Grid\RiskControl\Cases\Claim;
use App\Admin\Repositories\RiskCase;
use App\Models\Merchant;
use App\Models\MerchantKyc;
use App\Models\RiskCase as RiskCaseModel;
use Dcat\Admin\Admin;
use Dcat\Admin\Grid;
use Dcat\Admin\Http\Controllers\AdminController;
use Dcat\Admin\Models\Administrator;

class RiskCaseController extends AdminController
{
    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        Admin::html(
            '<a
            style="display: none"
            class="jumper-new-tab"
            target="_blank"
            iframe-extends="true"
            iframe-tab="true">'.admin_trans_label('detail').'</a>'
        );

        return Grid::make(new RiskCase(), function (Grid $grid) {
            $grid->fixColumns(1, -1);
            $grid->model()->orderByDesc('created_at');
            $grid->disableBatchActions();
            $grid->disableRowSelector();
            $grid->showColumnSelector();
            $grid->disableEditButton();
            $grid->disableQuickEditButton();
            $grid->disableDeleteButton();
            $grid->disableViewButton();
            $grid->disableCreateButton();

            $grid->hideColumns(['id', 'user_id', 'updated_at']);

            $grid->column('id')->sortable();
            $grid->column('merchant_id');
            $grid->column('merchant_name');
            $grid->column('case_number')->sortable();
            $grid->column('case_type')->display(function ($caseType) {
                return admin_trans('risk-case.options.case_type.' . $caseType) ?? '';
            });
            $grid->column('country')->display(function ($country) {
                return admin_trans('risk-case.options.country.' . $country) ?? '';
            });
            $grid->column('case_status')->display(function ($caseStatus) {
                return admin_trans('risk-case.options.case_status.' . $caseStatus) ?? '';
            });
            $grid->column('audit_result')->display(function ($auditResult) {
                return admin_trans('risk-case.options.audit_result.' . $auditResult) ?? '';
            });
            $grid->column('auditor');
            $grid->column('user_id');
            $grid->column('completion_at')->sortable();
            $grid->column('created_at')->sortable();
            $grid->column('updated_at')->sortable();

            $grid->filter(function (Grid\Filter $filter) {
                $filter->panel();
                $filter->equal('case_number')->width(2);
                $merchants = Merchant::pluck('merchant_name', 'merchant_id')
                                     ->map(static function ($item, $key) {
                                         return $item . ':' . $key;
                                     })
                                     ->toArray();
                $filter->in('merchant_id')->multipleSelect($merchants)->width(4);
                $filter->equal('case_type')->select(admin_trans('risk-case.options.case_type'))->width(2);
                $filter->where('country', function ($query) {
                    if ($this->input == MerchantKyc::TYPE_OVERSEAS) {
                        $query->where('country', '!=', MerchantKyc::TYPE_CHINESE_MAINLAND)
                            ->where('country', '!=', MerchantKyc::TYPE_CHINESE_HONGKONG);
                    } else {
                        $query->where('country', $this->input);
                    }
                })->select(admin_trans('risk-case.options.country'))->width(2);
                $filter->equal('case_status')->select(admin_trans('risk-case.options.case_status'))->width(2);
                $filter->equal('audit_result')->select(admin_trans('risk-case.options.audit_result'))->width(2);
                $user = Administrator::whereHas('roles', function ($query) {
                    $query->where('slug', RiskCaseModel::getRole());
                })
                                     ->pluck('name', 'id')
                                     ->toArray();
                $filter->equal('user_id', admin_trans_field('auditor'))->select($user)->width(2);
                $filter->between('created_at')->datetime()->width(3);
                $filter->between('completion_at')->datetime()->width(3);
            });

            $grid->actions(function (Grid\Displayers\Actions $actions) {
                $userId = $actions->row->user_id;
                $user   = Admin::user();
                $isRisk = $user->isRole(RiskCaseModel::getRole());
                if (!$userId && $isRisk && $actions->row->case_status != RiskCaseModel::CASE_STATUS_COMPLETE) {
                    $actions->append(new Claim());
                }

                if ($user->isAdministrator() && $isRisk && $actions->row->case_status != RiskCaseModel::CASE_STATUS_COMPLETE) {
                    $actions->append(new Assign());
                }

                // 根据case_type获取不同的模型
                $model = RiskCaseModel::getModelByCaseType($actions->row->case_type);

                if ($userId == $user->id && $isRisk && $actions->row->case_status != RiskCaseModel::CASE_STATUS_COMPLETE) {
                    $actions->append(new Audit($model, admin_trans_field('review')));
                }

                if ($actions->row->case_status == RiskCaseModel::CASE_STATUS_COMPLETE) {
                    $actions->append(new Audit($model, admin_trans_field('show')));
                }
            });
        });
    }

//    /**
//     * Make a show builder.
//     *
//     * @param mixed $id
//     *
//     * @return Show
//     */
//    protected function detail($id)
//    {
//        return Show::make($id, new RiskCase(), function (Show $show) {
//            $show->field('id');
//            $show->field('merchant_id');
//            $show->field('case_number');
//            $show->field('case_type');
//            $show->field('country');
//            $show->field('case_status');
//            $show->field('audit_result');
//            $show->field('auditor');
//            $show->field('user_id');
//            $show->field('completion_at');
//            $show->field('created_at');
//            $show->field('updated_at');
//        });
//    }
//
//    /**
//     * Make a form builder.
//     *
//     * @return Form
//     */
//    protected function form()
//    {
//        return Form::make(new RiskCase(), function (Form $form) {
//            $form->display('id');
//            $form->text('merchant_id');
//            $form->text('case_number');
//            $form->text('case_type');
//            $form->text('country');
//            $form->text('case_status');
//            $form->text('audit_result');
//            $form->text('auditor');
//            $form->select('user_id');
//            $form->text('completion_at');
//
//            $form->display('created_at');
//            $form->display('updated_at');
//        });
//    }
}
