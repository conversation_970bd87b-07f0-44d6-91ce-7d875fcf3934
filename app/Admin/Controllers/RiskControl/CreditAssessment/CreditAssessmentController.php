<?php

namespace App\Admin\Controllers\RiskControl\CreditAssessment;

use App\Admin\Repositories\RiskCreditAssessment;
use App\Models\RiskCase;
use App\Models\RiskMcc;
use App\Models\RiskCreditAssessment as RiskCreditAssessmentModel;
use Dcat\Admin\Admin;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Http\Controllers\AdminController;
use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Symfony\Component\HttpFoundation\Response;

class CreditAssessmentController extends AdminController
{
    public function form(): Form
    {
        return Form::make(new RiskCreditAssessment(['case.riskCasesBid', 'merchant:merchant_id,source']), function (Form $form) {
            if (request()->input('show') === null) {
                $formId = $form->getElementId();
                Admin::script(
                    <<<JS
                    window.creditAssessmentConfig = {
                       data: '$formId'
                    }
                JS
                );
                Admin::js('/js/decimal.js');
                Admin::js('/js/credit-assessment.js?s='.$form->getElementId());
            }

            // 信用风险评估报告
            $form->tab(admin_trans_label('credit_risk_assessment'), function (Form $form) {
                $form->width(3)->hidden('risk_case_id');
                $form->row(function (Form\Row $row) {
                    $row->width(3)->display('case.case_number');
                    $row->width(3)->display('merchant_id');
                    $row->width(3)->display('case.riskCasesBid', admin_trans_field('business_id'))
                        ->customFormat(function ($value) {return $value[0]['business_id'] ?? '';});
                    $row->width(3)->select('case.case_type')
                        ->options(admin_trans('risk-case.options.case_type'));
                });
                $form->row(function (Form\Row $row) {
                    $row->width(3)->display('case.auditor');
                    $row->width(3)->select('merchant.source')
                        ->options(admin_trans('credit-assessment.options.merchant_source')
                    );
                    $row->width(3)->display('case.created_at');
                    $row->width(3)->display('case.completion_at');
                });
            });

            // 处理细节
            $form->tab(admin_trans_label('processing_details'), function (Form $form) {
                $this->commonFormFiled($form);
            });

            // 审核信息
            $form->tab(admin_trans_label('audit_information'), function (Form $form) {
                $form->column(6, function (Form $form) {
                    $form->select('audit_result')->options(admin_trans('kyc.options.audit'))->required();
                });

                $form->column(6, function (Form $form) {
                    $form->textarea('audit_remark')->required();
                    $form->multipleFile('files')->autoUpload()->required();
                });
            });

            if (request()->input('show')) {
                Admin::script(<<<JS
                        $('.layui-layer-btn0').hide()
                        $('.form-content').find('form :input').prop('disabled', true);
                    JS
                );
            }

            $form->confirm(admin_trans_label('confirm_save'), admin_trans_label('confirm_save_message'));

            $form->saving(function (Form $form) {
              if ($form->isEditing()) {
                  $form->deleteInput('mcc_id');
                  $tableData = $form->input('mcc_table') ?? [];
                  $ndxDetails = $form->input('ndx_details') ?? [];
                  if (empty($tableData) || empty($ndxDetails)) {
                      return  $form->response()->error('mcc表格或NDX详情不能为空');
                  }

                  $total = 0;
                  foreach ($tableData as $value) {
                      $total = bcadd($total, $value['percentage_of_apv'], 2);
                  }

                  if ($total != 100) {
                      return $form->response()->error('累计APV百分比必须等于100%');
                  }

                  try {
                      $params = $form->input();
                      $controller = new CreditAssessmentController();
                      $controller->validateRiskAmount($params);
                      unset($params['ndx_details']['id']);
                      $form->ndx_details = $params['ndx_details'];
                  } catch (Exception $e) {
                      return $form->response()->error($e->getMessage());
                  }

                  $case = RiskCase::query()->find($form->input('risk_case_id'));
                  $update = [
                      'audit_result' => $form->input('audit_result'),

                  ];

                  $update['completion_at'] = now();
                  $update['case_status']   = RiskCase::CASE_STATUS_COMPLETE;

                  // 更新 case
                  $case->update($update);
              }
            })->saved(function (Form $form) {
                return $form->response()->success('保存成功')->refresh();
            });
        });
    }

    private function commonFormFiled(Form $form): void
    {
        $form->row(function (Form\Row $row) {
            $row->width(3)->text('company_name')->required()
                ->rules('required', [
                    'required' => admin_trans('credit-assessment.labels.validation_messages.required'),
                ]);
            $row->width(3)->text('annual_processing_volume')
                ->type('number')->required()
                ->rules('required|numeric|integer|min:1', [
                    'required' => admin_trans('credit-assessment.labels.validation_messages.required'),
                    'numeric' => admin_trans('credit-assessment.labels.validation_messages.numeric'),
                    'integer' => admin_trans('credit-assessment.labels.validation_messages.integer'),
                    'min' => admin_trans('credit-assessment.labels.validation_messages.min', ['value' => 1]),
                ]);
            $row->width(3)->text('annual_net_revenue')
                ->type('number')->required()
                ->rules('required|numeric|min:0.01|regex:/^\d+(\.\d{1,2})?$/', [
                    'required' => admin_trans('credit-assessment.labels.validation_messages.required'),
                    'numeric' => admin_trans('credit-assessment.labels.validation_messages.numeric'),
                    'min' => admin_trans('credit-assessment.labels.validation_messages.min', ['value' => 0.01]),
                    'regex' => admin_trans('credit-assessment.labels.validation_messages.regex_amount'),
                ]);
            $row->width(3)->text('average_transaction_value')
                ->type('number')->required()
                ->rules('required|numeric|min:0.01|regex:/^\d+(\.\d{1,2})?$/', [
                    'required' => admin_trans('credit-assessment.labels.validation_messages.required'),
                    'numeric' => admin_trans('credit-assessment.labels.validation_messages.numeric'),
                    'min' => admin_trans('credit-assessment.labels.validation_messages.min', ['value' => 0.01]),
                    'regex' => admin_trans('credit-assessment.labels.validation_messages.regex_amount'),
                ]);
        });
        $form->row(function (Form\Row $row) {
            $row->width(3)->text('refund_rate')
                ->type('number')->required()
                ->rules('required|numeric|min:0.01|max:100|regex:/^\d+(\.\d{1,2})?$/', [
                    'required' => admin_trans('credit-assessment.labels.validation_messages.required'),
                    'numeric' => admin_trans('credit-assessment.labels.validation_messages.numeric'),
                    'min' => admin_trans('credit-assessment.labels.validation_messages.min', ['value' => 0.01]),
                    'max' => admin_trans('credit-assessment.labels.validation_messages.max', ['value' => 100]),
                    'regex' => admin_trans('credit-assessment.labels.validation_messages.regex_amount'),
                ]);
            $row->width(3)->text('chargeback_rate')
                ->type('number')->required()
                ->rules('required|numeric|min:0.01|max:100|regex:/^\d+(\.\d{1,2})?$/', [
                    'required' => admin_trans('credit-assessment.labels.validation_messages.required'),
                    'numeric' => admin_trans('credit-assessment.labels.validation_messages.numeric'),
                    'min' => admin_trans('credit-assessment.labels.validation_messages.min', ['value' => 0.01]),
                    'max' => admin_trans('credit-assessment.labels.validation_messages.max', ['value' => 100]),
                    'regex' => admin_trans('credit-assessment.labels.validation_messages.regex_amount'),
                ]);
            $row->width(3)->text('total_share_wallet')
                ->type('number')->required()
                ->rules('required|numeric|min:0.01|max:100|regex:/^\d+(\.\d{1,2})?$/', [
                    'required' => admin_trans('credit-assessment.labels.validation_messages.required'),
                    'numeric' => admin_trans('credit-assessment.labels.validation_messages.numeric'),
                    'min' => admin_trans('credit-assessment.labels.validation_messages.min', ['value' => 0.01]),
                    'max' => admin_trans('credit-assessment.labels.validation_messages.max', ['value' => 100]),
                    'regex' => admin_trans('credit-assessment.labels.validation_messages.regex_amount'),
                ]);
            $row->width(3)->text('settlement_frequency')
                ->type('number')->required()
                ->rules('required|numeric|integer|min:1', [
                    'required' => admin_trans('credit-assessment.labels.validation_messages.required'),
                    'numeric' => admin_trans('credit-assessment.labels.validation_messages.numeric'),
                    'integer' => admin_trans('credit-assessment.labels.validation_messages.integer'),
                    'min' => admin_trans('credit-assessment.labels.validation_messages.min', ['value' => 1]),
                ]);
        });
        $form->row(function (Form\Row $row) {
            $row->width(3)->multipleSelect('mcc_id')
                ->options(function () {
                    return RiskMcc::query()
                        ->where('overall_risk_rating', '!=', 'Prohibited')
                        ->pluck('mcc', 'id');
                })->saving(function ($value) {
                    return implode(',', $value);
                })->disable();
        });
        $form->fieldset(admin_trans_label('table'), function (Form $form) {
            $form->table('mcc_table', function (Form\NestedForm $table) {
                $table->hidden('id');
                $table->text('mcc')->attribute('readonly');
                $table->textarea('mcc_description')->attribute('readonly');
                $table->select('credit_risk_rating')
                    ->options(admin_trans('credit-assessment.options.credit_risk_level'))
                    ->required();
                $table->text('delivery_days')
                    ->type('number')->required()
                    ->rules('required|numeric|integer|min:1', [
                        'required' => admin_trans('credit-assessment.labels.validation_messages.required'),
                        'numeric' => admin_trans('credit-assessment.labels.validation_messages.numeric'),
                        'integer' => admin_trans('credit-assessment.labels.validation_messages.integer'),
                        'min' => admin_trans('credit-assessment.labels.validation_messages.min', ['value' => 1]),
                    ])->width(0, 12);
                $table->text('percentage_of_apv')
                    ->type('number')->required()
                    ->help(admin_trans_label('apv_help'))
                    ->rules('required|numeric|min:0.01|max:100|regex:/^\d+(\.\d{1,2})?$/', [
                        'required' => admin_trans('credit-assessment.labels.validation_messages.required'),
                        'numeric' => admin_trans('credit-assessment.labels.validation_messages.numeric'),
                        'min' => admin_trans('credit-assessment.labels.validation_messages.min', ['value' => 0.01]),
                        'max' => admin_trans('credit-assessment.labels.validation_messages.max', ['value' => 100]),
                        'regex' => admin_trans('credit-assessment.labels.validation_messages.regex_amount'),
                    ]);
                $table->text('merchant_website')->required();
            })->saveAsJson()->width(12)->disableCreate()->disableDelete();
        });
        $form->fieldset(admin_trans_label('ndx_details'), function (Form $form) {
            $form->table('ndx_details', function (Form\NestedForm $table) {
                $table->text('title', admin_trans_field('ndx_title'))->attribute('readonly')->customFormat(function () {
                    return admin_trans('credit-assessment.options.ndx_risk_title')[$this->title] ??  $this->title;
                });
                $table->text('days')->type('number')
                    ->customFormat(function ($value, $data, $self) {
                        if (!in_array($this->title, ['chargeback_risk', 'refund_risk'])) {
                            $self->attribute('readonly');
                        }

                        if ($this->title !== 'total_risk') {
                            $self->required()->rules('required|numeric|integer|min:1', [
                                'required' => admin_trans('credit-assessment.labels.validation_messages.required'),
                                'numeric' => admin_trans('credit-assessment.labels.validation_messages.numeric'),
                                'integer' => admin_trans('credit-assessment.labels.validation_messages.integer'),
                                'min' => admin_trans('credit-assessment.labels.validation_messages.min', ['value' => 1]),
                            ]);
                        }

                        return $this->days;
                    });
                $table->text('rate')->type('number')->attribute('readonly');
                $table->text('amount', admin_trans_field('ndx_amount'))->attribute('readonly')
                    ->type('number')->required()
                    ->rules('required|numeric|min:0.01|regex:/^\d+(\.\d{1,2})?$/', [
                        'required' => admin_trans('credit-assessment.labels.validation_messages.required'),
                        'numeric' => admin_trans('credit-assessment.labels.validation_messages.numeric'),
                        'min' => admin_trans('credit-assessment.labels.validation_messages.min', ['value' => 0.01]),
                        'regex' => admin_trans('credit-assessment.labels.validation_messages.regex_amount'),
                    ]);
            })->saveAsJson()->width(12)->disableCreate()->disableDelete();
        });
        $form->fieldset(admin_trans_label('risk_reward_analysis'), function (Form $form) {
            $form->row(function (Form\Row $row) {
                $row->width(3)->text('total_exposure')->attribute('readonly')->required();
                $row->width(3)->text('exposure_coverage')
                    ->type('number')->required()
                    ->rules('required|numeric|min:0.01|max:100|regex:/^\d+(\.\d{1,2})?$/', [
                        'required' => admin_trans('credit-assessment.labels.validation_messages.required'),
                        'numeric' => admin_trans('credit-assessment.labels.validation_messages.numeric'),
                        'min' => admin_trans('credit-assessment.labels.validation_messages.min', ['value' => 0.01]),
                        'max' => admin_trans('credit-assessment.labels.validation_messages.max', ['value' => 100]),
                        'regex' => admin_trans('credit-assessment.labels.validation_messages.regex_amount'),
                    ]);
                $row->width(3)->display('annual_net_revenue_display');
                $row->width(3)->text('break_even_time')->attribute('readonly')->required();
            });
        });
        $form->fieldset(admin_trans_label('reserve_calculator'), function (Form $form) {
            $form->html('<h4>'. admin_trans_label('rolling_reserve') .'</h4>');
            $form->text('rolling_reserve_timeframe')
                ->type('number')->required()
                ->rules('required|numeric|integer|min:1', [
                    'required' => admin_trans('credit-assessment.labels.validation_messages.required'),
                    'numeric' => admin_trans('credit-assessment.labels.validation_messages.numeric'),
                    'integer' => admin_trans('credit-assessment.labels.validation_messages.integer'),
                    'min' => admin_trans('credit-assessment.labels.validation_messages.min', ['value' => 1]),
                ]);
            $form->text('rolling_reserve')->attribute('readonly')
                ->type('number')->required()
                ->rules('required|numeric|min:0.01|regex:/^\d+(\.\d{1,2})?$/', [
                    'required' => admin_trans('credit-assessment.labels.validation_messages.required'),
                    'numeric' => admin_trans('credit-assessment.labels.validation_messages.numeric'),
                    'min' => admin_trans('credit-assessment.labels.validation_messages.min', ['value' => 0.01]),
                ]);
            $form->text('daily_capture')->attribute('readonly')
                ->type('number')->required()
                ->rules('required|numeric|min:0.01|regex:/^\d+(\.\d{1,2})?$/', [
                    'numeric' => admin_trans('credit-assessment.labels.validation_messages.numeric'),
                    'min' => admin_trans('credit-assessment.labels.validation_messages.min', ['value' => 0.01]),
                    'required' => admin_trans('credit-assessment.labels.validation_messages.required'),
                    'regex' => admin_trans('credit-assessment.labels.validation_messages.regex_amount'),
                ]);
            $form->text('full_timeframe_capture')->type('number')
                ->attribute('readonly')->required();
            $form->html('<h4>'. admin_trans_label('fixed_reserve') .'</h4>');
            $form->text('reserve_currency')->attribute('readonly')->required();
            $form->text('total_fixed_reserve')->type('number')
                ->attribute('readonly')->required()
                ->rules('required|numeric|min:0.01|regex:/^\d+(\.\d{1,2})?$/', [
                    'required' => admin_trans('credit-assessment.labels.validation_messages.required'),
                    'numeric' => admin_trans('credit-assessment.labels.validation_messages.numeric'),
                    'min' => admin_trans('credit-assessment.labels.validation_messages.min', ['value' => 0.01]),
                    'regex' => admin_trans('credit-assessment.labels.validation_messages.regex_amount'),
                ]);
        });
    }

    public function processMccTable(Request $request): JsonResponse
    {
        // 获取 mcc 参数并拆分为数组
        $mccList = $request->get('mcc');
        if (!is_array($mccList)) {
            $mccList = [];
        }

        $validator = Validator::make($request->all(), [
            'mcc' => 'array',
            'mcc.*' => 'required|numeric',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'message' => $validator->errors()->first(),
            ], Response::HTTP_BAD_REQUEST);
        }

        $existMccList = $request->get('currentMcc');
        $resultList = [];
        if (!empty($existMccList) && is_array($existMccList)) {
            // 构建最新 MCC 的集合（用于快速查找）
            $newMccSet = [];
            if (!empty($mccList)) {
                $newMccSet = array_flip($mccList);
            }

            // 遍历原有列表，决定是否保留或删除
            foreach ($existMccList as $item) {
                $id = $item['id'] ?? null;

                // ✅ 规则1：如果 mcc 为 null，强制保留
                if ($id === null) {
                    $resultList[] = $item;
                    continue;
                }

                // ✅ 规则2：如果 mcc 在新列表中存在，则保留
                if (isset($newMccSet[$id])) {
                    $resultList[] = $item;
                }
                // ❌ 否则删除
            }

            // ✅ 规则3：找出需要新增的 MCC（即不在 resultList 中的）
            $existingMccInResult = array_column($resultList, 'id');
            $addList = array_values(array_diff($mccList, $existingMccInResult));
        } else {
            // 如果没有旧数据，直接新增所有
            $addList = $mccList;
        }

        // 查询新增的 MCC 数据
        $newItems = [];
        if (!empty($addList)) {
            $newList = RiskMcc::query()->whereIn('id', $addList)->get();
            foreach ($newList as $item) {
                $newItems[] = [
                    'id' => $item->id,
                    'mcc' => $item->mcc,
                    'mcc_description' => $item->description,
                    'credit_risk_rating' => $item->overall_risk_rating,
                    'delivery_days' => null,
                    'percentage_of_apv' => null,
                    'merchant_website' => '',
                ];
            }
        }

        // 合并保留的数据 + 新增的数据，保持原始顺序
        $finalList = array_merge($resultList, $newItems);

        return response()->json($finalList, Response::HTTP_OK);
    }


    /**
     * 验证风险金额是否跟前端计算一致
     * @throws Exception
     */
    public function validateRiskAmount(array &$params)
    {
        $reqUndeliveredRiskDays = 0;
        $reqUndeliveredRiskAmount = 0;
        $reqChargebackRiskDays = 0;
        $reqChargebackRiskAmount = 0;
        $reqRefundRiskDays = 0;
        $reqRefundRiskAmount = 0;
        $reqTotalRiskAmount = 0;
        $mccRiskAmountMap = [];

        foreach ($params['ndx_details'] as $key => $item) {
            $title = $item['title'];

            switch ($title) {
                case RiskCreditAssessmentModel::EXPOSURE_BREAKDOWN_UN_DELIVERY_EXPOSURE_ZH:
                case RiskCreditAssessmentModel::EXPOSURE_BREAKDOWN_UN_DELIVERY_EXPOSURE_EN:
                    $reqUndeliveredRiskDays = $item['days'];
                    $reqUndeliveredRiskAmount = $item['amount'];
                    $params['ndx_details'][$key]['title'] = RiskCreditAssessmentModel::EXPOSURE_BREAKDOWN_UN_DELIVERY_EXPOSURE;
                    break;

                case RiskCreditAssessmentModel::EXPOSURE_BREAKDOWN_CHARGEBACK_EXPOSURE_ZH:
                case RiskCreditAssessmentModel::EXPOSURE_BREAKDOWN_CHARGEBACK_EXPOSURE_EN:
                    $reqChargebackRiskDays = $item['days'];
                    $reqChargebackRiskAmount = $item['amount'];
                    $this->validateBcComp($item['rate'], $params['chargeback_rate'], 2, '拒付率不相等');
                    $params['ndx_details'][$key]['title'] = RiskCreditAssessmentModel::EXPOSURE_BREAKDOWN_CHARGEBACK_EXPOSURE;
                    break;

                case RiskCreditAssessmentModel::EXPOSURE_BREAKDOWN_REFUND_EXPOSURE_ZH:
                case RiskCreditAssessmentModel::EXPOSURE_BREAKDOWN_REFUND_EXPOSURE_EN:
                    $reqRefundRiskDays = $item['days'];
                    $reqRefundRiskAmount = $item['amount'];
                    $this->validateBcComp($item['rate'], $params['refund_rate'], 2, '退款率不相等');
                    $params['ndx_details'][$key]['title'] = RiskCreditAssessmentModel::EXPOSURE_BREAKDOWN_REFUND_EXPOSURE;
                    break;

                case  RiskCreditAssessmentModel::EXPOSURE_BREAKDOWN_TOTAL_EXPOSURE_ZH:
                case RiskCreditAssessmentModel::EXPOSURE_BREAKDOWN_TOTAL_EXPOSURE_EN:
                    $reqTotalRiskAmount = $item['amount'];
                    $params['ndx_details'][$key]['title'] = RiskCreditAssessmentModel::EXPOSURE_BREAKDOWN_TOTAL_EXPOSURE;
                    break;
                default:
                    $mccRiskAmountMap[$key] = ['amount' => $item['amount']];
            }
        }

        // 校验未交付风险天数
        $undeliveredRiskDays = 0;
        foreach ($params['mcc_table'] as $key => $item) {
            $key = $key + 1;
            $undeliveredRiskDays += $item['delivery_days'] * ($item['percentage_of_apv'] / 100);

            // 校验MCC风险金额
            $mccRiskAmount = self::truncateDecimal(
                $item['delivery_days'] / 365 * (($item['percentage_of_apv'] / 100) * $params['annual_processing_volume'])
            );
            $this->validateBcComp($mccRiskAmountMap[$key]['amount'], $mccRiskAmount, 2, 'MCC风险金额不相等');
        }

        $undeliveredRiskDays = ceil($undeliveredRiskDays);
        $this->validateBcComp($reqUndeliveredRiskDays, $undeliveredRiskDays, 0, '未交付风险日期不正确');

        // 校验未交付风险金额
        $undeliveredRiskAmount = self::truncateDecimal($undeliveredRiskDays / 365 * $params['annual_processing_volume']);
        $this->validateBcComp($reqUndeliveredRiskAmount, $undeliveredRiskAmount, 2, '未交付风险金额不正确');

        // 校验拒付风险金额
        $chargebackRiskAmount = self::truncateDecimal($params['chargeback_rate'] / 100 * $params['annual_processing_volume'] * $reqChargebackRiskDays / 365);
        $this->validateBcComp($reqChargebackRiskAmount, $chargebackRiskAmount, 2, '拒付风险金额不正确');

        // 校验退款风险金额
        $refundRiskAmount = self::truncateDecimal($params['refund_rate'] / 100 * $params['annual_processing_volume'] * $reqRefundRiskDays / 365);
        $this->validateBcComp($reqRefundRiskAmount, $refundRiskAmount, 2, '退款风险金额不正确');

        // 校验总风险金额
        $totalRiskAmount = bcadd($undeliveredRiskAmount, $chargebackRiskAmount, 2);
        $totalRiskAmount = bcadd($totalRiskAmount, $refundRiskAmount, 2);
        foreach ($mccRiskAmountMap as $item) {
            $totalRiskAmount = bcadd($totalRiskAmount, $item['amount'], 2);
        }
        $this->validateBcComp($reqTotalRiskAmount, $totalRiskAmount, 2, '总风险金额不正确');

        // 校验盈亏平衡时间
        $breakEvenTime = $this->formatBreakEvenTime($totalRiskAmount, $params['exposure_coverage'], $params['annual_net_revenue']);
        if ($breakEvenTime !== $params['break_even_time']) {
            throw new Exception('盈亏平衡时间不正确');
        }

        // 校验固定保证金
        $totalFixedReserve = self::truncateDecimal($totalRiskAmount * $params['exposure_coverage'] / 100);
        $this->validateBcComp($params['total_fixed_reserve'], $totalFixedReserve, 2, '固定保证金不正确');

        // 校验滚动保证金
        $rollingReserve = $this->calculateRollingReserve($totalRiskAmount, $params['rolling_reserve_timeframe'], $params['annual_processing_volume']);
        $this->validateBcComp($params['rolling_reserve'], $rollingReserve, 2, '滚动保证金不正确');

        // 校验每日捕获金额
        $dailyCapture = self::truncateDecimal($rollingReserve / 100 * $params['annual_processing_volume'] / 365);
        $this->validateBcComp($params['daily_capture'], $dailyCapture, 2, '每日捕获金额不正确');

        // 校验完整时间范围捕获金额
        $fullTimeframeCapture = self::truncateDecimal($dailyCapture * $params['rolling_reserve_timeframe'] * 7);
        $this->validateBcComp($params['full_timeframe_capture'], $fullTimeframeCapture, 2, '完整时间范围捕获金额不正确');
    }


    /**
     * 验证两个高精度数值是否相等（使用 bccomp），否则抛出异常
     *
     * @param string|float|int $value1
     * @param string|float|int $value2
     * @param int $scale 小数位数
     * @param string $errorMessage 错误信息
     * @return void
     * @throws Exception
     */
    private function validateBcComp($value1, $value2, int $scale = 2, string $errorMessage = '数值不相等'): void
    {
        if (bccomp((string)$value1, (string)$value2, $scale) !== 0) {
            throw new Exception($errorMessage);
        }
    }

    /**
     * 将 (总风险 - 风险覆盖率) / 年度净收入 转换为 "X yrs Y mths"
     *
     * @param string|float|int $totalRisk 总风险金额
     * @param string|float|int $coverage 风险覆盖率
     * @param string|float|int $netRevenue 年度净收入
     * @return string
     * @throws Exception
     */
    private function formatBreakEvenTime($totalRisk, $coverage, $netRevenue): string
    {
        try {
            // 防止除以零
            if (bccomp((string)$netRevenue, '0') <= 0) {
                throw new Exception('年度净收入不能为零');
            }

            // 总风险 - 风险覆盖率
            $diff = $totalRisk - ($coverage / 100);

            // 计算年数（带小数）: (总风险 - 覆盖率) / 净收入
            $years = $diff  / $netRevenue;

            // 取整数部分（向下取整）
            $fullYears = floor($years);

            // 取小数部分：年数 - 整数年
            $decimalPart = $years - $fullYears;

            // 小数部分 × 12 得到月份数
            $months = $decimalPart * 12;

            // 对月份向上取整
            $fullMonths = ceil($months);

            // 如果满 12 个月，则进位一年
            if (bccomp($fullMonths, '12', 0) >= 0) {
                $fullYears = bcadd($fullYears, '1', 0);
                $fullMonths = '0';
            }

            // 拼接结果字符串
            return "$fullYears yrs $fullMonths mths";
        } catch (Exception $e) {
            throw new Exception('盈亏平衡时间计算失败：' . $e->getMessage());
        }
    }

    /**
     * 模拟 Excel: =IFERROR(MROUND(固定保证金/(结算周数*7*年处理量/365),0.005), "-")
     *
     * @param string|float|int $fixedReserve 固定保证金
     * @param string|float|int $settlementWeeks 结算周期周数
     * @param string|float|int $annualVolume 年处理量
     * @return string
     * @throws Exception
     */
    private function calculateRollingReserve($fixedReserve, $settlementWeeks, $annualVolume): string
    {
        try {
            // 转为字符串进行计算
            $fixedReserve = (string) $fixedReserve;
            $settlementWeeks = (string) $settlementWeeks;
            $annualVolume = (string) $annualVolume;

            // 防止除以零
            if (
                bccomp($settlementWeeks, '0') <= 0 ||
                bccomp($annualVolume, '0') <= 0
            ) {
                throw new Exception('结算周期周数或年处理量不能为零');
            }

            // 分母 = 周数 * 7 * 年交易量 / 365
            $denominator = $settlementWeeks * 7;
            $denominator = $denominator * $annualVolume;
            $denominator = $denominator / 365;

            // 分子 = 固定保证金
            $numerator = $fixedReserve;

            // 计算原始值
            $rawValue = $numerator / $denominator;

            // MROUND 到最接近 0.005 的倍数：
            $rounded = round($rawValue * 200); // 这里必须用 round，因为 bcmath 不支持 ceil/floor 到任意步长
            $result = bcdiv((string)$rounded, '200', 3); // 保留三位小数

            return (string)($result * 100) ;
        } catch (Exception $e) {
            throw new Exception('计算滚动储备百分比：' . $e->getMessage());
        }
    }

    /**
     * 截断小数部分（不四舍五入）
     * @param string $value
     * @param int $scale
     * @return string
     */
    public static function truncateDecimal(string $value, int $scale = 2): string
    {
        return bcadd($value, '0', $scale);
    }
    function completeGrid($merchantId): Grid
    {
        app('admin.translator')->setPath('credit-assessment');
        $grid = Grid::make(RiskCase::with(['creditAssessment', 'riskCasesBid', 'bids']));

        $grid->model()->where('merchant_id', $merchantId)
            ->where('case_type', RiskCase::CASE_TYPE_CRA)
            ->where('case_status', RiskCase::CASE_STATUS_COMPLETE)
            ->where('audit_result', RiskCase::CASE_AUDIT_APPROVE)
            ->orderByDesc('created_at');

        $grid->column('riskCasesBid', admin_trans_field('business_id'))
            ->display(function ($value) {
                return $value[0]['business_id'] ?? '';
            });
        $grid->column('creditAssessment.mcc_id', admin_trans_field('mcc_id'))
            ->display(function ($value) {
                $mccList = RiskMcc::query()->whereIn('id', explode(',', $value))->get();

                if (empty($mccList)) {
                    return '';
                }

                $tags = $mccList->map(function ($item) {
                    return "<span class='badge badge-info'>{$item->mcc}</span>";
                });

                return implode('&nbsp;', $tags->toArray());
            });
        $grid->column('merchant_website')
            ->display(function () {
                $mccTable = json_decode($this->creditAssessment->mcc_table, true);
                if (empty($mccTable)) return '';

                $merchantWebsite = array_column($mccTable, 'merchant_website');

                $tags = array_map(function ($website) {
                    return "<span class='badge badge-pill badge-success'>{$website}</span>";
                }, $merchantWebsite);

                return implode('&nbsp;', $tags);
            })->width('10%');
        $grid->column('creditAssessment.refund_rate', admin_trans_field('refund_rate'));
        $grid->column('creditAssessment.chargeback_rate', admin_trans_field('chargeback_rate'));
        $grid->column('creditAssessment.settlement_frequency', admin_trans_field('settlement_frequency'));
        $grid->column('creditAssessment.total_exposure', admin_trans_field('total_exposure'));
        $grid->column('bids', admin_trans_field('deposit_type'))
            ->display(function ($value) {
                if (!isset($value[0]->deposit_type)) {
                    return '-';
                }

                return admin_trans( 'credit-assessment.options.deposit_type')[$value[0]->deposit_type] ?? '-';
            });
        $grid->column('creditAssessment.total_fixed_reserve', admin_trans_field('reserve_amount'))
            ->display(function ($value) {
                return $value. " ". $this->reserve_currency;
            });
        $grid->column('audit_result', admin_trans('risk-case.fields.audit_result'))
            ->display(function ($value) {
                return admin_trans('risk-case.options.audit_result')[$value] ?? '-';
            });
        $grid->column('updated_at');
        $grid->actions(function (Grid\Displayers\Actions $actions) use ($merchantId) {
            $actions->disableDelete();
            $actions->disableEdit();
            $actions->disableQuickEdit();
            $actions->disableView();

            Form::dialog(admin_trans_label('credit_risk_assessment'))
                ->click('.view-form')
                ->resetButton(false)
                ->width('80%')
                ->height('80%');

            $url = admin_url("/risk_credit_assessments/{$this->creditAssessment->id}/edit?".http_build_query([
                    'show' => true,
                ]));

            $actions->append("<span class='view-form' data-url='$url'><i class='feather icon-eye grid-action-icon'></i></span>");
        });

        $grid->disableCreateButton();
        $grid->disableRowSelector();
        $grid->disablePagination();
        $grid->disablePerPages();

        $grid->filter(function (Grid\Filter $filter) {
           $filter->where('business_id', function ($query) {
              // 查询关联数据
               $query->whereHas('riskCasesBid', function ($query) {
                   $query->where('business_id', $this->input);
               });
           });
           $filter->where('mcc_id', function ($query) {
               $query->whereHas('creditAssessment', function ($query) {
                   $query->whereRaw('FIND_IN_SET(?, mcc_id)', [$this->input]);
               });
           })->select(function () {
               return RiskMcc::query()->pluck('mcc', 'id')->toArray();
           });
           $filter->where('deposit_type', function ($query) {
               $query->whereHas('bids', function ($query) {
                   $query->where('deposit_type', $this->input);
               });
           })->select(admin_trans('credit-assessment.options.deposit_type'));
        });

        return $grid;
    }
}
