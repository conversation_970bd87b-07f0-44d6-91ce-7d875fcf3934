<?php

namespace App\Admin\Controllers\RiskControl\MerchantControl;

use App\Admin\Actions\Grid\RiskControl\Merchant\CaseSummary;
use App\Admin\Actions\Grid\RiskControl\Merchant\CreditForm;
use App\Admin\Controllers\Merchant\MerchantKycController;
use App\Admin\Controllers\RiskControl\CreditAssessment\CreditAssessmentController;
use App\Admin\Controllers\RiskControl\CreditAssessment\ProcessingSnapshotController;
use App\Admin\Controllers\RiskControl\Financial\RiskFinancialController;
use App\Admin\Controllers\RiskControl\Summary\RiskSummaryController;
use App\Admin\Repositories\Merchant;
use App\Admin\Repositories\MerchantUrl;
use App\Models\MerchantBusiness;
use App\Models\MerchantKyc;
use App\Models\Merchant as MerchantModel;
use App\Models\RiskCase;
use App\Models\RiskMcc;
use App\Models\RiskSummary;
use Dcat\Admin\Grid;
use Dcat\Admin\Http\Controllers\AdminController;
use Dcat\Admin\Layout\Content;
use Dcat\Admin\Widgets\Modal;
use Dcat\Admin\Widgets\Tab;

class MerchantController extends AdminController
{
    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid(): Grid
    {
        return Grid::make(new Merchant(['merchantKyc' => function ($query) {
            $query->where('audit_result', MerchantKyc::STATUS_UP);
            $query->orderBy('created_at', 'desc');
        }]), function (Grid $grid) {
            $grid->disableEditButton();
            $grid->disableBatchActions();
            $grid->disableCreateButton();
            $grid->disableRowSelector();

            $grid->model()->whereHas('merchantKyc', function ($query) {
                $query->where('audit_result', MerchantKyc::STATUS_UP);
                $query->whereHas('case', function ($query) {
                    $query->where('audit_result', RiskCase::CASE_AUDIT_APPROVE);
                });
            })->orderByDesc('created_at');
            $grid->column('merchant_id');
            $grid->column('merchant_name');
            $grid->column('merchantKyc.type')->using(MerchantKyc::$typeMap);
            $grid->filter(function (Grid\Filter $filter) {
                $filter->equal('merchant_id')->select(
                    MerchantModel::where('status', MerchantModel::STATUS_ENABLE)
                                 ->pluck('merchant_name', 'merchant_id')
                                 ->map(static function ($item, $key) {
                                     return $item . ':' . $key;
                                 })
                                 ->toArray()
                );
                $filter->equal('merchantKyc.type')->select(MerchantKyc::$typeMap);

            });

            $grid->actions(function (Grid\Displayers\Actions $actions) {
                $actions->disableDelete();
                $actions->append(CaseSummary::make());
                $creditForm = Modal::make()
                                   ->title(admin_trans_field('credit'))
                                   ->lg()
                                   ->body(CreditForm::make()->payload(['merchant_id' => $this->merchant_id]))
                                   ->button('<button type="button" class="btn btn-outline-primary">' . admin_trans_field('credit') . '</button>');
                $actions->append($creditForm);
            });
        });
    }

    /**
     * Make a show builder.
     *
     * @param mixed $id
     *
     * @return Tab
     */
    protected function detail($id): Tab
    {
        // 获取当前请求的 _t 参数，默认为 1
        $type = request('_t', 1);

        // 验证 _t 参数是否有效，如果无效则使用默认值 1
        if (!in_array($type, [1, 2, 3, 4, 5, 6])) {
            $type = 1;
        }

        $kyc = MerchantKyc::query()
                          ->where('merchant_id', $id)
                          ->where('audit_result', MerchantKyc::STATUS_UP)
                          ->orderBy('created_at', 'desc')
                          ->first();

        $summary = RiskSummary::query()
                              ->where('merchant_id', $id)
                              ->whereHas('riskCase', function ($query) {
                                  $query->where('case_status', RiskCase::CASE_STATUS_COMPLETE);
                              })
                              ->orderBy('created_at', 'desc')
                              ->first();

        $kycController        = new MerchantKycController();
        $riskSummary          = new RiskSummaryController();
        $riskFinancial        = new RiskFinancialController();
        $riskCreditAssessment = new CreditAssessmentController();
        $processingSnapshot   = new ProcessingSnapshotController();

        $tab = new Tab();

        // Tab 1: KYC
        if ($type == 1) {
            $tab->add(admin_trans_field('kyc'), $kycController->getDetailsTab()->edit($kyc->id), true);
        } else {
            $tab->addLink(admin_trans_field('kyc'), request()->fullUrlWithQuery(['_t' => 1]));
        }

        // Tab 2: 方案合规 (Web)
        if ($type == 2) {
            $tab->add(admin_trans_field('web'), $this->schemeCompliance($id), true);
        } else {
            $tab->addLink(admin_trans_field('web'), request()->fullUrlWithQuery(['_t' => 2]));
        }

        // Tab 3: 信用风险评估 (Credit)
        if ($type == 3) {
            $tab->add(admin_trans_field('cra'), $riskCreditAssessment->completeGrid($id), true);
        } else {
            $tab->addLink(admin_trans_field('cra'), request()->fullUrlWithQuery(['_t' => 3]));
        }

        // Tab 4: FA
        if ($type == 4) {
            $tab->add(admin_trans_field('fa'), $riskFinancial->index(Content::make()), true);
        } else {
            $tab->addLink(admin_trans_field('fa'), request()->fullUrlWithQuery(['_t' => 4, 'merchant_id' => $id]));
        }

        // Tab 5: 风险摘要 (Risk Summary)
        if ($type == 5) {
            $tab->add(admin_trans_field('risk_summary'), $riskSummary->getDetailsTab()->edit($summary->id ?? 0), true);
        } else {
            $tab->addLink(admin_trans_field('risk_summary'), request()->fullUrlWithQuery(['_t' => 5]));
        }

        // Tab 6: 交易概览（Processing Snapshot）
        if ($type == 6) {
            $tab->add(admin_trans_field('processing_snapshot'), $processingSnapshot->list($id), true);
        } else {
            $tab->addLink(admin_trans_field('processing_snapshot'), request()->fullUrlWithQuery(['_t' => 6]));
        }

        return $tab->withCard();
    }

    public function schemeCompliance($merchantId): Grid
    {
        app('admin.translator')->setPath('url');
        return Grid::make(new MerchantUrl(['case', 'mcc']), function (Grid $grid) use ($merchantId) {
            $grid->model()->where('merchant_id', $merchantId)->whereIn('pid_status', [RiskCase::CASE_AUDIT_REJECT, RiskCase::CASE_AUDIT_APPROVE])->orderBy('created_at', 'desc');
            $grid->disableEditButton();
            $grid->disableBatchActions();
            $grid->disableCreateButton();
            $grid->disableRowSelector();

            $grid->column('business_id');
            $grid->column('url_name');
            $grid->column('channel_report')->downloadable()->display(function ($value) {
                // 为支持的Office文件类型添加在线预览链接
                return add_office_preview_links($value);
            });
            $grid->column('mcc.mcc', 'MCC');
            $grid->column('pid_status')->using(admin_trans('url.options.audit'));
            $grid->column('url_status')->using(admin_trans('url.options.url_status'));
            $grid->column('case.completion_at');

            $grid->filter(function (Grid\Filter $filter) use ($merchantId) {
                $filter->panel();

                $filter->equal('business_id')->select(MerchantBusiness::where('merchant_id', $merchantId)->pluck('business_id', 'business_id')->toArray())->width(3);
                $filter->equal('url_name')->select(\App\Models\MerchantUrl::where('merchant_id', $merchantId)->pluck('url_name', 'url_name')->toArray())->width(3);
                $filter->equal('mcc.mcc', 'MCC')->select(RiskMcc::pluck('mcc', 'mcc')->toArray())->width(3);
                $filter->equal('pid_status')->select(admin_trans('url.options.audit'))->width(3);
                $filter->equal('url_status')->select(admin_trans('url.options.url_status'))->width(3);
            });

            $grid->actions(function (Grid\Displayers\Actions $actions) {
                $actions->disableDelete();
                $actions->disableEdit();
                $actions->disableQuickEdit();
                $actions->disableView();

                // 跳转至详情页面
                $actions->append("<a href=".admin_url('/merchant_urls/'.$this->id.'/edit?show=true')." target='_blank'>".admin_trans_field('detail')."</a>");
            });
        });
    }
}
