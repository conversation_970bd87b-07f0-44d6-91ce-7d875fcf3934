<?php

namespace App\Admin\Controllers\Transaction;
use App\Admin\Actions\Grid\Order\History;
use App\Admin\Actions\Tools\OrderExportTool;
use App\Admin\Repositories\Order;
use App\Admin\Repositories\PaymentOrder;
use App\Models\Channel;
use App\Models\ChannelPid;
use App\Models\ChannelSupplier;
use App\Models\DirectoryCc;
use App\Models\DirectoryChannelMainBody;
use App\Models\Merchant;
use App\Models\MerchantBusiness;
use App\Models\MerchantUrl;
use App\Models\OrderAddress;
use App\Models\OrderCard;
use App\Models\Order as OrderModel;
use App\Models\OrderProduct;
use App\Models\OrderRelation;
use Dcat\Admin\Admin;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Show;
use Dcat\Admin\Http\Controllers\AdminController;
use Dcat\Admin\Widgets\Modal;
use Illuminate\Support\Str;

class OrderController extends AdminController
{
    protected $title = '订单管理';

    protected $exportTitle = [
        'channel_suppliers',
        'channel',
        'order_id',
        'order_number',
        'type',
        'status',
        'is_refund',
        'is_chargeback',
        'merchant_id',
        'business_id',
        'merchant_name',
        'cc_type',
        'card_mask',
        'card_mask_ciphertext',
        'card_country',
        'url_name',
        'amount',
        'payment_amount',
        'amount_usd',
        'payment_amount_usd',
        'code',
        'result',
        'remark',
        'payment_code',
        'payment_result',
        'payment_remark',
        'access_type',
        'is_3d',
        'parent_order_id',
        'payment_order_number',
        'payment_order_id',
        'bill_name',
        'bill_email',
        'bill_country',
        'bill_state',
        'bill_city',
        'bill_address',
        'bill_postcode',
        'bill_phone',
        'ship_name',
        'ship_email',
        'ship_country' ,
        'ship_state',
        'ship_city',
        'ship_address',
        'ship_postcode',
        'ship_phone',
        'ip',
        'product_name',
        'product_url',
        'product_sum',
        'tracking_type',
        'tracking_number',
        'is_settle',
        'settle_at',
        'expired_at',
        'completed_at',
        'created_at',
        'updated_at'
    ];

    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        Admin::style(
            <<<css
	.modal-xxl {max-width: 100%;}
css
        );

        return Grid::make(new Order([
            'track:order_id,tracking_type,tracking_number',
            'card:id,cc_type,card_mask,card_country',
            'address:id,bill_name,bill_email,bill_country,bill_address,ship_name,ship_email,ship_country,ship_address,ip,bill_city,bill_state,bill_postcode,bill_phone,ship_city,ship_state,ship_postcode,ship_phone',
            'relation:order_id,is_refund,is_chargeback',
            'paymentOrder:order_id,order_number,payment_order_id,amount,currency,code,result,remark',
            'products:order_id,name',
            'settlements:order_id,amount_usd,payment_amount_usd,is_settle,settle_at'
        ]), function (Grid $grid) {
            if (count(request()->toArray()) <= 1) {
                request()->offsetSet('completed_at', [
                    'start' => date('Y-m-d 00:00:00', strtotime('-1 week')),
                    'end'   => date('Y-m-d H:i:s')
                ]);
            }
            $grid->model()->select('orders.*')->join('order_cards', 'orders.card_id', '=', 'order_cards.id')->orderBy('completed_at', 'desc')->orderBy('order_id', 'desc');
            $grid->showColumnSelector();
            $grid->hideColumns(['bill_name/bill_city/bill_state/bill_postcode/bill_country/bill_phone/bill_email/bill_address', 'ship_name/ship_city/ship_state/ship_postcode/ship_country/ship_phone/ship_email/ship_address']);

            $grid->column('channel');
            $grid->column('order_id/order_number/parent_order_id', '订单号/商户订单号/原始订单号')->display(function () {
                return $this->order_id . '<br/>' . $this->order_number . '<br/>' . $this->parent_order_id;
            });
            $grid->column('type')->display(function ($value) {
                return OrderModel::$typesMap[$value] ?? '未知';
            });
            $grid->column('status')->display(function ($value) {
                return OrderModel::$statusMap[$value] ?? '未知';
            })->dot(['0' => 'danger', '1' => 'success', '2' => 'primary', '3' => 'primary', '4' => 'danger']);

            $grid->column('relation.is_refund')->display(function ($value) {
                return OrderRelation::$isRefundMap[$value] ?? '未知';
            });

            $grid->column('relation.is_chargeback')->display(function ($value) {
                return OrderRelation::$isChargebackMap[$value] ?? '未知';
            });

            $grid->column('merchant_name/merchant_id/business_id', '商户名/MID/BID')->display(function () {
                return $this->merchant_name . '<br/>' . $this->merchant_id . '<br/>' . $this->business_id;
            });
            $grid->column('cc_type/card_mask/card_country/url_name', '卡种/卡号/卡国家/交易网站')->display(function () {
                return $this->card['cc_type'] . '<br/>' . $this->card['card_mask'] . '<br/>' . $this->card['card_country'] . '<br/>' . $this->url_name;
            });
            $grid->column('currency/amount', '支付金额/授权金额')->display(function () {
                return $this->amount . ' ' . $this->currency . '<br/>' . $this->paymentOrder['amount'] . ' ' . $this->paymentOrder['currency'];
            });
            $grid->column('amount_usd/payment_amount_usd', '支付USD金额/授权USD金额')->display(function () {
                return $this->settlements['amount_usd'] . '<br/>' . $this->settlements['payment_amount_usd'];
            });
            $grid->column('code/result/remark', '返回代码/返回结果/备注')->display(function () {
                return $this->code . '<br/>' . $this->result . '<br/>' . $this->remark;
            });
            $grid->column('paymentCode/paymentResult/paymentRemark', '渠道返回代码/返回结果/备注')->display(function () {
                return $this->paymentOrder['code'] . '<br/>' . $this->paymentOrder['result'] . '<br/>' . $this->paymentOrder['remark'];
            });
            $grid->column('access_type/is_3d', '接入方式/是否3d/抛投次数')->display(function () {
                $yes = $this->is_3d ? '是' : '否';
                return $this->access_type . '<br/>' . $yes . '<br/>' . $this->throw_cnt;
            });
            $grid->column('order_number/payment_order_id', '渠道订单号/渠道交易流水号')->display(function () {
                return $this->paymentOrder['order_number'] . '<br/>' . $this->paymentOrder['payment_order_id'];
            });
            $grid->column('bill_name/bill_city/bill_state/bill_postcode/bill_country/bill_phone/bill_email/bill_address', '账单人名/账单人邮箱/账单人国家 省州(地区) 城市/账单人地址/账单人邮编 电话')->display(function () {
                return $this->address['bill_name'] . '<br/>' . $this->address['bill_email'] . '<br/>' . $this->address['bill_country'] . '  ' . $this->address['ship_state'] . '  ' .  $this->address['bill_city'] . '<br/>' . $this->address['bill_address'] . '<br/>' . $this->address['ship_postcode'] . '  ' . $this->address['bill_phone'];
            });
            $grid->column('ship_name/ship_city/ship_state/ship_postcode/ship_country/ship_phone/ship_email/ship_address', '收件人名/收件人邮箱/收件人国家 省州(地区) 城市/收件人地址/收件人邮编 电话')->display(function () {
                return $this->address['ship_name'] . '<br/>' . $this->address['ship_email'] . '<br/>' . $this->address['ship_country'] . '  ' . $this->address['ship_state'] . '  ' .  $this->address['ship_city'] . '<br/>' . $this->address['ship_address'] . '<br/>' . $this->address['ship_postcode'] . '  ' . $this->address['ship_phone'];
            });
            $grid->column('ip/products', 'IP/产品名称')->display(function () {
                $products = '';
                foreach ($this->products as $key => $value) {
                    $splicer = '##';
                    if ($key == 0) {
                        $splicer = '';
                    }
                    $value['name'] = str_replace(',', ' ', $value['name']);
                    $products .= $splicer . $value['name'] ?? '';
                }
                return $this->address['ip'] . '<br/>' . Str::limit($products, 40);
            });
            $grid->column('tracking_type/tracking_number', '运单类型/运单号')->display(function () {
                return $this->track['tracking_type'] . '<br/>' . $this->track['tracking_number'];
            });
            $grid->column('is_settle', '是否结算')->display(function () {
                return $this->settlements['is_settle'] ? '是' : '否';
            });
            $grid->column('settle_at', '结算日期')->display(function () {
                return  $this->settlements['settle_at'];
            });
            $grid->column('expired_at/completed_at', '有效期/完成时间')->display(function () {
                return $this->expired_at . '<br/>' . $this->completed_at;
            });
            $grid->column('created_at/updated_at', '创建时间/更新时间')->display(function () {
                return $this->created_at . '<br/>' . $this->updated_at;
            });

            $grid->actions(function (Grid\Displayers\Actions $actions) {
                $modal = Modal::make()
                    ->size('xxl')
                    ->title('订单历史')
                    ->body(History::make()->payload(['key' => $this->order_id]))
                    ->button('订单历史');
                // prepend一个操1作
                $actions->prepend($modal . '&nbsp;');
            });

            $grid->fixColumns(1, -1);

            $grid->quickSearch(['order_id', 'order_number']);
            $grid->disableDeleteButton();
            $grid->disableCreateButton();
            $grid->disableEditButton();
            $grid->disableBatchDelete();

            $grid->tools(function (Grid\Tools $tools) use ($grid) {
                $tools->append(new OrderExportTool($this->exportTitle));
            });

			$businessId = json_encode($grid->getRequestInput('business_id') ?? []);
            $grid->filter(function (Grid\Filter $filter) use ($businessId) {

                $filter->equal('channelObj.channel_supplier_id', '渠道')->select(
                    ChannelSupplier::get()->pluck('supplier_name', 'id')->toArray()
                )->load('channelObj.channel_pid_id', '/channel_pids/get_pid');
                $filter->inTextarea('order_id')->setSeparator("\n");
                $filter->equal('paymentOrder.payment_order_id', '渠道交易流水号');
                $filter->inTextarea('order_number')->setSeparator("\n");
                $filter->in('merchant_id')->multipleSelect(
                    Merchant::where('status', Merchant::STATUS_ENABLE)
                        ->pluck('merchant_name', 'merchant_id')
                        ->map(static function ($item, $key) {
                            return $item . ':' . $key;
                        })
                        ->toArray()
                )->load('business_id', '/businesses/get_businesses/' . MerchantBusiness::INTERNAL_STATUS_ENABLE);
                $filter->in('business_id')->multipleSelect(
                    MerchantBusiness::where('internal_status', MerchantBusiness::INTERNAL_STATUS_ENABLE)->pluck('business_id', 'business_id')->toArray()
                );
				Admin::script(
					<<<JS
						// 获取mid下拉框的引用
						const midDropdown = document.getElementsByName("merchant_id[]")[0];
						// 获取bid下拉框的引用
						const bidDropdown = document.getElementsByName("business_id[]")[0];
						midDropdown.onchange = function () {
							// 判断mid是否为多选
					    if (midDropdown.selectedOptions.length >= 2) {
						  	// 禁用bid下拉框
							bidDropdown.disabled = true;
					    } else {
						  	// 启用bid下拉框
							bidDropdown.disabled = false;
					    }
					}

					 setTimeout(function () {
							$(".business_id").val({$businessId}).trigger("change");
					}, 1500);
JS
				);
                $filter->equal('url_id')->select(MerchantUrl::get()->pluck('url_name', 'id')->toArray());
                $filter->equal('type')->select(OrderModel::$typesMap);
                $filter->equal('status')->select(OrderModel::$statusMap);
                $filter->equal('is_3d', '是否3D')->select(['否', '是']);
                $filter->where('card_mask', function ($query) {
                    $cardMask = $this->input;

                    if (strlen($cardMask) == 6) {
                        $query->where('order_cards.card_mask', 'like', $cardMask .'%');
                    } else {
                        $query->where('order_cards.card_mask', $cardMask);
                    }
                }, '卡号');
                $filter->where('cc_type', function ($query) {
                    $ccType = $this->input;
                    $query->where([['order_cards.cc_type', '=', $ccType], ['completed_at', '<=', date('Y-m-d H:i:s', time())]]);
                },'卡种')->select(DirectoryCc::isRiskControl()->get()->pluck('cc_type', 'cc_type')->toArray());
                $filter->equal('address.bill_email', '持卡人邮箱');
                $filter->equal('address.bill_phone', '持卡人电话');
                $filter->equal('address.bill_name', '持卡人姓名');
                $filter->equal('address.ip', 'IP');
                $filter->equal('relation.is_refund', '是否退款')->select(OrderRelation::$isRefundMap);
                $filter->equal('relation.is_chargeback', '是否拒付')->select(OrderRelation::$isChargebackMap);
                $filter->equal('channelObj.channel_pid_id', 'PID')->select(ChannelPid::get()->pluck('channel_pid', 'id')->toArray());
				$filter->where('main_bodys_id', function ($query) {
					$channelIds     = [];
					$mainBodyId     = $this->input ?? '';
					$channelPidInfo = ChannelPid::with('Channel:id,channel_pid_id')
						->select('id', 'main_bodys_id')
						->where('main_bodys_id', $mainBodyId)
						->get()->toArray();
					foreach ($channelPidInfo as $key => $channelData) {
						foreach ($channelData['channel'] as $channelId) {
							$channelIds[] = $channelId['id'];
						}
					}
					if (count($channelIds)) {
						$query->whereIn('channel_id', $channelIds);
					} else {
						$query->where('channel_id','');
					}
				}, '主体名称')->select(DirectoryChannelMainBody::get()->pluck('full_name', 'id')->toArray());
                $filter->equal('code');
                $filter->in('channel_id')->multipleSelect(Channel::all()->pluck('channel', 'id')->toArray());
                $filter->where('throw_cnt', function ($query) {
                    if ($this->input) {
                        $query->where('throw_cnt', '>', 0);
                    } else {
                        $query->where('throw_cnt', '=', 0);
                    }
                }, '是否抛投')->select(['否', '是']);
                $filter->whereBetween('created_at', function ($query) {
                    $start = $this->input['start'] ?? '';
                    $end   = $this->input['end'] ?? '';

                    if (!empty($end)) {
                        $query->where('orders.created_at', '<=', $end);
                    }

                    if (!empty($start)) {
                        $query->where('orders.created_at', '>=', $start);
                    }
                })->datetime(['sideBySide'=>true]);
                $filter->between('completed_at')->datetime(['sideBySide'=>true]);
            });
        });
    }

    /**
     * Make a show builder.
     *
     * @param mixed $id
     *
     * @return Show
     */
    protected function detail($id)
    {
        return Show::make($id, new Order(['paymentOrder', 'card', 'address', 'products']), function (Show $show) {

            $show->html();
            $show->disableEditButton();
            $show->disableDeleteButton();

            $show->row(function (Show\Row $show) {
                $show->width(4)->order_id;
                $show->width(4)->parent_order_id;
                $show->width(4)->order_number;
                $show->width(4)->merchant_id;
                $show->width(4)->business_id;
                $show->width(4)->merchant_name;
                $show->width(4)->url_name;
                $show->width(4)->channel;
                $show->width(4)->currency;
                $show->width(4)->amount;
                $show->width(4)->type->using(\App\Models\Order::$typesMap);
                $show->width(4)->status->using(\App\Models\Order::$statusMap);
                $show->width(4)->code;
                $show->width(4)->result;
                $show->width(4)->remark;
                $show->width(4)->access_type;
                $show->width(4)->is_3d('是否3d')->using(['0' => '否', '1' => '是']);
                $show->width(4)->card_bill;
                $show->width(4)->created_at;
                $show->width(4)->updated_at;
                $show->width(4)->completed_at;
                $show->width(4)->expired_at;
                $show->width(4)->risk_type->using(\App\Models\Order::$riskTypeMap);
                $show->width(4)->risk_level->using(\App\Models\Order::$riskLevelMap);
                $show->width(4)->throw_cnt;
            });

            // 渠道信息
            $show->payment_info(function ($model) {
                return Show::make($model->paymentOrder['id'], new PaymentOrder(), function (Show $show) {
                    $show->row(function (Show\Row $show) {
                        $show->width(4)->order_number('提交给渠道的订单号');
                        $show->width(4)->payment_order_id('渠道返回的订单号');
                        $show->width(4)->currency;
                        $show->width(4)->amount;
                        $show->width(4)->type->using(\App\Models\PaymentOrder::$typesMap);
                        $show->width(4)->status->using(\App\Models\PaymentOrder::$statusMap);
                        $show->width(4)->code;
                        $show->width(4)->result;
                        $show->width(4)->remark;
                    });

                    $show->disableDeleteButton();
                    $show->disableEditButton();
                    $show->disableListButton();
                });
            });

            // 卡信息
            $show->card_info(function ($model) {
                return Show::make($model->card['id'], new OrderCard(), function (Show $show) {
                    $show->row(function (Show\Row $show) {
                        $show->width(3)->card_mask;
                        $show->width(3)->cc_type;
                        $show->width(3)->card_country;
                        $show->width(3)->card_level;
                    });

                    $show->disableDeleteButton();
                    $show->disableEditButton();
                    $show->disableListButton();
                });
            });

            // 地址信息
            $show->address_info(function ($model) {
                return Show::make($model->address['id'], new OrderAddress(), function (Show $show) {
                    $show->row(function (Show\Row $show) {
                        $show->width(4)->bill_name;
                        $show->width(4)->bill_email;
                        $show->width(4)->bill_address;
                        $show->width(4)->bill_city;
                        $show->width(4)->bill_state;
                        $show->width(4)->bill_postcode;
                        $show->width(4)->bill_country;
                        $show->width(4)->bill_country_isoa2;
                        $show->width(4)->bill_phone;
                    });

                    $show->row(function (Show\Row $show) {
                        $show->width(4)->ship_name;
                        $show->width(4)->ship_email;
                        $show->width(4)->ship_address;
                        $show->width(4)->ship_city;
                        $show->width(4)->ship_state;
                        $show->width(4)->ship_postcode;
                        $show->width(4)->ship_country;
                        $show->width(4)->ship_country_isoa2;
                        $show->width(4)->ship_phone;
                    });

                    $show->row(function (Show\Row $show) {
                        $show->width(4)->ip;
                        $show->width(4)->ip_country;
                        $show->width(4)->ip_country_isoa2;
                        $show->width(4)->ip_city;
                        $show->width(4)->ip_isp;
                        $show->width(4)->ip_postal_code;
                    });

                    $show->disableDeleteButton();
                    $show->disableEditButton();
                    $show->disableListButton();
                });
            });

            // 商户产品信息
            $show->product_info_merchant_list(function ($model) {
                $grid = new Grid(new OrderProduct());
                $grid->model()->where(['order_id' => $model->order_id, 'type' => OrderProduct::TYPE_MERCHANT]);

                $grid->name();
                $grid->qty();
                $grid->price();
                $grid->url();
                $grid->attribute();
                $grid->sku();
                $grid->is_gift();
                $grid->is_virtual();

                $grid->disableActions();
                $grid->disableCreateButton();
                $grid->disableRowSelector();
                $grid->disableRefreshButton();
                $grid->disablePagination();

                return $grid;
            });

            // 商户产品信息
            $show->product_info_bank_list(function ($model) {
                $grid = new Grid(new OrderProduct());
                $grid->model()->where(['order_id' => $model->order_id, 'type' => OrderProduct::TYPE_BANK]);

                $grid->name();
                $grid->qty();
                $grid->price();
                $grid->url();
                $grid->attribute();
                $grid->sku();
                $grid->is_gift();
                $grid->is_virtual();

                $grid->disableActions();
                $grid->disableCreateButton();
                $grid->disableRowSelector();
                $grid->disableRefreshButton();
                $grid->disablePagination();

                return $grid;
            });
        });
    }

    /**
     * Make a form builder.
     *
     * @return Form
     */
    protected function form()
    {
        return Form::make(new Order(), function (Form $form) {
            $form->display('order_id');
            $form->text('parent_order_id');
            $form->text('merchant_id');
            $form->text('business_id');
            $form->text('merchant_name');
            $form->text('url_id');
            $form->text('url_name');
            $form->text('channel_id');
            $form->text('channel');
            $form->text('order_number');
            $form->text('currency');
            $form->text('amount');
            $form->text('type');
            $form->text('status');
            $form->text('code');
            $form->text('result');
            $form->text('remark');
            $form->text('address_id');
            $form->text('card_id');
            $form->text('is_3d');

            $form->display('created_at');
            $form->display('updated_at');
        });
    }
}
