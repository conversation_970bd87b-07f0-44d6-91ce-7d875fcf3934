<?php

namespace App\Admin\Extensions\Table;

use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Concerns\WithCustomChunkSize;
use Maatwebsite\Excel\Concerns\WithCustomValueBinder;
use Maatwebsite\Excel\Concerns\WithTitle;
use Maatwe<PERSON>ite\Excel\DefaultValueBinder;
use Maatwebsite\Excel\Concerns\WithStyles;
use Maatwebsite\Excel\Concerns\FromArray;
use Maatwebsite\Excel\Concerns\WithCustomStartCell;
use Maatwebsite\Excel\Concerns\WithColumnWidths;
use Maatwebsite\Excel\Concerns\WithStrictNullComparison;
use PhpOffice\PhpSpreadsheet\Cell\Cell;
use PhpOffice\PhpSpreadsheet\Cell\DataType;
use PhpOffice\PhpSpreadsheet\Style\Border;
use PhpOffice\PhpSpreadsheet\Style\Fill;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;

class ChargebackStatChannelSheet extends DefaultValueBinder implements WithTitle, WithCustomValueBinder, WithCustomStartCell, WithCustomChunkSize, WithStrictNullComparison, FromArray, WithColumnWidths, WithStyles
{
	use Exportable;

	protected $inputs;
	protected $sites;
	protected $data;

	public function __construct(array $inputs = [], array $data = [])
	{
		$this->inputs = $inputs;
		$this->sites  = [];
		$this->data   = $data;
	}

	public function title(): string
	{
		return '渠道拒付情况统计表';
	}

	public function chunkSize(): int
	{
		return 500;
	}

	public function bindValue(Cell $cell, $value)
	{
		$cell->setValueExplicit($value, DataType::TYPE_STRING);

		return true;
	}

	// 导出数组
	public function array(): array
	{
		// 筛选参数
		$startMonth = $this->inputs['date_stat.start'];
		$endMonth   = $this->inputs['date_stat.end'];
		// 表格内容标题
		$title[] = [
			'', '', '', '各渠道' . $startMonth . '至' . $endMonth . '拒付情况统计',
		];
		// 表格数据展示字段列
		$mainHead[] = [
			'渠道', '主体', 'PID', '卡种', '成交笔数', '拒付笔数', '拒付率', '成交金额(USD)', '欺诈金额(USD)', '欺诈类拒付率'
		];

		// 表格数据
		$mergeData   = $this->data;
		$this->sites = $mergeData;
		$finallyData = array_merge(
			$title,
			$mainHead,
			$mergeData
		);

		return $finallyData;
	}


	// 从哪一行开始
	public function startCell(): string
	{
		return 'A1';
	}

	// 设置每一列的宽度
	public function columnWidths(): array
	{
		return [
			'A' => 20,
			'B' => 60,
			'C' => 30,
			'D' => 20,
			'E' => 20,
			'F' => 20,
			'G' => 20,
			'H' => 20,
			'I' => 20,
			'J' => 20,
		];
	}

	// 设置excel样式
	public function styles(Worksheet $sheet)
	{
		$tableData = $this->sites;
		// 从第三行开始
		$row = 3;
		// 样式  Border::BORDER_HAIR 中粗边框 / 纯黑色RGB
		$borderStyle = ['borderStyle' => Border::BORDER_MEDIUM, 'color' => ['rgb' => '000000']];
		// 居中
		$alignmentStyle = ['horizontal' => 'center', 'vertical' => 'center'];
		$frameStyle     = ['borders' => ['bottom' => $borderStyle, 'left' => $borderStyle, 'top' => $borderStyle, 'right' => $borderStyle], 'alignment' => $alignmentStyle];
		// 表头应用样式
		$sheet->getStyle('A2:J2')->applyFromArray($frameStyle);
		$startColumn      = 3;
		$nextStartColumn  = '';
		$mergeChannelName = [];
		$prevMainBody     = '';
		$prevChannelPid   = '';
		// 循环按顺序插入数据
		foreach ($tableData as $channel => $itemInfo) {
			$countCardType              = count($itemInfo) > 1 ? count($itemInfo) : 1;
			$startColumn                = empty($nextStartColumn) ? $startColumn : $nextStartColumn;
			$endColumn                  = $startColumn + $countCardType - 1;
			$nextStartColumn            = $endColumn + 1;
			$mergeChannelName[$channel] = [
				'startColumn' => $startColumn,
				'endColumn'   => $endColumn,
			];
			asort($itemInfo);
			foreach ($itemInfo as $item) {
				$sheet->setCellValue('A' . $startColumn, $item['channel_name']);
				$sheet->setCellValue('B' . $row, $item['main_body_name']);
				$sheet->setCellValue('C' . $row, $item['channel_pid']);
				$sheet->setCellValue('D' . $row, $item['cc_type']);
				$sheet->setCellValue('E' . $row, $item['transaction_qty']);
				$sheet->setCellValue('F' . $row, $item['dishonour_qty']);
				$sheet->setCellValue('G' . $row, $item['dishonour_qty_per']);
				$sheet->setCellValue('H' . $row, $item['amount_usd']);
				$sheet->setCellValue('I' . $row, $item['fraud_amount_usd']);
				$sheet->setCellValue('J' . $row, $item['fraud_qty_per']);

				// 如果与上一行的"main_body_name"和"channel_pid"相同，进行合并
				if ($item['main_body_name'] === $prevMainBody) {
					$mergeRange = 'B' . ($row - 1) . ':B' . $row;
					$sheet->mergeCells($mergeRange);
				}

				if ($item['channel_pid'] === $prevChannelPid) {
					$mergeRange = 'C' . ($row - 1) . ':C' . $row;
					$sheet->mergeCells($mergeRange);
				}

				// 获取 "拒付率" 列的值
				$dishonourQtyPer = $item['dishonour_qty_per'];

				// 将字符串转换为百分比并去除百分号
				$dishonourQtyPer = str_replace('%', '', $dishonourQtyPer);
				$dishonourQtyPer = floatval($dishonourQtyPer);

				if ($dishonourQtyPer > 0.9) {
					// 设置颜色
					$sheet->getStyle('D' . $row . ':J' . $row)->getFill()->setFillType(Fill::FILL_SOLID);
					$sheet->getStyle('D' . $row . ':J' . $row)->getFill()->getStartColor()->setARGB('FFC000');
				}

				// 给单元格范围应用样式
				$range = 'A' . $row . ':J' . $row;
				$sheet->getStyle($range)->applyFromArray($frameStyle);
				// 更新行计数器
				$row++;
				// 更新上一行的"main_body_name"和"channel_pid"的值
				$prevMainBody   = $item['main_body_name'];
				$prevChannelPid = $item['channel_pid'];
			}
		}
		foreach ($mergeChannelName as $channelName => $mergeChannel) {
			$mergeChannelName = 'A' . $mergeChannel['startColumn'] . ':A' . $mergeChannel['endColumn'];
			$sheet->mergeCells($mergeChannelName);
			$sheet->setCellValue('A' . $mergeChannel['startColumn'], $channelName);
		}
		// 不懂为什么会多出一列, 没搞懂是哪里多的,表格是定死的,所以暂时选择直接移除
		$sheet->removeColumn("K");
	}

}
