<?php

namespace App\Admin\Metrics\Dashboard;

use Dcat\Admin\Support\Helper;
use Dcat\Admin\Widgets\Box;
use Dcat\Admin\Traits\InteractsWithApi;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use App\Models\StatOrderChargeback;

class RiskChartBox extends Box
{

    use InteractsWithApi;

    protected $content;

    protected $menus;
    protected $merUrl;
    protected $bid;
    protected $ccs;
    protected $curr;
    protected $mcc;
    protected $country;
    protected $value = [];

    protected $option;
    protected $chartName = [];

    public static $js = [
        '@apex-charts',
    ];

    public function __construct($menus = [])
    {
        $this->menus = $menus;
        parent::__construct();
    }

    public function handle(Request $request)
    {
        $merchantId = Auth::user()->merchant_id;
        $menus      = $request->get('riskmenus', []);
        $value      = [];
        $data       = [];

        $monthType = $request->get('mouthTypeContent', admin_trans_field('当月'));
        if ($monthType == admin_trans_field('当月')) {
            $data['monthType'] = 0;
        } else {
            $data['monthType'] = 1;
        }

        if ($request->get('mouthContent', -1) > 0) {
            $value['month'] = $request->get('mouthContent') ?? 0;
        }

        $data['month'] = $value['month'] ?? 6;

        if (count($menus) > 0) {
            if ($request->get('bidContent', -1) >= 0) {
                $data['bid'] = $request->get('bidContent');
            }

            if ($request->get('urlContent', -1) >= 0) {
                $value['url'] = $request->get('urlContent');
                if (isset($menus['url'])) {
                    $url         = array_flip($menus['url']);
                    $data['url'] = $url[$value['url']] ?? 0;
                }
            }

            if ($request->get('mccContent', -1) >= 0) {
                $value['mcc'] = $request->get('mccContent');
                if (isset($menus['mcc'])) {
                    $mcc         = array_flip($menus['mcc']);
                    $data['mcc'] = $mcc[$value['mcc']] ?? 0;
                }
            }

            if ($request->get('currContent', -1) >= 0) {
                $data['curr'] = $request->get('currContent');
            }

            if ($request->get('ccsContent', -1) >= 0) {
                $data['ccs'] = $request->get('ccsContent');
            }

            if ($request->get('countryContent', -1) >= 0) {
                $data['country'] = $request->get('countryContent');
            }
        }

        $this->setData($merchantId, $data);
    }

    public function renderContent(): string
    {
        return Helper::render($this->content);
    }

    function parameters(): array
    {
        return [
            'riskmenus' => $this->menus,
        ];
    }

    public function setData($merchantId, $data)
    {
        $monthType = $data['monthType'] ?? 0;
        $m         = date('Ym', strtotime(date('Y-m-d') . '-' . $data['month'] . 'month'));
        $bid       = $data['bid'] ?? '';
        $url       = $data['url'] ?? '';
        $mcc       = $data['mcc'] ?? '';
        $curr      = $data['curr'] ?? '';
        $cctype    = $data['ccs'] ?? '';
        $country   = $data['country'] ?? '';

        $where = [
            ['merchant_id', '=', (string)$merchantId],
            ['date_stat_month', '>', $m]
        ];

        if ($bid != 'business_id' && $bid != admin_trans_field('全部')) {
            $where[] = ['business_id', '=', (string)$bid];
        }

        if ($url != 'url_name' && $url != 0) {
            $where[] = ['url_id', '=', $url];
        }

        if ($mcc != 'MCC' && $mcc != 0) {
            $where[] = ['d_mcc_id', '=', $mcc];
        }

        if ($curr != 'currency' && $curr != admin_trans_field('全部')) {
            $where[] = ['currency', '=', $curr];
        }

        if ($cctype != 'cc_type' && $cctype != admin_trans_field('全部')) {
            $where[] = ['cc_type', '=', $cctype];
        }

        if ($country != 'card_country' && $country != admin_trans_field('全部')) {
            $where[] = ['card_country', '=', $country];
        }

        $selectArr = [
            'date_stat_month',
            DB::raw('SUM(transaction_qty) as transaction_qty'),
            DB::raw('SUM(3d_transaction_amount_usd) as 3d_transaction_amount_usd'),
        ];

        if ($monthType == 0) {
            $colors = '#506CFF';
            $selectArr[] = DB::raw('SUM(dishonour_qty) as dishonour_qty');
            $selectArr[] = DB::raw('SUM(refund_qty) as refund_qty');
            $selectArr[] = DB::raw('SUM(fraud_qty) as fraud_qty');
        } else {
            $colors = '#00c66b';
            $selectArr[] = DB::raw('SUM(dishonour_qty1) as dishonour_qty');
            $selectArr[] = DB::raw('SUM(refund_qty1) as refund_qty');
            $selectArr[] = DB::raw('SUM(fraud_qty1) as fraud_qty');
        }

        $orderChargebacks = StatOrderChargeback::select($selectArr)
            ->where($where)
            ->groupBy('date_stat_month')
            ->orderBy('date_stat_month', 'asc')
            ->get()->toArray();

        $result = ['dishonour_qty_percent' => [], 'fraud_qty_percent' => [], 'refund_percent' => [], 'dishonour_percent' => []];
        foreach ($orderChargebacks as $orderChargeback) {
            $transactionQty                     = $orderChargeback['transaction_qty'];
            $result['dishonour_qty_percent'][]  = $this->divisionRisk($orderChargeback['dishonour_qty'], $transactionQty);
            $result['fraud_qty_percent'][]      = $this->divisionRisk($orderChargeback['fraud_qty'], $transactionQty);
            $result['refund_percent'][]         = $this->divisionRisk($orderChargeback['refund_qty'], $transactionQty);
            $result['dishonour_percent'][]      = $orderChargeback['3d_transaction_amount_usd'];
        }

        $labels = array_column($orderChargebacks, 'date_stat_month', '');

        $this->makeArea(admin_trans_field('拒付率') . '(%)', $labels, $result['dishonour_qty_percent'], admin_trans_field('拒付率') . '(%)', 'chargeBackRatio', $colors);
        $this->makeArea(admin_trans_field('欺诈率') . '(%)', $labels, $result['fraud_qty_percent'], admin_trans_field('欺诈率') . '(%)', 'monthCheatRatio', $colors);
        $this->makeArea(admin_trans_field('退款率') . '(%)', $labels, $result['refund_percent'], admin_trans_field('退款率') . '(%)', 'refundRatio', $colors);
        $this->makeArea(admin_trans_field('3d交易金额') . '($)', $labels, $result['dishonour_percent'], admin_trans_field('3d交易金额') . '($)', 'transactionAmount', $colors);

        $this->content =
            <<<HTML
            <div class="col-sm-12 justify-content-center">
                <div class="col-md-3" style = "height:auto;background: #FFFFFF;padding: 1.3em 1em 1em 0;float:left">
                    <div id = "chargeBackRatio" style="border: 1px solid #D9D9D9;border-radius: 8px;"></div>
                </div>
                <div class="col-md-3" style = "height:auto;background: #FFFFFF;padding: 1.3em 1em 1em 0;float:left">
                    <div id = "monthCheatRatio" style="border: 1px solid #D9D9D9;border-radius: 8px;"></div>
                </div>
                <div class="col-md-3" style = "height:auto;background: #FFFFFF;padding: 1.3em 1em 1em 0;float:left">
                    <div id = "refundRatio" style="border: 1px solid #D9D9D9;border-radius: 8px;"></div>
                </div>
                <div class="col-md-3" style = "height:auto;background: #FFFFFF;padding: 1.3em 1em 1em 0;float:left">
                    <div id = "transactionAmount" style="border: 1px solid #D9D9D9;border-radius: 8px;"></div>
                </div>
            </div>
            HTML;
    }

    private function makeArea($title, $xData, $yData, $yName, $id, $colors = '#506CFF')
    {
        $options = [
            'chart' => [
                'height'  => 280,
                'type'    => 'area',
                'toolbar' => [
                    'show' => false,
                ],
            ],
            'dataLabels' => [
                'enabled' => false
            ],
            'colors' => [
                $colors
            ],
            'series' => [
                [
                    'name' => $yName,
                    'data' => $yData,
                ]
            ],
            'fill' => [
                'type'     => 'gradient',
                'gradient' => [
                    'shadeIntensity' => 1,
                    'opacityFrom'    => 0.5,
                    'opacityTo'      => 0.7,
                    'stops'          => [0, 90, 100]
                ]
            ],
            'markers' => [
                'size'            => 3,
                'strokeColor'     => '#ffffff',
                'strokeWidth'     => 2,
                'strokeOpacity'   => 0.9,
                'strokeDashArray' => 0,
            ],
            'stroke' => [
                'width' => 1,
            ],
            'xaxis' => [
                'categories' => $xData,
                'labels'     => [
                    'show' => true,
                ],
            ],
            'yaxis'      => [
                'labels' => [
                    'show' => true,
                ],
            ],
            'title'      => [
                'text'   => $title,
                'align'  => 'left',
                'margin' => 10,
            ],
            'dataLabels' => [
                'enabled' => false
            ],
            'legend'     => [
                'show' => false,
            ],
        ];
        $this->option[$id]['options'] = $options;
    }

    public function render()
    {
        $this->script               = $this->buildRequestScript();
        $this->variables['content'] = $this->renderContent();
        return parent::render();
    }

    public function valueResult()
    {
        return [
            'status'  => 1,
            'content' => $this->content,
            'option'  => $this->option,
        ];
    }

    public function divisionRisk($qty, $transactionQty)
    {
        if (!$qty) {
            return 0;
        }

        if (!$transactionQty) {
            return 100;
        }

        return round($qty * 100 / $transactionQty, 4);
    }

    public function buildRequestScript()
    {
        if (!$this->allowBuildRequest()) {
            return;
        }

        $fetching = implode(';', $this->requestScripts['fetching']);
        $fetched  = implode(';', $this->requestScripts['fetched']);

        return <<<JS
(function () {
    var loading;
    function request(data) {
        if (loading) {
            return;
        }
        loading = 1;

        data = $.extend({$this->formatRequestData()}, data || {});

        data.mouthContent = $("#mouth")[0].text.trim().slice(1).slice(0, -2);
        data.mouthTypeContent = $("#mouthType")[0].text.trim();
        data.bidContent = $("#rickBid")[0].text.trim();
        data.urlContent = $("#rickUrl")[0].text.trim();
        data.mccContent = $("#rickMcc")[0].text.trim();
        data.countryContent = $("#rickCountry")[0].text.trim();
        data.ccsContent = $("#rickCcs")[0].text.trim();
        data.currContent = $("#rickCurr")[0].text.trim();

        {$fetching};

        $.ajax({
          url: '{$this->getRequestUrl()}',
          dataType: 'json',
          method: '{$this->method}',
          data: data,
          success: function (response) {
            loading = 0;
            {$fetched};
            if (response.status == 1) {
                document.getElementById("risk-chart-box").innerHTML = response.content;
                var chargeBackRatioOption = response.option['chargeBackRatio']['options'];
                var chargeBackRatio = $('#chargeBackRatio');
                var chargeBackChart = new ApexCharts(chargeBackRatio[0], chargeBackRatioOption);
                chargeBackChart.render();
                var monthCheatRatioOption = response.option['monthCheatRatio']['options'];
                var monthCheatRatio = $('#monthCheatRatio');
                var monthCheatRatioChart = new ApexCharts(monthCheatRatio[0], monthCheatRatioOption);
                monthCheatRatioChart.render();
                var refundRatioOption = response.option['refundRatio']['options'];
                var refundRatio = $('#refundRatio');
                var refundRatioChart = new ApexCharts(refundRatio[0], refundRatioOption);
                refundRatioChart.render();
                var transactionAmountOption = response.option['transactionAmount']['options'];
                var transactionAmount = $('#transactionAmount');
                var transactionAmountChart = new ApexCharts(transactionAmount[0], transactionAmountOption);
                transactionAmountChart.render();
            }
          }
        });
    }

    request();

    {$this->buildBindingScript()}
})();
JS;
    }
}
