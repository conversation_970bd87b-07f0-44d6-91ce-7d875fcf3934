<?php

namespace App\Classes\Pay\Gateways\Booty;

use App\Classes\Supports\Collection;
use App\Classes\Pay\Events;
use App\Classes\Pay\Parser\Booty;

class RetrieveGateway extends Gateway
{
    /**
     * Pay an order.
     *
     * @param string $endpoint
     *
     * @param array $payload
     * @return Collection
     */
    public function pay($endpoint, array $payload): Collection
    {
        $payConfig = $payload['pay_config'];
        
        // 组装请求头
        $header = array(
            'X-USER-ACCESS-TOKEN' => $payConfig['access_token'],
            'Content-Type'        => 'application/json; charset=UTF-8',
        );

        $post = array(
            'data'   => '',
            'header' => $header,
        );

        return Booty::retrieveParser(Support::requestApi('/gateway/v3/stores/' . $payConfig['store_id'] . '/query/transactions/' . $payload['payment_order']['payment_order_id'], $post, 'get', $payload['order_id']));
    }
}
