<?php

namespace App\Classes\Pay\Gateways\Checkout;

use App\Classes\Pay\Gateways\Checkout;
use App\Classes\Supports\Config;
use App\Classes\Pay\Contracts\Support as BaseSupport;
use App\Classes\Pay\Events;
use App\Classes\Supports\Collection;

/**
 * @property array http  http options
 * @property string mode current mode
 * @property array log   log options
 * @property string sign_key
 */
class Support
{
    use BaseSupport;

    /**
     * Instance.
     *
     * @var Support
     */
    private static $instance;

    /**
     * Bootstrap.
     *
     * @param Config $config
     */
    public function __construct(Config $config)
    {
        self::$gatewayName = 'Checkout';
        $this->baseUri     = Checkout::URL[$config->get('mode', Checkout::MODE_NORMAL)];
        $this->config      = $config;
        $this->setHttpOptions();
    }

    /**
     * Get API result.
     *
     * @param string $endpoint
     * @param array $data
     * @param string $requestId
     * @param array $customize
     * @return Collection
     */
    public static function requestApi(string $endpoint, array $data, string $method, string $requestId = ''): Collection
    {
        $logData = Support::handleLogData($data);

        Events::dispatch(new Events\ApiRequesting(self::$gatewayName, '', self::$instance->getBaseUri() . $endpoint, $logData));
        Events::dispatch(new Events\SlsLog(
            self::$gatewayName, 
            'Requesting To Api',
            [self::$instance->getBaseUri() . $endpoint, $logData], 
            $requestId,
            'info'
        ));

        $result = [];

        try {
            $result = self::$instance->$method($endpoint, [], $data);
        } catch (\Exception $e) {
            //返回数据格式特殊处理
            $content = $e->getMessage();
            if ($e instanceof \GuzzleHttp\Exception\RequestException) {
                $content = $e->getResponse()->getBody()->getContents();
            }

            $result = self::returnDateHandle($content, $requestId);
        }

        Events::dispatch(new Events\ApiRequested(self::$gatewayName, '', self::$instance->getBaseUri() . $endpoint, $result));
        Events::dispatch(new Events\SlsLog(
            self::$gatewayName, 
            'Result Of Api',
            $result, 
            $requestId
        ));

        return self::processingApiResult($result);
    }

    /**
     * 处理日志数据
     *
     * @param array $data
     * @return array
     */
    public static function handleLogData($data)
    {
        $card = $data['data']['source'] ?? [];

        if (isset($card['number'], $card['expiry_month'], $card['expiry_year'], $card['cvv'])) {
            $card                 = &$data['data']['source'];
            $card['expiry_month'] = get_mark_data($card['expiry_month']);
            $card['expiry_year']  = get_mark_data($card['expiry_year']);
            $card['cvv']          = get_mark_data($card['cvv']);
            $card['number']       = get_markcard($card['number']);
        }

        return $data;
    }
    
    /**
     * 金额格式处理
     *
     * @param $amount
     * @param $currency
     * @return string
    */
    public static function amountHandle($amount, $currency): string
    {
        // 要特殊处理货币
        $currencyData = [
            'BIF' => 1,
            'CLF' => 1,
            'DJF' => 1,
            'GNF' => 1,
            'ISK' => 1,
            'JPY' => 1,
            'KMF' => 1,
            'KRW' => 1,
            'PYG' => 1,
            'RWF' => 1,
            'UGX' => 1,
            'VUV' => 1,
            'VND' => 1,
            'XAF' => 1,
            'XOF' => 1,
            'XPF' => 1,
            
            'BHD' => 1000,
            'IQD' => 1000,
            'JOD' => 1000,
            'KWD' => 1000,
            'LYD' => 1000,
            'OMR' => 1000,
            'TND' => 1000
        ];

        $amount = isset($currencyData[$currency]) ? $amount * $currencyData[$currency] : $amount * 100;
        return self::amount_format($amount, 0);
    }
}
