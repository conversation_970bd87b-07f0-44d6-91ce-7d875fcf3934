<?php

namespace App\Classes\Pay\Gateways\Dlp;

use App\Classes\Pay\Gateways\Dlp;
use App\Classes\Supports\Collection;
use App\Classes\Supports\Config;
use App\Classes\Pay\Contracts\Support as BaseSupport;
use App\Classes\Pay\Events;


/**
 * @property array http  http options
 * @property string mode current mode
 * @property array log   log options
 * @property string sign_key
 */
class Support
{
	use BaseSupport;

	/**
	 * Instance.
	 *
	 * @var Support
	 */
	private static $instance;

	/**
	 * Bootstrap.
	 *
	 * @param Config $config
	 */
	public function __construct(Config $config)
	{
		self::$gatewayName = 'Dlp';
		$this->baseUri     = Dlp::URL[$config->get('mode', Dlp::MODE_NORMAL)];
		$this->config      = $config;
		$this->setHttpOptions();
	}

	/**
	 * Get API result.
	 *
	 * @param array $data
	 * @param string $endpoint
	 * @param string $type
	 * @param string $method
	 * @param string $requestId
	 * @return Collection
	 */
	public static function requestApi(string $endpoint, array $data, $type = '', $method = 'post', string $requestId = '' ): Collection
	{
		Events::dispatch(new Events\ApiRequesting(self::$gatewayName, '', self::$instance->getBaseUri() . $endpoint, $data));
		Events::dispatch(new Events\SlsLog(
            self::$gatewayName, 
            'Requesting To Api',
            [self::$instance->getBaseUri() . $endpoint, $data], 
            $requestId
        ));

		try {
			switch ($method) {
				case 'get':
					$result = self::$instance->get($endpoint, $data['data'], $data['header']);
				break;
				default:
					switch ($type) {
						case 'json':
							$result = self::$instance->post($endpoint, [], ['json' => $data['data'], 'headers' => $data['header']]);
						break;
						default:
							$result = self::$instance->post($endpoint, $data);
					}
			}
		} catch (\Exception $e) {
			// 记录日志
			logger()->channel('intercept')->warning('Dlp-Support-requestApi', ['data' => $data['data'] ?? [], 'error' => $e->getMessage()]);
			Events::dispatch(new Events\SlsLog(
                self::$gatewayName, 
                'Dlp-Support-requestApi',
                ['data' => $data['data'] ?? [], 'error' => $e->getMessage()], 
                $requestId,
				'warning'
            ));

			// 渠道错误返回不规范
			$responseData = explode("\n", $e->getMessage());
			$str          = substr($responseData[1], 0, strripos($responseData[1], ','));
			$tmpRes       = json_decode($responseData[1], true) ?? json_decode($str . '}', true);
			$result       = $tmpRes ?? [];
		}

		Events::dispatch(new Events\ApiRequested(self::$gatewayName, '', self::$instance->getBaseUri() . $endpoint, $result));
		Events::dispatch(new Events\SlsLog(
            self::$gatewayName, 
            'Result Of Api',
            $result, 
            $requestId
        ));

		return self::processingApiResult($result);
	}

	/**
	 * 签名
	 *
	 * @param $data
	 * @param $privateKey
	 * @return string
	 */
	public static function sign(&$data, $privateKey)
	{
		return self::genSign(self::genSignContent($data), $privateKey);
	}

	/**
	 * 验签
	 *
	 * @param $data
	 * @param $sign
	 * @param $pubKey
	 * @return bool
	 */
	public static function verifySign(&$data, $sign, $pubKey)
	{
		return self::verifyGenSign(self::genSignContent($data), $sign, $pubKey);
	}

	/**
	 * 生成签名内容
	 *
	 * @param $req
	 * @return string
	 */
	private static function genSignContent(&$req)
	{
		$arr  = array($req);
		$strs = array();

		ksort($arr);
		self::items(0, $arr, $strs);
		$msg = implode('&', $strs);

		return $msg;
	}

	/**
	 * 递归深度优先排序
	 *
	 * @param $x
	 * @param $y
	 * @param $strs
	 */
	private static function items($x, $y, &$strs)
	{
		if ($y == null) {
			return;
		}

		if (is_array($y)) {
			ksort($y);
			foreach ($y as $key => $value) {
				self::items($key, $value, $strs);
			}

			return;
		}

		$strs[] = $x . '=' . $y;
	}

	/**
	 * 生成签名
	 *
	 * @param $toSign
	 * @param $privateKey
	 * @return string
	 */
	public static function genSign($toSign, $privateKey)
	{
		//这里他是拼接成和pem文件一样的格式
		$privateKey = "-----BEGIN RSA PRIVATE KEY-----\n" .
			wordwrap($privateKey, 64, "\n", true) .
			"\n-----END RSA PRIVATE KEY-----";

		$key = openssl_get_privatekey($privateKey);
		openssl_sign($toSign, $signature, $key);
		openssl_free_key($key);
		$sign = base64_encode($signature);

		return $sign;
	}

	/**
	 * 验证签名
	 *
	 * @param $data
	 * @param $sign
	 * @param $pubKey
	 * @return bool
	 */
	public static function verifyGenSign($data, $sign, $pubKey)
	{
		$sign = base64_decode($sign);

		$pubKey = "-----BEGIN PUBLIC KEY-----\n" .
			wordwrap($pubKey, 64, "\n", true) .
			"\n-----END PUBLIC KEY-----";

		$key    = openssl_pkey_get_public($pubKey);
		$result = openssl_verify($data, $sign, $key, OPENSSL_ALGO_SHA1) === 1;

		return $result;
	}
}
