<?php

namespace App\Classes\Pay\Parser;


use App\Classes\Supports\Collection;
use App\Models\Order;
use App\Models\Refund;
use Illuminate\Support\Str;

class Authorize extends BaseParser
{
    protected static $_supplierName = 'Authorize';

    public static function _authParser($params): Collection
    {
        $status = Order::STATUS_PENDING;

        $respCode = $params['transactionResponse']['messages'][0]['code'] ?? $params['transactionResponse']['errors'][0]['errorCode'];
        $respMsg  = $params['transactionResponse']['messages'][0]['description'] ?? $params['transactionResponse']['errors'][0]['errorText'];

        if ($params['transactionResponse']['responseCode'] == '1') {
            $status = Order::STATUS_APPROVED;
        }

        if (in_array($params['transactionResponse']['responseCode'], ['2', '3']) || $params['messages']['resultCode'] == 'Error') {
            $status = Order::STATUS_DECLINED;
        }

        $paymentOrderCollection = new Collection();
        $paymentOrderCollection->set('payment_order_id', $params['transactionResponse']['transId'] ?? '0');
        $paymentOrderCollection->set('code', $respCode ?? $params['messages']['message'][0]['code'] ?? '');
        $paymentOrderCollection->set('result', $respMsg ?? Str::limit($params['messages']['message'][0]['text'], 50) ?? '');
        $paymentOrderCollection->set('type', Order::TYPES_SALE);
        $paymentOrderCollection->set('remark', $params['transactionResponse']['accountNumber'] ?? '');
        $paymentOrderCollection->set('html', '');
        $paymentOrderCollection->set('status', $status);

        $code = self::$_channelExternalCode[$paymentOrderCollection->code] ?? get_system_code('099');

        if ($status == Order::STATUS_APPROVED) {
            $code = get_system_code('000');
        } elseif ($status == Order::STATUS_PENDING && $params['transactionResponse']['responseCode'] == '4') {
            $code = get_system_code('210');
        }

        $orderCollection = new Collection();
        $orderCollection->set('type', Order::TYPES_SALE);
        $orderCollection->set('status', self::$_respCode[$code][0]);
        $orderCollection->set('code', $code);
        $orderCollection->set('result', self::$_respCode[$code][1]);
        $orderCollection->set('remark', self::$_respCode[$code][2]);

        $collection = new Collection();
        $collection->set('order', $orderCollection->toArray());
        $collection->set('payment_order', $paymentOrderCollection->toArray());

        return $collection;
    }

    public static function captureParser($params): Collection
    {
        return self::authParser($params);
    }

    public static function _refundParser($params): Collection
    {
        $status = Order::STATUS_PENDING;

        $respCode = $params['transactionResponse']['messages'][0]['code'] ?? $params['transactionResponse']['errors'][0]['errorCode'];
        $respMsg  = $params['transactionResponse']['messages'][0]['description'] ?? $params['transactionResponse']['errors'][0]['errorText'];

        if ($params['transactionResponse']['responseCode'] == '1') {
            $status = Order::STATUS_APPROVED;
        }

        if (in_array($params['transactionResponse']['responseCode'], ['2', '3']) || $params['messages']['resultCode'] == 'Error') {
            $status = Order::STATUS_DECLINED;
        }

        $paymentRefundCollection = new Collection();
        $paymentRefundCollection->set('payment_refund_id', $params['transactionResponse']['transId'] ?? '0');
        $paymentRefundCollection->set('code', $respCode);
        $paymentRefundCollection->set('result', $respMsg);
        $paymentRefundCollection->set('remark', '');
        $paymentRefundCollection->set('status', $status);

        $code = self::$_channelExternalCode[$paymentRefundCollection->code] ?? get_system_code('099');

        if ($status == Refund::STATUS_APPROVED) {
            $code = get_system_code('000');
        } elseif ($status == Refund::STATUS_PENDING && $params['transactionResponse']['responseCode'] == '4') {
            $code = get_system_code('210');
        }

        $refundCollection = new Collection();
        $refundCollection->set('status', self::$_respCode[$code][0]);
        $refundCollection->set('code', $code);
        $refundCollection->set('result', self::$_respCode[$code][1]);
        $refundCollection->set('remark', self::$_respCode[$code][2]);

        $collection = new Collection();
        $collection->set('refund', $refundCollection->toArray());
        $collection->set('payment_refund', $paymentRefundCollection->toArray());

        return $collection;
    }

    public static function _retrieveParser($params): Collection
    {
        $status = Order::STATUS_PENDING;

        $respCode = $params['transaction']['responseReasonCode'] ?? '';
        $respMsg  = $params['transaction']['responseReasonDescription'] ?? '';

        if ($params['transactionResponse']['responseCode'] == '1') {
            $status = Order::STATUS_APPROVED;
        }

        if (in_array($params['transactionResponse']['responseCode'], ['2', '3'])) {
            $status = Order::STATUS_DECLINED;
        }

        $paymentRetrieveCollection = new Collection();
        $paymentRetrieveCollection->set('code', $respCode);
        $paymentRetrieveCollection->set('result', $respMsg);
        $paymentRetrieveCollection->set('status', $status);

        $code = self::$_channelExternalCode[$paymentRetrieveCollection->code] ?? get_system_code('099');

        if ($status == Refund::STATUS_APPROVED) {
            $code = get_system_code('000');
        } elseif ($status == Refund::STATUS_PENDING && $params['transactionResponse']['responseCode'] == '4') {
            $code = get_system_code('210');
        }

        $retrieveCollection = new Collection();
        $retrieveCollection->set('status', self::$_respCode[$code][0]);
        $retrieveCollection->set('code', $code);
        $retrieveCollection->set('result', self::$_respCode[$code][1]);
        $retrieveCollection->set('remark', self::$_respCode[$code][2]);

        $collection = new Collection();
        $collection->set('order', $retrieveCollection->toArray());
        $collection->set('payment_order', $paymentRetrieveCollection->toArray());
        $collection->set('transaction_status', $params['transaction']['transactionStatus']??'');

        return $collection;
    }

    public static function syncParser($params): Collection
    {
        return self::authParser($params);
    }

    public static function asyncParser($params): Collection
    {
        return self::authParser($params);
    }

    public static function _notifyParser($params): Collection
    {
        return self::authParser($params);
    }
}
