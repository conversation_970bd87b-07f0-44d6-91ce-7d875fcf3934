<?php

namespace App\Classes\Pay\Parser;


use App\Classes\Supports\Collection;
use App\Models\Order;
use App\Models\Refund;
use Illuminate\Support\Facades\Cache;
use App\Classes\Pay\Gateways\Gee\Support;
use App\Models\ChargebackCase;
use App\Services\ChargebackCaseService;

class Gee extends BaseParser
{
    protected static $_supplierName = 'Gee';

    public static function _authParser($params): Collection
    {
        $tempStatus = $params['data']['orderState'] ?? $params['code'] ?? 'time out';
        $result     = $params['data']['errMsg'] ?? $params['msg'] ?? '';
        $result     = trim(strlen($result) > 128 ? mb_strcut($result, 0, 128) : $result);

        switch ($tempStatus) {
            case '2':
                $status  = Order::STATUS_APPROVED;
                break;
            default:
                $status  = Order::STATUS_DECLINED;
                break;
        }

        // 3ds正常解析
        if (isset($params['data']['payData'],  $params['data']['payOrderId']) && !empty($params['data']['payData'])){
            switch ($tempStatus) {
            case '0':
            case '1':
            case 'time out':
                $status  = Order::STATUS_PENDING;
                break;
            }
        }

        $paymentOrderCollection = new Collection();
        $paymentOrderCollection->set('type', Order::TYPES_SALE);
        $paymentOrderCollection->set('payment_order_id', $params['data']['payOrderId'] ?? '0');
        $paymentOrderCollection->set('code', $params['data']['errCode'] ?? $params['data']['orderState'] ?? $params['code'] ?? 'time out');
        $paymentOrderCollection->set('result', $result);
        $paymentOrderCollection->set('remark', '');
        $paymentOrderCollection->set('status', $status);
        $paymentOrderCollection->set('html', '');

        $code = self::$_channelExternalCode[$paymentOrderCollection->code] ?? self::$_channelExternalCode[$paymentOrderCollection->result] ?? get_system_code('100');
        if ($status == Order::STATUS_APPROVED) {
            $code = get_system_code('000');
        } elseif ($status == Order::STATUS_PENDING) {
            $code = get_system_code('200');
        }

        //超时默认用p095
        if ($paymentOrderCollection->code == 'time out') {
            $code = get_system_code('095');
        }

        // 3D验证链接返回跳转
        if (isset($params['data']['payData'],  $params['data']['payOrderId']) && !empty($params['data']['payData'])) {
            if (!empty($params['pay_config']['transitUrl'])) {
                $mark       = MD5($params['data']['payOrderId'] . 'll3d');
                $verifyHtml = self::formPackage('', $params['data']['payData']);
                $markArr    = [
                    'transitVerify' => base64_encode($verifyHtml),
                ];

                Cache::add($mark, $markArr, 1800);

                $url = $params['pay_config']['transitUrl'] . '/Verify.php?' . Support::urlPayment($params['pay_config'], ['mark' => $mark]);
            } else {
                $url = $params['data']['payData'];
            }

            $code = get_system_code('200');
            $html = self::formPackage('', $url);
            $paymentOrderCollection->set('status', Order::STATUS_PENDING);
            $paymentOrderCollection->set('html', $html);
        }

        $orderCollection = new Collection();
        $orderCollection->set('status', self::$_respCode[$code][0]);
        $orderCollection->set('code', $code);
        $orderCollection->set('type', Order::TYPES_SALE);
        $orderCollection->set('result', self::$_respCode[$code][1]);
        $orderCollection->set('remark', self::$_respCode[$code][2]);

        if ($code == get_system_code('200') && $paymentOrderCollection->html) {
            $orderCollection->set('is_3d', 1);
        }

        // 卡账单获取
	    if (isset($params['data']['billDes']) && !empty($params['data']['billDes'])) {
		    $cardBill = strlen($params['data']['billDes']) > 64 ? mb_strcut($params['data']['billDes'], 0, 64) : $params['data']['billDes'];
		    $orderCollection->set('card_bill', $cardBill);
	    }

        $collection = new Collection();
        $collection->set('order', $orderCollection->toArray());
        $collection->set('payment_order', $paymentOrderCollection->toArray());
        return $collection;
    }

    public static function captureParser($params): Collection
    {
        return self::authParser($params);
    }

    /**
     * 同步支付通知
     * @param $params
     * @return Collection
     */
    public static function syncParser($params): Collection
    {
        return self::asyncParser($params);
    }

    /**
     * 异步支付通知
     * @param $params
     * @return Collection
     */
    public static function _asyncParser($params): Collection
    {
        $status     = Order::STATUS_PENDING;
        $tempStatus = $params['state'] ?? 'time out';
        $result     = $params['errMsg'] ?? '';
        $result     = trim(strlen($result) > 128 ?  mb_strcut($result, 0, 128) : $result);

        switch ($tempStatus) {
            case '2':
                $status  = Order::STATUS_APPROVED;
                break;
            case '0':
            case '1':
            case 'time out':
                $status  = Order::STATUS_PENDING;
                break;
            default:
                $status  = Order::STATUS_DECLINED;
                break;
        }

        $paymentOrderCollection = new Collection();
        $paymentOrderCollection->set('type', Order::TYPES_SALE);
        $paymentOrderCollection->set('payment_order_id', $params['payOrderId'] ?? '0');
        $paymentOrderCollection->set('code', $params['errCode'] ?? $params['state'] ?? 'time out');
        $paymentOrderCollection->set('result', $result);
        $paymentOrderCollection->set('remark', '');
        $paymentOrderCollection->set('status', $status);
        $paymentOrderCollection->set('html', '');

        $code = self::$_channelExternalCode[$paymentOrderCollection->code] ?? self::$_channelExternalCode[$paymentOrderCollection->result] ?? get_system_code('100');
        if ($status == Order::STATUS_APPROVED) {
            $code = get_system_code('000');
        } elseif ($status == Order::STATUS_PENDING) {
            $code = get_system_code('200');
        }

        //超时默认用p095
        if ($paymentOrderCollection->code == 'time out') {
            $code = get_system_code('095');
        }

        $orderCollection = new Collection();
        $orderCollection->set('status', self::$_respCode[$code][0]);
        $orderCollection->set('code', $code);
        $orderCollection->set('type', Order::TYPES_SALE);
        $orderCollection->set('result', self::$_respCode[$code][1]);
        $orderCollection->set('remark', self::$_respCode[$code][2]);

        // 卡账单获取
	    if (isset($params['billDes']) && !empty($params['billDes'])) {
		    $cardBill = strlen($params['billDes']) > 64 ? mb_strcut($params['billDes'], 0, 64) : $params['billDes'];
		    $orderCollection->set('card_bill', $cardBill);
	    }

        $collection = new Collection();
        $collection->set('order', $orderCollection->toArray());
        $collection->set('payment_order', $paymentOrderCollection->toArray());
        return $collection;
    }

    public static function _retrieveParser($params): Collection
    {
        $status     = Order::STATUS_PENDING;
        $tempStatus = $params['data']['state'] ?? $params['code'] ?? 'time out';
        $result     = $params['data']['errMsg'] ?? $params['msg'] ?? '';
        $result     = trim(strlen($result) > 128 ? mb_strcut($result, 0, 128) : $result);

        switch ($tempStatus) {
            case '2':
                $status  = Order::STATUS_APPROVED;
                break;
            case '0':
            case '1':
            case 'time out':
                $status  = Order::STATUS_PENDING;
                break;
            default:
                $status  = Order::STATUS_DECLINED;
                break;
        }

        $paymentOrderCollection = new Collection();
        $paymentOrderCollection->set('type', Order::TYPES_SALE);
        $paymentOrderCollection->set('payment_order_id', $params['data']['payOrderId'] ?? '0');
        $paymentOrderCollection->set('code', $params['data']['errCode'] ?? $params['data']['state'] ?? $params['code'] ?? 'time out');
        $paymentOrderCollection->set('result', $result);
        $paymentOrderCollection->set('remark', '');
        $paymentOrderCollection->set('status', $status);
        $paymentOrderCollection->set('html', '');

        $code = self::$_channelExternalCode[$paymentOrderCollection->code] ?? self::$_channelExternalCode[$paymentOrderCollection->result] ?? get_system_code('100');
        if ($status == Order::STATUS_APPROVED) {
            $code = get_system_code('000');
        } elseif ($status == Order::STATUS_PENDING) {
            $code = get_system_code('200');
        }

        //超时默认用p095
        if ($paymentOrderCollection->code == 'time out') {
            $code = get_system_code('095');
        }

        $orderCollection = new Collection();
        $orderCollection->set('status', self::$_respCode[$code][0]);
        $orderCollection->set('code', $code);
        $orderCollection->set('type', Order::TYPES_SALE);
        $orderCollection->set('result', self::$_respCode[$code][1]);
        $orderCollection->set('remark', self::$_respCode[$code][2]);

        // 卡账单获取
	    if (isset($params['data']['billDes']) && !empty($params['data']['billDes'])) {
		    $cardBill = strlen($params['data']['billDes']) > 64 ? mb_strcut($params['data']['billDes'], 0, 64) : $params['data']['billDes'];
		    $orderCollection->set('card_bill', $cardBill);
	    }

        $collection = new Collection();
        $collection->set('order', $orderCollection->toArray());
        $collection->set('payment_order', $paymentOrderCollection->toArray());
        return $collection;
    }

    public static function _refundParser($params): Collection
    {
        $status     = Refund::STATUS_PENDING;
        $tempStatus = $params['data']['state'] ?? $params['code'] ?? 'time out';
        $result     = $params['data']['errMsg'] ?? $params['msg'] ?? '';
        $result     = trim(strlen($result) > 128 ? mb_strcut($result, 0, 128) : $result);

        switch ($tempStatus) {
            case '2':
                $status  = Refund::STATUS_APPROVED;
                break;
            case '0':
            case '1':
            case 'time out':
                $status  = Refund::STATUS_PENDING;
                break;
            default:
                $status  = Refund::STATUS_DECLINED;
                break;
        }

        $paymentRefundCollection = new Collection();
        $paymentRefundCollection->set('payment_refund_id', $params['data']['refundOrderId'] ?? '0');
        $paymentRefundCollection->set('code', $params['data']['errCode'] ?? $params['data']['state'] ?? $params['code'] ?? 'time out');
        $paymentRefundCollection->set('result', $result);
        $paymentRefundCollection->set('remark', '');
        $paymentRefundCollection->set('status', $status);

        $code = self::$_channelExternalCode[$paymentRefundCollection->code] ?? self::$_channelExternalCode[$paymentRefundCollection->result] ?? get_system_code('100');

        if ($status == Refund::STATUS_APPROVED) {
            $code = get_system_code('000');
        } elseif ($status == Refund::STATUS_PENDING) {
            $code = get_system_code('200');
        }

        //超时默认用p095
        if ($paymentRefundCollection->code == 'time out') {
            $code = get_system_code('095');
        }

        $refundCollection = new Collection();
        $refundCollection->set('status', self::$_respCode[$code][0]);
        $refundCollection->set('code', $code);
        $refundCollection->set('result', self::$_respCode[$code][1]);
        $refundCollection->set('remark', self::$_respCode[$code][2]);

        $collection = new Collection();
        $collection->set('refund', $refundCollection->toArray());
        $collection->set('payment_refund', $paymentRefundCollection->toArray());

        return $collection;
    }

    public static function _refundUpdate($params): Collection
    {
        return self::refundParser($params);
    }

    public static function _notifyParser($params): Collection
    {
        // 延迟两秒执行
        sleep(2);

        $status     = Refund::STATUS_PENDING;
        $tempStatus = $params['state'] ?? 'time out';
        $result     = $params['errMsg'] ?? $params['msg'] ?? '';
        $result     = trim(strlen($result) > 128 ? mb_strcut($result, 0, 128) : $result);

        switch ($tempStatus) {
            case '2':
                $status  = Refund::STATUS_APPROVED;
                break;
            case '0':
            case '1':
            case 'time out':
                $status  = Refund::STATUS_PENDING;
                break;
            default:
                $status  = Refund::STATUS_DECLINED;
                break;
        }

        $paymentRefundCollection = new Collection();
        $paymentRefundCollection->set('payment_refund_id', $params['refundOrderId'] ?? '0');
        $paymentRefundCollection->set('code', $params['errCode'] ?? $params['state'] ?? 'time out');
        $paymentRefundCollection->set('result', $result);
        $paymentRefundCollection->set('remark', '');
        $paymentRefundCollection->set('status', $status);

        $code = self::$_channelExternalCode[$paymentRefundCollection->code] ?? self::$_channelExternalCode[$paymentRefundCollection->result] ?? get_system_code('100');

        if ($status == Refund::STATUS_APPROVED) {
            $code = get_system_code('000');
        } elseif ($status == Refund::STATUS_PENDING) {
            $code = get_system_code('200');
        }

        //超时默认用p095
        if ($paymentRefundCollection->code == 'time out') {
            $code = get_system_code('095');
        }

        $refundCollection = new Collection();
        $refundCollection->set('status', self::$_respCode[$code][0]);
        $refundCollection->set('code', $code);
        $refundCollection->set('result', self::$_respCode[$code][1]);
        $refundCollection->set('remark', self::$_respCode[$code][2]);

        $collection = new Collection();
        $collection->set('refund', $refundCollection->toArray());
        $collection->set('payment_refund', $paymentRefundCollection->toArray());

        return $collection;
    }

    public static function retrieveChargebackCaseParser($params): Collection
    {
        $code   = $params['code'] ?? 'time out';
        $result = $params['result'] ?? '';
        $error  = [];

        $collection = new Collection();
        if ($params['code'] != 0) {
            $error[] = '渠道返还错误，返回数据' . json_encode(['code' => $code, 'result' => $result, 'data' => $params], JSON_UNESCAPED_SLASHES | JSON_UNESCAPED_UNICODE);
            $collection->set('error', $error);

            return $collection;
        }

        $data = [];
        if (!empty($params['data']['records'])) {
            foreach ($params['data']['records'] as $vo) {
                if (empty($vo['orderId']) || empty($vo['mchOrderNo']) || empty($vo['alertService']) || empty($vo['chargebackReason'])) {
                    continue;
                }

                $caseId        = $vo['orderId'];
                $data[$caseId] = [
                    'case_id'             => $caseId,
                    'order_id'            => $vo['mchOrderNo'],
                    'chargeback_from'     => $vo['alertService'] == 'RDR' ? ChargebackCase::FROM_RDR : ChargebackCase::FROM_ETHOCA,
                    'alert_from'          => ChargebackCase::ALERT_FROM_GEE,
                    'reason_code'         => $vo['chargebackReason'],
                    'warn_currency'       => $vo['currency'],
                    'warn_amount'         => $vo['chargebackAmount'],
                    'dishonour_warn_info' => json_encode($vo, JSON_UNESCAPED_SLASHES | JSON_UNESCAPED_UNICODE),
                    'channel'             => self::$_supplierName,
                ];
            }

            // 过滤掉重复的caseId、已经预警和拒付的数据
            $orderList             = [];
            $chargebackCaseService = new ChargebackCaseService();
            $chargebackCaseService->ProcessingData($data, $orderList, $error);
            $collection->set('data', $data);
            $collection->set('orderList', $orderList);
            $collection->set('error', $error);
        }

        return $collection;
    }
}