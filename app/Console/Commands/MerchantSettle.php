<?php

namespace App\Console\Commands;

use App\Classes\Supports\Logger;
use App\Jobs\SendNotice;
use App\Jobs\SendSlsLog;
use App\Models\Channel;
use App\Models\ChargebackCase;
use App\Models\ChargebackPenalty;
use App\Models\DirectoryBinbase;
use App\Models\DirectoryCountry;
use App\Models\DirectoryCurrency;
use App\Models\Merchant;
use App\Models\MerchantBusiness;
use App\Models\MerchantBusinessHistory;
use App\Models\MerchantCardTicket;
use App\Models\MerchantTicket;
use App\Models\Order as OrderModel;
use App\Models\OrderCard;
use App\Models\OrderRelation;
use App\Models\OrderSettlement;
use App\Models\SettleAdjustment;
use App\Models\SettleDetail;
use App\Models\SettleDetailMerchant;
use App\Models\Settlement;
use App\Models\SettleMerchant;
use App\Services\SettlementService;
use Illuminate\Console\Command;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;

class MerchantSettle extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'settle:merchantSettle';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * @var
     */
    protected $log;

    /**
     * @var array
     */
    protected $merchantList = [];

    /**
     * @var array
     */
    protected $currentBusinessList = [];

    /**
     * @var array
     */
    protected $businessHistoriesList = [];

    /**
     * @var array
     */
    protected $rateFeeList = [];

    /**
     * @var array
     */
    protected $currencyList = [];

    /**
     * @var array
     */
    protected $channelList = [];

    /**
     * @var array
     */
    protected $refundHandleRateFeeDetail = [];

    /**
     * @var array
     */
    protected $refundHandleFeeDetail = [];

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();

        // 初始化日志
        $this->log = new Logger();
        $config    = ['file' => storage_path('logs/consoleTask.log')];
        $this->log->setConfig($config);
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        ini_set('memory_limit', '3072M');
        set_time_limit(0);

        $this->log->info("商户结算任务执行开始");
        dispatch(new SendSlsLog(
            ['message' => '商户结算任务执行开始'],
            [],
            'info',
            'task'
        ));

        // 结算日期
        $date = date('Y-m-d');

        // 初始化结算参数
        $this->_init();

        try {
            // BID结算
            $this->_businessSettle($date);

            // MID结算
            $this->_merchantSettle($date);

        } catch (\Throwable $th) {
            $this->log->error('商户结算任务异常信息：' . $th->getMessage());
            dispatch(new SendSlsLog(
                ['message' => '商户结算任务异常信息：' . $th->getMessage()],
                [],
                'error',
                'task'
            ));

            $content = '[商家结算告警]' . $date . '结算异常' . PHP_EOL . '来源:' . config('app.url');
            $data = [
                'level'             => 1,
                'contents'          => $content,
                'notice_user_roles' => 'Administrator,Settlement Supervisor',
                'type'              => 3,
                'status'            => 2,
            ];
            dispatch(new SendNotice($data, 5));
        }

        $this->log->info("商户结算任务执行结束");
        dispatch(new SendSlsLog(
            ['message' => '商户结算任务执行结束'],
            [],
            'info',
            'task'
        ));
    }

    /**
     * 结算前参数初始化
     */
    private function _init()
    {
        // MID
        $this->merchantList = Merchant::select('merchant_name', 'merchant_id', 'is_credit', 'is_virtual')->get()->keyBy('merchant_id')->toArray();
        if (empty($this->merchantList)) {
            $this->log->error('已激活商户信息不存在,结算任务中止执行');
            dispatch(new SendSlsLog(
                ['message' => '已激活商户信息不存在,结算任务中止执行'],
                [],
                'error',
                'task'
            ));

            die;
        }

        // BID交易计费
        $tempBusiness = MerchantBusiness::get()->toArray();

        foreach ($tempBusiness as $business) {
            $this->currentBusinessList[$business['business_id']] = $business;
        }

        // 币种汇率
        $this->currencyList = DirectoryCurrency::all()->pluck('rate', 'code')->toArray();

        // 币种汇率加入缓存
        Cache::add('MerchantSettle_Currency_List' . date('Ymd'), $this->currencyList, 3 * 60 * 60);

        // 账单标识
        $tempChannelList = Channel::with('channelPid')->whereHas('channelPid', function ($query) {
            $query->where('channel_type', '0');
        })->get()->toArray();

        foreach ($tempChannelList as $channel) {
            $this->channelList[$channel['id']] = $channel;
        }
    }

    /**
     * 自动勾兑
     *
     * @param $orderSettlement
     * @param $date
     */
    private function _autoBlend(&$orderSettlement, $date)
    {
        $orderTypeList = [OrderSettlement::TYPE_SALE, OrderSettlement::TYPE_CAPTURE];

        // 无需自动勾兑
        if (!in_array($orderSettlement['type'], $orderTypeList)
            || $orderSettlement['status'] == OrderSettlement::STATUS_DECLINED) {
            return;
        }

        // 未上传运单的订单推迟结算日
        $orderRelationTmp = OrderRelation::find($orderSettlement['order_id']);

        if (empty($orderRelationTmp)) { // 防止错误数据
            $this->log->error('自动勾兑', [sprintf('%s:订单关系数据不存在', $orderSettlement['order_id'])]);
            dispatch(new SendSlsLog(
                ['message' => '自动勾兑'],
                [sprintf('%s:订单关系数据不存在', $orderSettlement['order_id'])],
                'error',
                'task'
            ));

            $orderSettlement['settle_at'] = date('Y-m-d', strtotime($date . '+1 day'));
            OrderSettlement::where('order_id', $orderSettlement['order_id'])->update(['settle_at' => $orderSettlement['settle_at']]);
            return;
        }

        if ($orderRelationTmp->is_refund == OrderRelation::IS_REFUND_NOT
            && $orderRelationTmp->is_chargeback == OrderRelation::IS_CHARGEBACK_WAITING
            && !$orderRelationTmp->is_delivery
            && !$this->currentBusinessList[$orderSettlement['business_id']]['is_not_delivery']) {

            $settleDate   = SettlementService::getDelaySettleDates($orderSettlement['business_id']);

            //延迟保证金
            $update_date['settle_at'] = $settleDate;
            //保证金不为空并且保证金返还日期小于当前结算时间
            if (
                !empty($orderSettlement['deposit_return_at'])
                && Carbon::parse($orderSettlement['deposit_return_at'])->lt(Carbon::parse($settleDate))
            ) {
                $update_date['deposit_return_at'] = date('Y-m-d', strtotime("{$settleDate} +1 days"));
            }

            $updateResult = OrderSettlement::where('order_id', $orderSettlement['order_id'])->update($update_date);

            if ($updateResult) {
                $orderSettlement['settle_at'] = $settleDate;
                $this->log->info(sprintf('交易订单 %s 未上传运单,结算日期推迟到 %s', $orderSettlement['order_id'], $settleDate));
                dispatch(new SendSlsLog(
                    ['message' => sprintf('交易订单 %s 未上传运单,结算日期推迟到 %s', $orderSettlement['order_id'], $settleDate)],
                    [],
                    'info',
                    'task'
                ));
            }

            return;
        }

        // 勾兑
        if ($orderSettlement['blend_status'] == OrderSettlement::BLEND_STATUS_WAITING) {
            $blendData = [
                'is_blend'                => $orderSettlement['blend_status'],
                'merchant_id'             => $orderSettlement['merchant_id'],
				'business_id'             => $orderSettlement['business_id'],
                'channel_id'              => $orderSettlement['channel_id'],
                'currency'                => $orderSettlement['currency'],
                'amount'                  => $orderSettlement['amount'],
                'settle_currency'         => $orderSettlement['settle_currency'],
                'settle_amount'           => $orderSettlement['settle_amount'],
                'payment_currency'        => $orderSettlement['payment_currency'],
                'payment_amount'          => $orderSettlement['payment_amount'],
                'payment_settle_currency' => $orderSettlement['payment_settle_currency'],
                'payment_settle_amount'   => $orderSettlement['payment_settle_amount'],
                'arrival_currency'        => $orderSettlement['payment_settle_currency'],
                'arrival_amount'          => $orderSettlement['payment_settle_amount'],
                'type'                    => '+'
            ];

            if (in_array($orderSettlement['type'], [OrderSettlement::TYPE_SALE, OrderSettlement::TYPE_AUTH])) {
                $orderId = $orderSettlement['order_id'];
            } else {
                $orderId = $orderSettlement['parent_order_id'];
            }

            $order = OrderModel::with(['card'])->find($orderId);
            if (!empty($order)) {
                $blendData['card_number'] = $order->card->card_number;
            }

            // 重新计算结算金额
            $tempData                                   = SettlementService::calculateSettle($blendData);
            $orderSettlement['settle_currency']         = $tempData['settle_currency'];
            $orderSettlement['settle_amount']           = $tempData['settle_amount'];
            $orderSettlement['payment_settle_currency'] = $tempData['payment_settle_currency'];
            $orderSettlement['payment_settle_amount']   = $tempData['payment_settle_amount'];
            $orderSettlement['rate']                    = $tempData['rate'];
            $orderSettlement['blend_status']            = OrderSettlement::BLEND_STATUS_SUCCESS;
            $orderSettlement['blend_by']                = 'system';
            $orderSettlement['blend_at']                = $date;

            // 更新结算
            $update = $orderSettlement;

            // 去除关联数组
            unset($update['order']);

            OrderSettlement::where('order_id', $orderSettlement['order_id'])->update($update);
        }
    }

    /**
     * BID结算
     *
     * @param $date
     */
    private function _businessSettle($date)
    {
        // 重复性判断
        if (Settlement::where('settle_at', $date)->count()) {
            $this->log->info('BID结算任务已执行过，请勿重复执行');
            dispatch(new SendSlsLog(
                ['message' => 'BID结算任务已执行过，请勿重复执行'],
                [],
                'error',
                'task'
            ));

            return;
        }

        // 分页获取数据
        $success   = 0;
        $failure   = 0;
        $updateCnt = 0;

        for ($page = 1; $page <= 1000000000; $page++) {
            $orderSettlementList = OrderSettlement::with('order')
                ->where('settle_at', $date)
                ->where('is_settle', '0')
                ->forPage(1, 1000)
                ->get()
                ->toArray();
            $settleDetails       = [];
            $updateOrderIds      = [];

            if (empty($orderSettlementList)) {
                break;
            }

            $orderIds          = array_column($orderSettlementList, 'order_id');
            $HistoriesIds      = array_column($orderSettlementList, 'business_id', 'business_history_id');
            $settleDetailsList = SettleDetail::whereIn('related_id', $orderIds)->get()->pluck(['related_id, settle_at'])->toArray();
            //填充历史条款和条款比例费用
            $this->fillBusinessHistoriesListAndRateFeeList($HistoriesIds);

            foreach ($orderSettlementList as $orderSettlement) {
                $business = $this->businessHistoriesList[$orderSettlement['business_history_id']] ?? [];

                if (empty($business)
                    || !isset($business['charge_cc']['transaction_fee_type'])) {
                    //可以改为历史条款不存在
                    $this->log->error(sprintf('%s:历史BID信息不存在', $orderSettlement['order_id']));
                    dispatch(new SendSlsLog(
                        ['message' => sprintf('%s:历史BID信息不存在', $orderSettlement['order_id'])],
                        [],
                        'error',
                        'task'
                    ));

                    throw new \Exception('历史BID信息不存在');
                }

                if (!$business['charge_cc']['transaction_fee_type']
                    && $orderSettlement['status'] == OrderSettlement::STATUS_DECLINED) {
                    $updateOrderIds[] = $orderSettlement['order_id'];
                    continue;
                }

                // 自动勾兑
                $this->_autoBlend($orderSettlement, $date);

                if ($orderSettlement['settle_at'] != $date) {
                    continue; // 重新计算结算日的数据不处理
                }

                // 添加结算明细
                $isSettle = isset($settleDetailsList[$orderSettlement['order_id']]);
                $this->_createBusinessSettleDetails($settleDetails, $orderSettlement, $date, $isSettle);

                // 待更新id
                $updateOrderIds[] = $orderSettlement['order_id'];
            }

            if (!empty($settleDetails)) {
                collect($settleDetails)->chunk(2000)->each(function ($list) use (&$success, &$failure) {
                    $insertData = $list->toArray();

                    if ((new SettleDetail())->insert($insertData)) {
                        $success += count($insertData);
                    } else {
                        $failure += count($insertData);
                    }
                });
            }

            // 更新交易结算状态
            if (!empty($updateOrderIds)) {
                $updateCnt += OrderSettlement::whereIn('order_id', $updateOrderIds)->update(['is_settle' => 1]);
            }
        }

        $this->log->info(sprintf('BID结算明细添加完毕,成功 %s 条,失败 %s 条', $success, $failure));
        dispatch(new SendSlsLog(
            ['message' => sprintf('BID结算明细添加完毕,成功 %s 条,失败 %s 条', $success, $failure)],
            [],
            'info',
            'task'
        ));

        // 更新交易结算状态
        $this->log->info(sprintf('交易订单修改结算状态成功%s条', $updateCnt));
        dispatch(new SendSlsLog(
            ['message' => sprintf('交易订单修改结算状态成功%s条', $updateCnt)],
            [],
            'info',
            'task'
        ));

        // 添加预拒付费
        $settleChargebackCases = ChargebackCase::where(['date_settle' => $date, 'is_normal' => ChargebackCase::IS_NORMAL_TRUE])->get();
        $chargebackCasesData   = [];
        $chargebackCasesCnt    = 0;
        $HistoriesIds          = $settleChargebackCases->pluck('case_id', 'business_history_id')->toArray();
        //填充历史条款和条款比例费用
        $this->fillBusinessHistoriesListAndRateFeeList($HistoriesIds);

        foreach ($settleChargebackCases as $chargebackCases) {
            // 获取商户计费项结算币种
            $business       = $this->businessHistoriesList[$chargebackCases['business_history_id']];
            $chargeCurrency = 'USD';

            if (!in_array($chargeCurrency, explode(',', $business['settle_currencies']))) {
                $chargeCurrency = $business['settle_default_currency'];
            }

            $chargeRate = amount_format($this->currencyList[$chargeCurrency], 4);

            $chargebackCasesData[] = [
                'business_id'         => $chargebackCases['business_id'],
                'merchant_id'         => $chargebackCases['merchant_id'],
                'merchant_name'       => $chargebackCases['merchant_name'],
                'related_id'          => $chargebackCases['order_id'],
                'order_number'        => $chargebackCases['order_number'],
                'channel_id'          => $chargebackCases['channel_id'],
                'channel_supplier_id' => $this->channelList[$chargebackCases['channel_id']]['channel_supplier_id'] ?? '0',
                'settle_currency'     => $chargeCurrency,
                'settle_amount'       => amount_format((-1) * $business['charge_cc']['pre_dishonour_fee'] * $chargeRate),
                'currency'            => 'USD',
                'amount'              => amount_format((-1) * $business['charge_cc']['pre_dishonour_fee']),
                'rate'                => $chargeRate,
                'amount_type'         => SettleDetail::AMOUNT_TYPE_43,
                'order_complete_at'   => $chargebackCases['created_at'],
                'settle_at'           => $date,
                'created_at'          => date_create()
            ];
        }

        if (!empty($chargebackCasesData) && (new SettleDetail())->insert($chargebackCasesData)) {
            $chargebackCasesCnt += count($chargebackCasesData);
        }

        $this->log->info(sprintf('添加预拒付费%s条', $chargebackCasesCnt));
        dispatch(new SendSlsLog(
            ['message' => sprintf('添加预拒付费%s条', $chargebackCasesCnt)],
            [],
            'info',
            'task'
        ));

        // 添加BID结算调整明细
        $settleAdjustment = SettleAdjustment::where('settle_at', $date)->where('type', SettleAdjustment::TYPE_BID)->get();
        $adjustData       = [];
        $adjustCnt        = 0;

        foreach ($settleAdjustment as $adjustment) {
            $adjustData[] = [
                'business_id'         => $adjustment['business_id'],
                'merchant_id'         => $adjustment['merchant_id'],
                'merchant_name'       => $adjustment['merchant_name'],
                'related_id'          => !empty($adjustment['order_id']) ? $adjustment['order_id'] : $adjustment['id'],
                'order_number'        => '0',
                'channel_id'          => '0',
                'channel_supplier_id' => '0',
                'settle_currency'     => $adjustment['settle_currency'],
                'settle_amount'       => $adjustment['settle_amount'],
                'currency'            => $adjustment['currency'],
                'amount'              => $adjustment['amount'],
                'rate'                => $adjustment['rate'],
                'remarks'             => $adjustment['remarks'],
                'amount_type'         => SettleDetail::AMOUNT_TYPE_90,
                'order_complete_at'   => $adjustment['created_at'],
                'settle_at'           => $date,
                'created_at'          => date_create()
            ];
        }

        if (!empty($adjustData) && (new SettleDetail())->insert($adjustData)) {
            $adjustCnt += count($adjustData);
        }

        $this->log->info(sprintf('添加结算调整信息明细%s条', $adjustCnt));
        dispatch(new SendSlsLog(
            ['message' => sprintf('添加结算调整信息明细%s条', $adjustCnt)],
            [],
            'info',
            'task'
        ));

        //添加拒付罚金
        $settleChargebackPenalty      = ChargebackPenalty::where('settle_at', $date)->get();
        $chargebackPenaltyData        = [];
        $chargebackPenaltySpecialData = [];
        $chargebackPenaltyCnt         = 0;
        $chargebackPenaltySpecialCnt  = 0;
        $historiesIds                 = $settleChargebackPenalty->pluck('id', 'business_history_id')->toArray();
        //填充历史条款和条款比例费用
        $this->fillBusinessHistoriesListAndRateFeeList($historiesIds);

        foreach ($settleChargebackPenalty as $chargebackPenalty) {
            // 获取商户计费项结算币种
            $business       = $this->businessHistoriesList[$chargebackPenalty['business_history_id']];
            $chargeCurrency = 'USD';

            if (!in_array($chargeCurrency, explode(',', $business['settle_currencies']))) {
                $chargeCurrency = $business['settle_default_currency'];
            }

            $chargeRate = amount_format($this->currencyList[$chargeCurrency], 4);
            $insert     = [
                'business_id'         => $chargebackPenalty['business_id'],
                'merchant_id'         => $chargebackPenalty['merchant_id'],
                'merchant_name'       => $chargebackPenalty['merchant_name'],
                'related_id'          => $chargebackPenalty['chargeback_id'],
                'order_number'        => $chargebackPenalty['order_number'],
                'channel_id'          => 0,
                'channel_supplier_id' => 0,
                'settle_currency'     => $chargeCurrency,
                'settle_amount'       => amount_format((-1) * $chargebackPenalty['penalty_amount'] * $chargeRate),
                'currency'            => 'USD',
                'amount'              => amount_format((-1) * $chargebackPenalty['penalty_amount']),
                'rate'                => $chargeRate,
                'order_complete_at'   => $chargebackPenalty['created_at'],
                'settle_at'           => $date,
                'created_at'          => date_create()
            ];
            switch ($chargebackPenalty['type']) {
                case ChargebackPenalty::TYPE_FIXED_PENALTY:
                case ChargebackPenalty::TYPE_PROPORTIONAL_PENALTY:
                    $insert['amount_type']   = SettleDetail::AMOUNT_TYPE_44;
                    $chargebackPenaltyData[] = $insert;
                    break;

                default:
                    $insert['amount_type']          = SettleDetail::AMOUNT_TYPE_45;
                    $chargebackPenaltySpecialData[] = $insert;
                    break;
            }
        }

        if (!empty($chargebackPenaltyData) && (new SettleDetail())->insert($chargebackPenaltyData)) {
            $chargebackPenaltyCnt += count($chargebackPenaltyData);
        }

        if (!empty($chargebackPenaltySpecialData) && (new SettleDetail())->insert($chargebackPenaltySpecialData)) {
            $chargebackPenaltySpecialCnt += count($chargebackPenaltySpecialData);
        }

        $this->log->info(sprintf('添加拒付罚金%s条，拒付罚金*%s条', $chargebackPenaltyCnt, $chargebackPenaltySpecialCnt));
        dispatch(new SendSlsLog(
            ['message' => sprintf('添加拒付罚金%s条，拒付罚金*%s条', $chargebackPenaltyCnt, $chargebackPenaltySpecialCnt)],
            [],
            'info',
            'task'
        ));

        // 延时处理
        sleep(1);

        // BID 结算汇总
        $this->_businessSettleTotal($date);

        // BID 结转 MID
        $this->_businessToMerchant($date);

        // 延时处理
        sleep(1);
    }

    /**
     * BID结算汇总
     *
     * @param $date
     */
    private function _businessSettleTotal($date)
    {
        // 获取BID结算币种信息
        $businessCurrencyList = array();

        foreach ($this->currentBusinessList as $value) {
            if (empty($value['settle_currencies'])) {
                continue;
            }

            $businessCurrencyList[$value['business_id']] = array_unique(explode(',', $value['settle_currencies']));
        }

        // 获取入账金额类型对应结算字段列表
        $settleAmountFieldList = Settlement::$settleAmountFieldMap;

        // 获取BID结算明细
        $settleDetails = [];

        SettleDetail::select([
            'business_id',
            'settle_currency',
            DB::raw('SUM(settle_amount) as settle_amount'),
            'amount_type'
        ])->where('settle_at', $date)
            ->groupBy('business_id', 'settle_currency', 'amount_type')
            ->get()
            ->each(function ($value) use ($settleAmountFieldList, &$settleDetails) {
                $field = isset($settleAmountFieldList[$value->amount_type]) ? $settleAmountFieldList[$value->amount_type] : ''; // settle中对应金额字段

                if (!empty($field)) {
                    $settleDetails[$value->business_id][$value->settle_currency][$field] = $value->settle_amount;
                }
            });

        // 添加settle
        $success = 0;
        $failure = 0;

        foreach ($businessCurrencyList as $businessId => $currencyList) {
            foreach ($currencyList as $currency) {
                $data = [
                    'business_id'     => $businessId,
                    'merchant_id'     => $this->currentBusinessList[$businessId]['merchant_id'],
                    'merchant_name'   => $this->currentBusinessList[$businessId]['merchant_name'],
                    'settle_currency' => $currency,
                    'settle_at'       => $date
                ];

                $amountList            = isset($settleDetails[$businessId][$currency]) ? array_map('amount_format', $settleDetails[$businessId][$currency]) : [];
                $data['settle_amount'] = !empty($amountList) ? amount_format(array_sum($amountList)) : '0.00';
                $data                  = array_merge($data, $amountList);

                if (Settlement::create($data)) {
                    $success++;
                } else {
                    $failure++;
                }
            }
        }

        $this->log->info(sprintf('添加商户结算汇总成功%s条, 失败%s条', $success, $failure));
        dispatch(new SendSlsLog(
            ['message' => sprintf('添加商户结算汇总成功%s条, 失败%s条', $success, $failure)],
            [],
            'info',
            'task'
        ));
    }

    /**
     * BID结转至MID
     *
     * @param $date
     */
    private function _businessToMerchant($date)
    {
        // 结转
        $settleList     = SettleDetail::getSettleBalanceToMid($date);
        $inDepositList  = SettleDetail::getInDepositBalanceToMid($date);
        $outDepositList = SettleDetail::getOutDepositBalanceToMid($date);

        // 获取BID结算币种信息
        $businessCurrencyList = array();

        foreach ($this->currentBusinessList as $value) {
            if (empty($value['settle_currencies'])) {
                continue;
            }

            $businessCurrencyList[$value['business_id']] = array_unique(explode(',', $value['settle_currencies']));
        }

        $settleDetails = [];
        $success       = 0;

        foreach ($businessCurrencyList as $businessId => $currencyList) {
            foreach ($currencyList as $currency) {
                $settleAmount     = isset($settleList[$businessId][$currency]) ? $settleList[$businessId][$currency] : '0.00';
                $inDepositAmount  = isset($inDepositList[$businessId][$currency]) ? $inDepositList[$businessId][$currency] : '0.00';
                $outDepositAmount = isset($outDepositList[$businessId][$currency]) ? $outDepositList[$businessId][$currency] : '0.00';

                // 添加结转
                $settleDetails[] = [
                    'business_id'         => $businessId,
                    'merchant_id'         => $this->currentBusinessList[$businessId]['merchant_id'],
                    'merchant_name'       => $this->currentBusinessList[$businessId]['merchant_name'],
                    'related_id'          => '0',
                    'order_number'        => '0',
                    'channel_id'          => '0',
                    'channel_supplier_id' => '0',
                    'settle_currency'     => $currency,
                    'settle_amount'       => amount_format((-1) * $settleAmount),
                    'currency'            => $currency,
                    'amount'              => amount_format((-1) * $settleAmount),
                    'rate'                => '1.0000',
                    'amount_type'         => SettleDetail::AMOUNT_TYPE_50,
                    'order_complete_at'   => date_create(),
                    'settle_at'           => $date,
                    'created_at'          => date_create()
                ];
                $settleDetails[] = [
                    'business_id'         => $businessId,
                    'merchant_id'         => $this->currentBusinessList[$businessId]['merchant_id'],
                    'merchant_name'       => $this->currentBusinessList[$businessId]['merchant_name'],
                    'related_id'          => '0',
                    'order_number'        => '0',
                    'channel_id'          => '0',
                    'channel_supplier_id' => '0',
                    'settle_currency'     => $currency,
                    'settle_amount'       => $inDepositAmount,
                    'currency'            => $currency,
                    'amount'              => $inDepositAmount,
                    'rate'                => '1.0000',
                    'amount_type'         => SettleDetail::AMOUNT_TYPE_51,
                    'order_complete_at'   => date_create(),
                    'settle_at'           => $date,
                    'created_at'          => date_create()
                ];
                $settleDetails[] = [
                    'business_id'         => $businessId,
                    'merchant_id'         => $this->currentBusinessList[$businessId]['merchant_id'],
                    'merchant_name'       => $this->currentBusinessList[$businessId]['merchant_name'],
                    'related_id'          => '0',
                    'order_number'        => '0',
                    'channel_id'          => '0',
                    'channel_supplier_id' => '0',
                    'settle_currency'     => $currency,
                    'settle_amount'       => $outDepositAmount,
                    'currency'            => $currency,
                    'amount'              => $outDepositAmount,
                    'rate'                => '1.0000',
                    'amount_type'         => SettleDetail::AMOUNT_TYPE_52,
                    'order_complete_at'   => date_create(),
                    'settle_at'           => $date,
                    'created_at'          => date_create()
                ];
            }
        }

        if ((new SettleDetail())->insert($settleDetails)) {
            $success += count($settleDetails);
        }

        $this->log->info(sprintf('添加结转MID明细成功%s条', $success));
        dispatch(new SendSlsLog(
            ['message' => sprintf('添加结转MID明细成功%s条', $success)],
            [],
            'info',
            'task'
        ));
    }

    /**
     * MID结算明细
     *
     * @param $date
     */
    private function _merchantSettle($date)
    {

        // 重复性判断
        if (SettleMerchant::where('settle_at', $date)->count()) {
            $this->log->info('MID结算任务已执行过，请勿重复执行');
            dispatch(new SendSlsLog(
                ['message' => 'MID结算任务已执行过，请勿重复执行'],
                [],
                'error',
                'task'
            ));

            return;
        }

        $data = [];

        // 添加BID结转MID
        $settleDetails     = [];
        $detailsCnt        = 0;
        $tempSettleDetails = SettleDetail::select()
            ->whereIn('amount_type', [SettleDetail::AMOUNT_TYPE_50, SettleDetail::AMOUNT_TYPE_51, SettleDetail::AMOUNT_TYPE_52])
            ->where('settle_at', $date)
            ->get()->toArray();

        foreach ($tempSettleDetails as $detail) {
            if (!isset($settleDetails[$detail['merchant_id']][$detail['settle_currency']][$detail['amount_type']])) {
                $settleDetails[$detail['merchant_id']][$detail['settle_currency']][$detail['amount_type']] = '0.00';
            }

            $settleDetails[$detail['merchant_id']][$detail['settle_currency']][$detail['amount_type']] += $detail['settle_amount'];
        }

        foreach ($settleDetails as $merchantId => $currencyList) {
            foreach ($currencyList as $currency => $amountList) {
                $data[] = [
                    'merchant_id'        => $merchantId,
                    'merchant_name'      => $this->merchantList[$merchantId]['merchant_name'],
                    'amount_type'        => SettleDetailMerchant::AMOUNT_TYPE_0,
                    'settle_currency'    => $currency,
                    'settle_amount'      => amount_format((-1) * $amountList[SettleDetail::AMOUNT_TYPE_50]),
                    'in_deposit_amount'  => amount_format((-1) * $amountList[SettleDetail::AMOUNT_TYPE_51]),
                    'out_deposit_amount' => amount_format((-1) * $amountList[SettleDetail::AMOUNT_TYPE_52]),
                    'settle_at'          => $date,
                    'created_at'         => date_create()
                ];

                $detailsCnt++;
            }
        }

        // 添加MID结算调整明细
        $settleAdjustment = SettleAdjustment::where('settle_at', $date)->where('type', SettleAdjustment::TYPE_MID)->get();
        $adjustCnt        = 0;

        foreach ($settleAdjustment as $adjustment) {
            $data[] = [
                'merchant_id'        => $adjustment['merchant_id'],
                'merchant_name'      => $adjustment['merchant_name'],
                'amount_type'        => SettleDetailMerchant::AMOUNT_TYPE_2,
                'settle_currency'    => $adjustment['settle_currency'],
                'settle_amount'      => $adjustment['settle_amount'],
                'in_deposit_amount'  => '0.00',
                'out_deposit_amount' => '0.00',
                'settle_at'          => $date,
                'created_at'         => date_create()
            ];

            $adjustCnt++;
        }

        // 添加mid充值表
        $merchantTicketCnt = 0;
        $idArr             = [];
        $merchantTicket    = MerchantTicket::where('settle_at', $date)
            ->where('status', MerchantTicket::MERCHANT_TICKET_STATUS_SUCCESS)
            ->where('is_settle', MerchantTicket::STATUS_DECLINED)
            ->get();

        foreach ($merchantTicket as $ticket) {
            $idArr[] = $ticket['id'];
            $data[]  = [
                'merchant_id'        => $ticket['merchant_id'],
                'merchant_name'      => $ticket['merchant_name'],
                'amount_type'        => SettleDetailMerchant::AMOUNT_TYPE_1,
                'settle_currency'    => $ticket['refill_currency'],
                'settle_amount'      => $ticket['reality_refill_amount'],
                'in_deposit_amount'  => '0.00',
                'out_deposit_amount' => '0.00',
                'settle_at'          => $date,
                'created_at'         => date_create()
            ];
            $merchantTicketCnt++;
        }

        $updateCnt = MerchantTicket::whereIn('id', $idArr)->update(['is_settle' => MerchantTicket::STATUS_APPROVED]);
        $this->log->info(sprintf('MID充值工单修改结算状态成功%s条', $updateCnt));
        dispatch(new SendSlsLog(
            ['message' => sprintf('MID充值工单修改结算状态成功%s条', $updateCnt)],
            [],
            'info',
            'task'
        ));

        // CID充值表
        $ticketIds          = [];
        $ticketCnt          = 0;
        $merchantCardTicket = MerchantCardTicket::where('settle_at', $date)
            ->where('status', MerchantCardTicket::MERCHANT_TICKET_STATUS_SUCCESS)
            ->where('is_settle', MerchantCardTicket::MERCHANT_TICKET_IS_SETTLE_WAITING)
            ->get();

        foreach ($merchantCardTicket as $ticket) {
            $ticketIds[] = $ticket['id'];
            $typeNumber  = $ticket['type'] == MerchantCardTicket::MERCHANT_TICKET_TYPE_IN ? -1 : 1;
            $data[]      = [
                'merchant_id'        => $ticket['merchant_id'],
                'merchant_name'      => $ticket['merchant_name'],
                'amount_type'        => $ticket['type'] == MerchantCardTicket::MERCHANT_TICKET_TYPE_IN ? SettleDetailMerchant::AMOUNT_TYPE_3 : SettleDetailMerchant::AMOUNT_TYPE_4,
                'settle_currency'    => $ticket['currency'],
                'settle_amount'      => amount_format($typeNumber * $ticket['amount']),
                'in_deposit_amount'  => '0.00',
                'out_deposit_amount' => '0.00',
                'settle_at'          => $date,
                'created_at'         => date_create()
            ];
            $data[]      = [
                'merchant_id'        => $ticket['merchant_id'],
                'merchant_name'      => $ticket['merchant_name'],
                'amount_type'        => $ticket['type'] == MerchantCardTicket::MERCHANT_TICKET_TYPE_IN ? SettleDetailMerchant::AMOUNT_TYPE_5 : SettleDetailMerchant::AMOUNT_TYPE_6,
                'settle_currency'    => $ticket['currency'],
                'settle_amount'      => amount_format($typeNumber * $ticket['fee']),
                'in_deposit_amount'  => '0.00',
                'out_deposit_amount' => '0.00',
                'settle_at'          => $date,
                'created_at'         => date_create()
            ];

            $ticketCnt++;
        }

        $updateCnt = MerchantCardTicket::whereIn('id', $ticketIds)->update(['is_settle' => MerchantCardTicket::MERCHANT_TICKET_IS_SETTLE_SUCCESS]);
        $this->log->info(sprintf('CID充值工单修改结算状态成功%s条', $updateCnt));
        dispatch(new SendSlsLog(
            ['message' => sprintf('CID充值工单修改结算状态成功%s条', $updateCnt)],
            [],
            'info',
            'task'
        ));

        if (!empty($data) && (new SettleDetailMerchant())->insert($data)) {
            $this->log->info(sprintf('添加MID结算明细成功. BID转入%s条,结算调整%s条,MID充值表%s条,CID充退值%s条', $detailsCnt, $adjustCnt, $merchantTicketCnt, $ticketCnt));
            dispatch(new SendSlsLog(
                ['message' => sprintf('添加MID结算明细成功. BID转入%s条,结算调整%s条,MID充值表%s条,CID充退值%s条', $detailsCnt, $adjustCnt, $merchantTicketCnt, $ticketCnt)],
                [],
                'info',
                'task'
            ));
        } else {
            $this->log->info('添加MID结算明细失败');
            dispatch(new SendSlsLog(
                ['message' => '添加MID结算明细失败'],
                [],
                'info',
                'task'
            ));
        }

        // 延时处理
        sleep(1);

        // MID结算汇总
        $this->_merchantSettleTotal($date);
    }

    /**
     * MID结算汇总
     *
     * @param $date
     */
    private function _merchantSettleTotal($date)
    {
        // 分类BID
        $bidMidList = [];
        // 获取MID结算币种信息
        $merchantCurrencyList = array();

        foreach ($this->currentBusinessList as $value) {
            if (empty($value['settle_currencies'])) {
                continue;
            }

            $bidMidList[$value['merchant_id']][] = $value['settle_currencies'];
        }

        foreach ($this->merchantList as $value) {
            $merchantCurrencyList[$value['merchant_id']] = [];

            if ($value['is_credit']) {
                $merchantCurrencyList[$value['merchant_id']] = $bidMidList[$value['merchant_id']] ?? [];
            }

            if ($value['is_virtual']) {
                $merchantCurrencyList[$value['merchant_id']][] = 'USD';
            }
        }

        // 获取MID结算明细
        $settleDetails     = [];
        $tempSettleDetails = SettleDetailMerchant::select([
            'merchant_id',
            'settle_currency',
            DB::raw('SUM(settle_amount) as settle_amount'),
            DB::raw('SUM(in_deposit_amount) as in_deposit_amount'),
            DB::raw('SUM(out_deposit_amount) as out_deposit_amount')
        ])->where('settle_at', $date)
            ->groupBy('merchant_id', 'settle_currency')
            ->get()->toArray();

        foreach ($tempSettleDetails as $detail) {
            $settleDetails[$detail['merchant_id']][$detail['settle_currency']] = $detail;
        }

        // 添加结算汇总
        $settleCnt  = 0;
        $settleData = [];

        foreach ($merchantCurrencyList as $merchantId => $tempCurrencyList) {
            $currencyList = array_unique(explode(',', implode(',', $tempCurrencyList)));

            foreach ($currencyList as $currency) {
                $settleData[] = array(
                    'merchant_id'        => $merchantId,
                    'merchant_name'      => $this->merchantList[$merchantId]['merchant_name'],
                    'settle_currency'    => $currency,
                    'settle_amount'      => isset($settleDetails[$merchantId][$currency]['settle_amount']) ? $settleDetails[$merchantId][$currency]['settle_amount'] : '0.00',
                    'in_deposit_amount'  => isset($settleDetails[$merchantId][$currency]['in_deposit_amount']) ? $settleDetails[$merchantId][$currency]['in_deposit_amount'] : '0.00',
                    'out_deposit_amount' => isset($settleDetails[$merchantId][$currency]['out_deposit_amount']) ? $settleDetails[$merchantId][$currency]['out_deposit_amount'] : '0.00',
                    'settle_at'          => $date,
                    'created_at'         => date_create()
                );

                $settleCnt++;
            }
        }

        if ((new SettleMerchant())->insert($settleData)) {
            $this->log->info(sprintf('添加MID结算汇总成功%s条', $settleCnt));
            dispatch(new SendSlsLog(
                ['message' => sprintf('添加MID结算汇总成功%s条', $settleCnt)],
                [],
                'info',
                'task'
            ));
        } else {
            $this->log->info(sprintf('添加商户结算汇总失败%s条', $settleCnt));
            dispatch(new SendSlsLog(
                ['message' => sprintf('添加商户结算汇总失败%s条', $settleCnt)],
                [],
                'info',
                'task'
            ));
        }
    }

    /**
     * 添加BID结算明细
     *
     * @param $settleDetails
     * @param $orderSettlement
     * @param $date
     * @param $isSettle
     */
    private function _createBusinessSettleDetails(&$settleDetails, $orderSettlement, $date, $isSettle)
    {
        // 消费与预授权交易类型
        $transactionTypeList = [OrderSettlement::TYPE_SALE, OrderSettlement::TYPE_CAPTURE];
        $rate                = '0.00';

        if (in_array($orderSettlement['type'], $transactionTypeList)
            && $orderSettlement['status'] == OrderSettlement::STATUS_APPROVED) {

            $rate = $this->getCommissionRatio($orderSettlement);
        }

        // 获取商户计费项结算币种
        $chargeCurrency = $orderSettlement['settle_currency'];
        $chargeRate     = amount_format($this->currencyList[$chargeCurrency], 4);

        // 渠道id
        $supplierId = isset($this->channelList[$orderSettlement['channel_id']]['channel_supplier_id']) ? $this->channelList[$orderSettlement['channel_id']]['channel_supplier_id'] : '0';

        // 交易类型
        $type = in_array($orderSettlement['type'], [OrderSettlement::TYPE_REFUND, OrderSettlement::TYPE_CHARGEBACK]) ? '-1' : '1';

        switch ($orderSettlement['type']) {
            case OrderSettlement::TYPE_SALE:
                if ($orderSettlement['status'] == OrderSettlement::STATUS_APPROVED) {
                    // sale金额
                    $settleDetails[] = [
                        'business_id'         => $orderSettlement['business_id'],
                        'merchant_id'         => $orderSettlement['merchant_id'],
                        'merchant_name'       => $orderSettlement['merchant_name'],
                        'related_id'          => $orderSettlement['order_id'],
                        'order_number'        => $orderSettlement['order_number'],
                        'channel_id'          => $orderSettlement['channel_id'],
                        'channel_supplier_id' => $supplierId,
                        'settle_currency'     => $orderSettlement['settle_currency'],
                        'settle_amount'       => amount_format($type * $orderSettlement['settle_amount']),
                        'currency'            => $orderSettlement['currency'],
                        'amount'              => amount_format($type * $orderSettlement['amount']),
                        'rate'                => $orderSettlement['rate'],
                        'amount_type'         => SettleDetail::AMOUNT_TYPE_00,
                        'order_complete_at'   => $orderSettlement['completed_at'],
                        'settle_at'           => $date,
                        'created_at'          => date_create(),
                        'remarks'             => ''
                    ];

                    // 消费比例手续费
                    $transactionRateFee = amount_format((-1) * $orderSettlement['amount'] * $rate / 100, 2);
                    $settleRateFee      = amount_format((-1) * $orderSettlement['settle_amount'] * $rate / 100, 2);
                    $settleDetails[]    = [
                        'business_id'         => $orderSettlement['business_id'],
                        'merchant_id'         => $orderSettlement['merchant_id'],
                        'merchant_name'       => $orderSettlement['merchant_name'],
                        'related_id'          => $orderSettlement['order_id'],
                        'order_number'        => $orderSettlement['order_number'],
                        'channel_id'          => $orderSettlement['channel_id'],
                        'channel_supplier_id' => $supplierId,
                        'settle_currency'     => $orderSettlement['settle_currency'],
                        'settle_amount'       => $settleRateFee,
                        'currency'            => $orderSettlement['currency'],
                        'amount'              => $transactionRateFee,
                        'rate'                => $transactionRateFee == '0.00' ? '1.0000' : amount_format($settleRateFee / $transactionRateFee, 4),
                        'amount_type'         => SettleDetail::AMOUNT_TYPE_01,
                        'order_complete_at'   => $orderSettlement['completed_at'],
                        'settle_at'           => $date,
                        'created_at'          => date_create(),
                        'remarks'             => ''
                    ];

                    // 消费保证金
                    $business = $this->businessHistoriesList[$orderSettlement['business_history_id']];
                    $deposit  = $business['deposit'][MerchantBusiness::DEPOSIT_TYPE_ACTIVITY][$orderSettlement['cc_type']] ?? $business['deposit'][MerchantBusiness::DEPOSIT_TYPE_ACTIVITY]['*'] ?? [];
                    if (!empty($deposit) && isset($deposit['deposit_value'])) {
                        $transactionDepositAmount = amount_format($orderSettlement['amount'] * $deposit['deposit_value'] / 100, 2);
                        $settleDepositAmount      = amount_format($orderSettlement['settle_amount'] * $deposit['deposit_value'] / 100, 2);

                        $settleDetails[] = [
                            'business_id'         => $orderSettlement['business_id'],
                            'merchant_id'         => $orderSettlement['merchant_id'],
                            'merchant_name'       => $orderSettlement['merchant_name'],
                            'related_id'          => $orderSettlement['order_id'],
                            'order_number'        => $orderSettlement['order_number'],
                            'channel_id'          => $orderSettlement['channel_id'],
                            'channel_supplier_id' => $supplierId,
                            'settle_currency'     => $orderSettlement['settle_currency'],
                            'settle_amount'       => amount_format((-1) * $settleDepositAmount),
                            'currency'            => $orderSettlement['currency'],
                            'amount'              => amount_format((-1) * $transactionDepositAmount),
                            'rate'                => $transactionDepositAmount == '0.00' ? '1.0000' : amount_format($settleDepositAmount / $transactionDepositAmount, 4),
                            'amount_type'         => SettleDetail::AMOUNT_TYPE_03,
                            'order_complete_at'   => $orderSettlement['completed_at'],
                            'settle_at'           => $date,
                            'created_at'          => date_create(),
                            'remarks'             => ''
                        ];
                        $settleDetails[] = [
                            'business_id'         => $orderSettlement['business_id'],
                            'merchant_id'         => $orderSettlement['merchant_id'],
                            'merchant_name'       => $orderSettlement['merchant_name'],
                            'related_id'          => $orderSettlement['order_id'],
                            'order_number'        => $orderSettlement['order_number'],
                            'channel_id'          => $orderSettlement['channel_id'],
                            'channel_supplier_id' => $supplierId,
                            'settle_currency'     => $orderSettlement['settle_currency'],
                            'settle_amount'       => $settleDepositAmount,
                            'currency'            => $orderSettlement['currency'],
                            'amount'              => $transactionDepositAmount,
                            'rate'                => $transactionDepositAmount == '0.00' ? '1.0000' : amount_format($settleDepositAmount / $transactionDepositAmount, 4),
                            'amount_type'         => SettleDetail::AMOUNT_TYPE_03,
                            'order_complete_at'   => $orderSettlement['completed_at'],
                            'settle_at'           => $orderSettlement['deposit_return_at'],
                            'created_at'          => date_create(),
                            'remarks'             => ''
                        ];
                    }

                    // 3d处理费
                    if ($orderSettlement['order']['is_3d']) {
                        $settleDetails[] = [
                            'business_id'         => $orderSettlement['business_id'],
                            'merchant_id'         => $orderSettlement['merchant_id'],
                            'merchant_name'       => $orderSettlement['merchant_name'],
                            'related_id'          => $orderSettlement['order_id'],
                            'order_number'        => $orderSettlement['order_number'],
                            'channel_id'          => $orderSettlement['channel_id'],
                            'channel_supplier_id' => $supplierId,
                            'settle_currency'     => $chargeCurrency,
                            'settle_amount'       => amount_format((-1) * $this->businessHistoriesList[$orderSettlement['business_history_id']]['charge_cc']['threed_fee'] * $chargeRate),
                            'currency'            => 'USD',
                            'amount'              => amount_format((-1) * $this->businessHistoriesList[$orderSettlement['business_history_id']]['charge_cc']['threed_fee']),
                            'rate'                => $chargeRate,
                            'amount_type'         => SettleDetail::AMOUNT_TYPE_04,
                            'order_complete_at'   => $orderSettlement['completed_at'],
                            'settle_at'           => $date,
                            'created_at'          => date_create(),
                            'remarks'             => ''
                        ];
                    }

                    // 风控处理费
                    $settleDetails[] = [
                        'business_id'         => $orderSettlement['business_id'],
                        'merchant_id'         => $orderSettlement['merchant_id'],
                        'merchant_name'       => $orderSettlement['merchant_name'],
                        'related_id'          => $orderSettlement['order_id'],
                        'order_number'        => $orderSettlement['order_number'],
                        'channel_id'          => $orderSettlement['channel_id'],
                        'channel_supplier_id' => $supplierId,
                        'settle_currency'     => $chargeCurrency,
                        'settle_amount'       => amount_format((-1) * $this->businessHistoriesList[$orderSettlement['business_history_id']]['charge_cc']['risk_fee'] * $chargeRate),
                        'currency'            => 'USD',
                        'amount'              => amount_format((-1) * $this->businessHistoriesList[$orderSettlement['business_history_id']]['charge_cc']['risk_fee']),
                        'rate'                => $chargeRate,
                        'amount_type'         => SettleDetail::AMOUNT_TYPE_05,
                        'order_complete_at'   => $orderSettlement['completed_at'],
                        'settle_at'           => $date,
                        'created_at'          => date_create(),
                        'remarks'             => ''
                    ];
                }

                // 处理费
                if (!$isSettle) {
                    $settleDetails[] = [
                        'business_id'         => $orderSettlement['business_id'],
                        'merchant_id'         => $orderSettlement['merchant_id'],
                        'merchant_name'       => $orderSettlement['merchant_name'],
                        'related_id'          => $orderSettlement['order_id'],
                        'order_number'        => $orderSettlement['order_number'],
                        'channel_id'          => $orderSettlement['channel_id'],
                        'channel_supplier_id' => $supplierId,
                        'settle_currency'     => $chargeCurrency,
                        'settle_amount'       => amount_format((-1) * $this->businessHistoriesList[$orderSettlement['business_history_id']]['charge_cc']['transaction_fee'] * $chargeRate),
                        'currency'            => 'USD',
                        'amount'              => amount_format((-1) * $this->businessHistoriesList[$orderSettlement['business_history_id']]['charge_cc']['transaction_fee']),
                        'rate'                => $chargeRate,
                        'amount_type'         => SettleDetail::AMOUNT_TYPE_02,
                        'order_complete_at'   => $orderSettlement['completed_at'],
                        'settle_at'           => $date,
                        'created_at'          => date_create(),
                        'remarks'             => ''
                    ];
                }
                break;
            case OrderSettlement::TYPE_AUTH:
                if ($orderSettlement['status'] == OrderSettlement::STATUS_APPROVED) {
                    // auth 3d处理费
                    if ($orderSettlement['order']['is_3d']) {
                        $settleDetails[] = [
                            'business_id'         => $orderSettlement['business_id'],
                            'merchant_id'         => $orderSettlement['merchant_id'],
                            'merchant_name'       => $orderSettlement['merchant_name'],
                            'related_id'          => $orderSettlement['order_id'],
                            'order_number'        => $orderSettlement['order_number'],
                            'channel_id'          => $orderSettlement['channel_id'],
                            'channel_supplier_id' => $supplierId,
                            'settle_currency'     => $chargeCurrency,
                            'settle_amount'       => amount_format((-1) * $this->businessHistoriesList[$orderSettlement['business_history_id']]['charge_cc']['threed_fee'] * $chargeRate),
                            'currency'            => 'USD',
                            'amount'              => amount_format((-1) * $this->businessHistoriesList[$orderSettlement['business_history_id']]['charge_cc']['threed_fee']),
                            'rate'                => $chargeRate,
                            'amount_type'         => SettleDetail::AMOUNT_TYPE_11,
                            'order_complete_at'   => $orderSettlement['completed_at'],
                            'settle_at'           => $date,
                            'created_at'          => date_create(),
                            'remarks'             => ''
                        ];
                    }

                    // auth风控处理费
                    $settleDetails[] = [
                        'business_id'         => $orderSettlement['business_id'],
                        'merchant_id'         => $orderSettlement['merchant_id'],
                        'merchant_name'       => $orderSettlement['merchant_name'],
                        'related_id'          => $orderSettlement['order_id'],
                        'order_number'        => $orderSettlement['order_number'],
                        'channel_id'          => $orderSettlement['channel_id'],
                        'channel_supplier_id' => $supplierId,
                        'settle_currency'     => $chargeCurrency,
                        'settle_amount'       => amount_format((-1) * $this->businessHistoriesList[$orderSettlement['business_history_id']]['charge_cc']['risk_fee'] * $chargeRate),
                        'currency'            => 'USD',
                        'amount'              => amount_format((-1) * $this->businessHistoriesList[$orderSettlement['business_history_id']]['charge_cc']['risk_fee']),
                        'rate'                => $chargeRate,
                        'amount_type'         => SettleDetail::AMOUNT_TYPE_12,
                        'order_complete_at'   => $orderSettlement['completed_at'],
                        'settle_at'           => $date,
                        'created_at'          => date_create(),
                        'remarks'             => ''
                    ];
                }

                // auth处理费
                $settleDetails[] = [
                    'business_id'         => $orderSettlement['business_id'],
                    'merchant_id'         => $orderSettlement['merchant_id'],
                    'merchant_name'       => $orderSettlement['merchant_name'],
                    'related_id'          => $orderSettlement['order_id'],
                    'order_number'        => $orderSettlement['order_number'],
                    'channel_id'          => $orderSettlement['channel_id'],
                    'channel_supplier_id' => $supplierId,
                    'settle_currency'     => $chargeCurrency,
                    'settle_amount'       => amount_format((-1) * $this->businessHistoriesList[$orderSettlement['business_history_id']]['charge_cc']['transaction_fee'] * $chargeRate),
                    'currency'            => 'USD',
                    'amount'              => amount_format((-1) * $this->businessHistoriesList[$orderSettlement['business_history_id']]['charge_cc']['transaction_fee']),
                    'rate'                => $chargeRate,
                    'amount_type'         => SettleDetail::AMOUNT_TYPE_10,
                    'order_complete_at'   => $orderSettlement['completed_at'],
                    'settle_at'           => $date,
                    'created_at'          => date_create(),
                    'remarks'             => ''
                ];
                break;
            case OrderSettlement::TYPE_CAPTURE:
                if ($orderSettlement['status'] == OrderSettlement::STATUS_APPROVED) {
                    // capture金额
                    $settleDetails[] = [
                        'business_id'         => $orderSettlement['business_id'],
                        'merchant_id'         => $orderSettlement['merchant_id'],
                        'merchant_name'       => $orderSettlement['merchant_name'],
                        'related_id'          => $orderSettlement['order_id'],
                        'order_number'        => $orderSettlement['order_number'],
                        'channel_id'          => $orderSettlement['channel_id'],
                        'channel_supplier_id' => $supplierId,
                        'settle_currency'     => $orderSettlement['settle_currency'],
                        'settle_amount'       => amount_format($type * $orderSettlement['settle_amount']),
                        'currency'            => $orderSettlement['currency'],
                        'amount'              => amount_format($type * $orderSettlement['amount']),
                        'rate'                => $orderSettlement['rate'],
                        'amount_type'         => SettleDetail::AMOUNT_TYPE_20,
                        'order_complete_at'   => $orderSettlement['completed_at'],
                        'settle_at'           => $date,
                        'created_at'          => date_create(),
                        'remarks'             => ''
                    ];

                    // capture比例手续费
                    $transactionRateFee = amount_format((-1) * $orderSettlement['amount'] * $rate / 100, 2);
                    $settleRateFee      = amount_format((-1) * $orderSettlement['settle_amount'] * $rate / 100, 2);
                    $settleDetails[]    = [
                        'business_id'         => $orderSettlement['business_id'],
                        'merchant_id'         => $orderSettlement['merchant_id'],
                        'merchant_name'       => $orderSettlement['merchant_name'],
                        'related_id'          => $orderSettlement['order_id'],
                        'order_number'        => $orderSettlement['order_number'],
                        'channel_id'          => $orderSettlement['channel_id'],
                        'channel_supplier_id' => $supplierId,
                        'settle_currency'     => $orderSettlement['settle_currency'],
                        'settle_amount'       => $settleRateFee,
                        'currency'            => $orderSettlement['currency'],
                        'amount'              => $transactionRateFee,
                        'rate'                => $transactionRateFee == '0.00' ? '1.0000' : amount_format($settleRateFee / $transactionRateFee, 4),
                        'amount_type'         => SettleDetail::AMOUNT_TYPE_21,
                        'order_complete_at'   => $orderSettlement['completed_at'],
                        'settle_at'           => $date,
                        'created_at'          => date_create(),
                        'remarks'             => ''
                    ];

                    // capture保证金
                    $business = $this->businessHistoriesList[$orderSettlement['business_history_id']];
                    $deposit  = $business['deposit'][MerchantBusiness::DEPOSIT_TYPE_ACTIVITY][$orderSettlement['cc_type']] ?? $business['deposit'][MerchantBusiness::DEPOSIT_TYPE_ACTIVITY]['*'] ?? [];
                    if (!empty($deposit) && isset($deposit['deposit_value'])) {
                        $transactionDepositAmount = amount_format($orderSettlement['amount'] * $deposit['deposit_value'] / 100, 2);
                        $settleDepositAmount      = amount_format($orderSettlement['settle_amount'] * $deposit['deposit_value'] / 100, 2);

                        $settleDetails[] = [
                            'business_id'         => $orderSettlement['business_id'],
                            'merchant_id'         => $orderSettlement['merchant_id'],
                            'merchant_name'       => $orderSettlement['merchant_name'],
                            'related_id'          => $orderSettlement['order_id'],
                            'order_number'        => $orderSettlement['order_number'],
                            'channel_id'          => $orderSettlement['channel_id'],
                            'channel_supplier_id' => $supplierId,
                            'settle_currency'     => $orderSettlement['settle_currency'],
                            'settle_amount'       => amount_format((-1) * $settleDepositAmount),
                            'currency'            => $orderSettlement['currency'],
                            'amount'              => amount_format((-1) * $transactionDepositAmount),
                            'rate'                => $transactionDepositAmount == '0.00' ? '1.0000' : amount_format($settleDepositAmount / $transactionDepositAmount, 4),
                            'amount_type'         => SettleDetail::AMOUNT_TYPE_22,
                            'order_complete_at'   => $orderSettlement['completed_at'],
                            'settle_at'           => $date,
                            'created_at'          => date_create(),
                            'remarks'             => ''
                        ];

                        $settleDetails[] = [
                            'business_id'         => $orderSettlement['business_id'],
                            'merchant_id'         => $orderSettlement['merchant_id'],
                            'merchant_name'       => $orderSettlement['merchant_name'],
                            'related_id'          => $orderSettlement['order_id'],
                            'order_number'        => $orderSettlement['order_number'],
                            'channel_id'          => $orderSettlement['channel_id'],
                            'channel_supplier_id' => $supplierId,
                            'settle_currency'     => $orderSettlement['settle_currency'],
                            'settle_amount'       => $settleDepositAmount,
                            'currency'            => $orderSettlement['currency'],
                            'amount'              => $transactionDepositAmount,
                            'rate'                => $transactionDepositAmount == '0.00' ? '1.0000' : amount_format($settleDepositAmount / $transactionDepositAmount, 4),
                            'amount_type'         => SettleDetail::AMOUNT_TYPE_22,
                            'order_complete_at'   => $orderSettlement['completed_at'],
                            'settle_at'           => $orderSettlement['deposit_return_at'],
                            'created_at'          => date_create(),
                            'remarks'             => ''
                        ];
                    }
                }
                break;
            case OrderSettlement::TYPE_REFUND:
                if ($orderSettlement['status'] == OrderSettlement::STATUS_APPROVED) {
                    $settleDetails[] = [
                        'business_id'         => $orderSettlement['business_id'],
                        'merchant_id'         => $orderSettlement['merchant_id'],
                        'merchant_name'       => $orderSettlement['merchant_name'],
                        'related_id'          => $orderSettlement['order_id'],
                        'order_number'        => $orderSettlement['order_number'],
                        'channel_id'          => $orderSettlement['channel_id'],
                        'channel_supplier_id' => $supplierId,
                        'settle_currency'     => $orderSettlement['settle_currency'],
                        'settle_amount'       => amount_format($type * $orderSettlement['settle_amount']),
                        'currency'            => $orderSettlement['currency'],
                        'amount'              => amount_format($type * $orderSettlement['amount']),
                        'rate'                => $orderSettlement['rate'],
                        'amount_type'         => SettleDetail::AMOUNT_TYPE_30,
                        'order_complete_at'   => $orderSettlement['completed_at'],
                        'settle_at'           => $date,
                        'created_at'          => date_create(),
                        'remarks'             => ''
                    ];

                    // 退款处理费
                    $settleDetails[] = [
                        'business_id'         => $orderSettlement['business_id'],
                        'merchant_id'         => $orderSettlement['merchant_id'],
                        'merchant_name'       => $orderSettlement['merchant_name'],
                        'related_id'          => $orderSettlement['order_id'],
                        'order_number'        => $orderSettlement['order_number'],
                        'channel_id'          => $orderSettlement['channel_id'],
                        'channel_supplier_id' => $supplierId,
                        'settle_currency'     => $chargeCurrency,
                        'settle_amount'       => amount_format((-1) * $this->businessHistoriesList[$orderSettlement['business_history_id']]['charge_cc']['refund_fee'] * $chargeRate),
                        'currency'            => 'USD',
                        'amount'              => amount_format((-1) * $this->businessHistoriesList[$orderSettlement['business_history_id']]['charge_cc']['refund_fee']),
                        'rate'                => $chargeRate,
                        'amount_type'         => SettleDetail::AMOUNT_TYPE_31,
                        'order_complete_at'   => $orderSettlement['completed_at'],
                        'settle_at'           => $date,
                        'created_at'          => date_create(),
                        'remarks'             => ''
                    ];

                    // 退款-返还原始手续费
                    // 针对当前遍历 只返回一次该订单的手续费结算明细（多次退款同一次结算的情况）
                    $refundReturnFee = json_decode($this->businessHistoriesList[$orderSettlement['business_history_id']]['refund_return_fee'], true);
                    if (!is_null($refundReturnFee)) {
                        // 是否开启退款退费
                        if (count($refundReturnFee) > 0) {
                            // 判断是否是全额退款
                            $flag          = false;
                            $orderOriginal = OrderSettlement::where('order_id', $orderSettlement['parent_order_id'])
                                ->where('status', OrderSettlement::STATUS_APPROVED)
                                ->whereIn('type', [OrderSettlement::TYPE_SALE, OrderSettlement::TYPE_CAPTURE])
                                ->with('order')->first();

                            if ($orderOriginal['amount'] == $orderSettlement['amount']) { // 单次退款额 是否=订单交易额
                                $flag = true;
                            }

                            // 是否全额退款
                            if ($flag) {
                                // 是否开启退还比例手续费,并且此次循环此笔订单没有退比例手续费
                                if (in_array(MerchantBusiness::REFUND_RETURN_RATE_FEE, $refundReturnFee) && !isset($this->refundHandleRateFeeDetail[$orderSettlement['parent_order_id']])) {
                                    $data = [
                                        'supplierId' => $supplierId,
                                    ];

                                    $this->_handleFeeSettleDetail($settleDetails, $orderSettlement, $date, MerchantBusiness::REFUND_RETURN_RATE_FEE, $data, [SettleDetail::AMOUNT_TYPE_01, SettleDetail::AMOUNT_TYPE_21], '全额退款退还原始交易比例手续费', $orderOriginal);
                                    $this->refundHandleRateFeeDetail[$orderSettlement['parent_order_id']] = OrderSettlement::TYPE_REFUND;
                                }

                                // 是否开启退还单笔手续费,并且此次循环此笔订单没有退单笔手续费
                                if (in_array(MerchantBusiness::REFUND_RETURN_FEE, $refundReturnFee) && !isset($this->refundHandleFeeDetail[$orderSettlement['parent_order_id']])) {
                                    $data = [
                                        'isSettle'       => $isSettle,
                                        'supplierId'     => $supplierId,
                                        'chargeCurrency' => $chargeCurrency,
                                        'chargeRate'     => $chargeRate
                                    ];
                                    $this->_handleFeeSettleDetail($settleDetails, $orderSettlement, $date, MerchantBusiness::REFUND_RETURN_FEE, $data, [SettleDetail::AMOUNT_TYPE_02, SettleDetail::AMOUNT_TYPE_10], '全额退款退还原始交易单笔手续费', $orderOriginal);
                                    $this->refundHandleFeeDetail[$orderSettlement['parent_order_id']] = OrderSettlement::TYPE_REFUND;
                                }
                            }
                        }
                    }
                }
                break;
            case OrderSettlement::TYPE_CHARGEBACK:
                $settleDetails[] = [
                    'business_id'         => $orderSettlement['business_id'],
                    'merchant_id'         => $orderSettlement['merchant_id'],
                    'merchant_name'       => $orderSettlement['merchant_name'],
                    'related_id'          => $orderSettlement['order_id'],
                    'order_number'        => $orderSettlement['order_number'],
                    'channel_id'          => $orderSettlement['channel_id'],
                    'channel_supplier_id' => $supplierId,
                    'settle_currency'     => $orderSettlement['settle_currency'],
                    'settle_amount'       => amount_format($type * $orderSettlement['settle_amount']),
                    'currency'            => $orderSettlement['currency'],
                    'amount'              => amount_format($type * $orderSettlement['amount']),
                    'rate'                => $orderSettlement['rate'],
                    'amount_type'         => SettleDetail::AMOUNT_TYPE_40,
                    'order_complete_at'   => $orderSettlement['completed_at'],
                    'settle_at'           => $date,
                    'created_at'          => date_create(),
                    'remarks'             => ''
                ];

                // 拒付处理费
                $settleFee       = $orderSettlement['settle_amount'] == 0 ? '0.00' : $this->businessHistoriesList[$orderSettlement['business_history_id']]['charge_cc']['dishonour_fee'];
                $settleDetails[] = [
                    'business_id'         => $orderSettlement['business_id'],
                    'merchant_id'         => $orderSettlement['merchant_id'],
                    'merchant_name'       => $orderSettlement['merchant_name'],
                    'related_id'          => $orderSettlement['order_id'],
                    'order_number'        => $orderSettlement['order_number'],
                    'channel_id'          => $orderSettlement['channel_id'],
                    'channel_supplier_id' => $supplierId,
                    'settle_currency'     => $chargeCurrency,
                    'settle_amount'       => amount_format((-1) * $settleFee * $chargeRate),
                    'currency'            => 'USD',
                    'amount'              => amount_format((-1) * $this->businessHistoriesList[$orderSettlement['business_history_id']]['charge_cc']['dishonour_fee']),
                    'rate'                => $chargeRate,
                    'amount_type'         => SettleDetail::AMOUNT_TYPE_41,
                    'order_complete_at'   => $orderSettlement['completed_at'],
                    'settle_at'           => $date,
                    'created_at'          => date_create(),
                    'remarks'             => ''
                ];

                // 拒付-返还原始手续费
                // 针对当前遍历 只返回一次该订单的手续费结算明细（多次退款同一次结算的情况）
                $chargebackReturnFee = json_decode($this->businessHistoriesList[$orderSettlement['business_history_id']]['chargeback_return_fee'], true);

                if (!is_null($chargebackReturnFee)) {
                    // 是否开启拒付退费
                    if (count($chargebackReturnFee) > 0) {
                        // 判断是否是拒付退款
                        $flag          = false;
                        $orderOriginal = OrderSettlement::where('order_id', $orderSettlement['parent_order_id'])
                        ->where('status', OrderSettlement::STATUS_APPROVED)
                        ->whereIn('type', [OrderSettlement::TYPE_SALE, OrderSettlement::TYPE_CAPTURE])
                        ->with('order')->first();

                        if ($orderOriginal['amount'] == $orderSettlement['amount']) { // 单次拒付额 是否=订单交易额
                            $flag = true;
                        }

                        // 是否全额拒付
                        if ($flag) {
                            // 是否开启退还比例手续费
                            if (in_array(MerchantBusiness::CHARGEBACK_RETURN_RATE_FEE, $chargebackReturnFee) && !isset($this->refundHandleRateFeeDetail[$orderSettlement['parent_order_id']])) {
                                $data = [
                                    'supplierId' => $supplierId,
                                ];
                                $this->_handleFeeSettleDetail($settleDetails, $orderSettlement, $date, MerchantBusiness::CHARGEBACK_RETURN_RATE_FEE, $data, [SettleDetail::AMOUNT_TYPE_01, SettleDetail::AMOUNT_TYPE_21], '全额拒付退还原始交易比例手续费', $orderOriginal);
                                $this->refundHandleRateFeeDetail[$orderSettlement['parent_order_id']] = OrderSettlement::TYPE_CHARGEBACK;
                            }

                            // 是否开启退还单笔手续费
                            if (in_array(MerchantBusiness::CHARGEBACK_RETURN_FEE, $chargebackReturnFee) && !isset($this->refundHandleFeeDetail[$orderSettlement['parent_order_id']])) {
                                $data = [
                                    'isSettle'       => $isSettle,
                                    'supplierId'     => $supplierId,
                                    'chargeCurrency' => $chargeCurrency,
                                    'chargeRate'     => $chargeRate
                                ];
                                $this->_handleFeeSettleDetail($settleDetails, $orderSettlement, $date, MerchantBusiness::CHARGEBACK_RETURN_FEE, $data, [SettleDetail::AMOUNT_TYPE_02, SettleDetail::AMOUNT_TYPE_10], '全额拒付退还原始交易单笔手续费', $orderOriginal);
                                $this->refundHandleFeeDetail[$orderSettlement['parent_order_id']] = OrderSettlement::TYPE_CHARGEBACK;
                            }
                        }
                    }
                }
                break;
            case OrderSettlement::TYPE_CHARGEBACK_REVERSAL:
                $settleDetails[] = [
                    'business_id'         => $orderSettlement['business_id'],
                    'merchant_id'         => $orderSettlement['merchant_id'],
                    'merchant_name'       => $orderSettlement['merchant_name'],
                    'related_id'          => $orderSettlement['order_id'],
                    'order_number'        => $orderSettlement['order_number'],
                    'channel_id'          => $orderSettlement['channel_id'],
                    'channel_supplier_id' => $supplierId,
                    'settle_currency'     => $orderSettlement['settle_currency'],
                    'settle_amount'       => amount_format($type * $orderSettlement['settle_amount']),
                    'currency'            => $orderSettlement['currency'],
                    'amount'              => amount_format($type * $orderSettlement['amount']),
                    'rate'                => $orderSettlement['rate'],
                    'amount_type'         => SettleDetail::AMOUNT_TYPE_42,
                    'order_complete_at'   => $orderSettlement['completed_at'],
                    'settle_at'           => $date,
                    'created_at'          => date_create(),
                    'remarks'             => ''
                ];

                //拒付申诉成功是否需要退回费用
                $chargebackReturnFee = json_decode($this->businessHistoriesList[$orderSettlement['business_history_id']]['chargeback_return_fee'], true);

                if (count($chargebackReturnFee) > 0) {
                    // 判断是否是拒付退款
                    $flag          = false;
                    $orderOriginal = OrderSettlement::where('order_id', $orderSettlement['parent_order_id'])
                    ->where('status', OrderSettlement::STATUS_APPROVED)
                    ->whereIn('type', [OrderSettlement::TYPE_SALE, OrderSettlement::TYPE_CAPTURE])
                    ->with('order')->first();

                    if ($orderOriginal['amount'] == $orderSettlement['amount']) { // 单次拒付额 是否=订单交易额
                        $flag = true;
                    }
                    // 是否全额拒付申请
                    if ($flag) {
                        if (in_array(MerchantBusiness::CHARGEBACK_RETURN_RATE_FEE, $chargebackReturnFee)) {
                            //本次循环是否有退款，并且退款类型不为拒付退款
                            if(!(isset($this->refundHandleRateFeeDetail[$orderSettlement['parent_order_id']]) && $this->refundHandleRateFeeDetail[$orderSettlement['parent_order_id']] === OrderSettlement::TYPE_REFUND)){
                                $data = [
                                    'supplierId' => $supplierId,
                                ];
                                $this->_handleAddFeeSettleDetail($settleDetails, $orderSettlement, $date, MerchantBusiness::CHARGEBACK_RETURN_RATE_FEE, $data, [SettleDetail::AMOUNT_TYPE_01, SettleDetail::AMOUNT_TYPE_21], $orderOriginal);
                            }
                        }
                        if (in_array(MerchantBusiness::CHARGEBACK_RETURN_FEE, $chargebackReturnFee)) {
                            //本次循环是否有退款，并且退款类型不为拒付退款
                            if(!(isset($this->refundHandleFeeDetail[$orderSettlement['parent_order_id']]) && $this->refundHandleFeeDetail[$orderSettlement['parent_order_id']] === OrderSettlement::TYPE_REFUND)){
                                $data = [
                                    'isSettle'       => $isSettle,
                                    'supplierId'     => $supplierId,
                                    'chargeCurrency' => $chargeCurrency,
                                    'chargeRate'     => $chargeRate
                                ];
                                $this->_handleAddFeeSettleDetail($settleDetails, $orderSettlement, $date, MerchantBusiness::CHARGEBACK_RETURN_FEE, $data, [SettleDetail::AMOUNT_TYPE_02, SettleDetail::AMOUNT_TYPE_10], $orderOriginal);
                            }
                        }
                    }
                }
                break;
        }
    }

    /**
     * 根据订单类型查询退款条数
     * @param [type] $parentOrderSettlement
     * @param array $returnFeeType
     * @param array $amountType
     * @return array
     */
    private function _getSettleDetailTemps($parentOrderSettlement, string $returnFeeType, array $amountType):array{
        //根据订单类型查询退款条数
        if ($parentOrderSettlement['order']['type'] == OrderModel::TYPES_SALE) {
            return SettleDetail::where('related_id', $parentOrderSettlement['order_id'])
            ->whereIn('amount_type', $amountType)->get()->toArray();
        } else {
            //退比例
            if (in_array($returnFeeType, [MerchantBusiness::REFUND_RETURN_RATE_FEE, MerchantBusiness::CHARGEBACK_RETURN_RATE_FEE])) {
                return SettleDetail::where('related_id', $parentOrderSettlement['order_id'])
                ->whereIn('amount_type', $amountType)->get()->toArray();
            } else {
                return SettleDetail::where('related_id', $parentOrderSettlement['parent_order_id'])
                ->whereIn('amount_type', $amountType)->get()->toArray();
            }
        }
        return [];
    }

    /**
     * 拒付申请成功重新收取费用
     * @param [type] $settleDetails
     * @param [type] $orderSettlement
     * @param [type] $date
     * @param string $returnFeeType
     * @param [type] $data
     * @param [array] $amountType
     * @return void
     */
    private function _handleAddFeeSettleDetail(&$settleDetails, $orderSettlement, $date, string $returnFeeType, $data, $amountType, $parentOrderSettlement): void
    {
        $relation = OrderRelation::select('is_refund')->where('order_id',$parentOrderSettlement->order_id)->first();
        if($relation->is_refund == OrderRelation::IS_REFUND_FULL){
            return;
        }

        $totalAmount       = 0;
        $settleDetailTemps = $this->_getSettleDetailTemps($parentOrderSettlement, $returnFeeType, $amountType);

        foreach ($settleDetailTemps as $settleDetailTemp) {
            $totalAmount += $settleDetailTemp['amount'];
            if ($settleDetailTemp['amount'] < 0) {
                $template = $settleDetailTemp;
            }
        }

        if ($totalAmount >= 0) {
            if (isset($template)) { //获取以前收费的记录
                // 根据数据库原收手续费结算详情 生成手续费退费明细
                $settleDetails[] = [
                    'business_id'         => $template['business_id'],
                    'merchant_id'         => $template['merchant_id'],
                    'merchant_name'       => $template['merchant_name'],
                    'related_id'          => $template['related_id'],
                    'order_number'        => $template['order_number'],
                    'channel_id'          => $template['channel_id'],
                    'channel_supplier_id' => $template['channel_supplier_id'],
                    'settle_currency'     => $template['settle_currency'],
                    'currency'            => $template['currency'],
                    'rate'                => $template['rate'],
                    'amount_type'         => $template['amount_type'],
                    'order_complete_at'   => $orderSettlement['completed_at'],
                    'settle_amount'       => $template['settle_amount'],
                    'amount'              => $template['amount'],
                    'settle_at'           => $date,
                    'created_at'          => date_create(),
                    'remarks'             => in_array($template['amount_type'], [SettleDetail::AMOUNT_TYPE_02, SettleDetail::AMOUNT_TYPE_10]) ? '拒付申诉成功重新收取原始交易单笔手续费' : '拒付申诉成功重新收取原始交易比例手续费',
                ];
                return;
            }
        } else {
            //只有一条收款记录，拒付和拒付申诉在同一天
            if (count($settleDetailTemps) === 1 && isset($template)) {
                $settleDetails[] = [
                    'business_id'         => $template['business_id'],
                    'merchant_id'         => $template['merchant_id'],
                    'merchant_name'       => $template['merchant_name'],
                    'related_id'          => $template['related_id'],
                    'order_number'        => $template['order_number'],
                    'channel_id'          => $template['channel_id'],
                    'channel_supplier_id' => $template['channel_supplier_id'],
                    'settle_currency'     => $template['settle_currency'],
                    'currency'            => $template['currency'],
                    'rate'                => $template['rate'],
                    'amount_type'         => $template['amount_type'],
                    'order_complete_at'   => $orderSettlement['completed_at'],
                    'settle_amount'       => $template['settle_amount'],
                    'amount'              => $template['amount'],
                    'settle_at'           => $date,
                    'created_at'          => date_create(),
                    'remarks'             => in_array($template['amount_type'], [SettleDetail::AMOUNT_TYPE_02, SettleDetail::AMOUNT_TYPE_10]) ? '拒付申诉成功重新收取原始交易单笔手续费' : '拒付申诉成功重新收取原始交易比例手续费',
                ];
                return;
            }
        }

        if ($returnFeeType === MerchantBusiness::CHARGEBACK_RETURN_RATE_FEE) {
            $detailData                     = $this->_calculateHandleFeeDetail($parentOrderSettlement, $date, $data, '拒付申诉成功重新收取原始交易比例手续费');
            $detailData['settle_amount']    = amount_format((-1) * $detailData['settle_amount']);
            $detailData['amount']           = amount_format((-1) * $detailData['amount']);
            $settleDetails[]                = $detailData;
        } else {
            // 使用 当前BID交易计费
            // 获取原订单交易类型
            $orderType = $parentOrderSettlement->type;
            if (!is_null($orderType)) {
                // sale处理费
                if (!$data['isSettle'] && $orderType == OrderModel::TYPES_SALE) {
                    $settleDetails[] = [
                        'business_id'         => $orderSettlement['business_id'],
                        'merchant_id'         => $orderSettlement['merchant_id'],
                        'merchant_name'       => $orderSettlement['merchant_name'],
                        'related_id'          => $parentOrderSettlement['order_id'],
                        'order_number'        => $orderSettlement['order_number'],
                        'channel_id'          => $orderSettlement['channel_id'],
                        'channel_supplier_id' => $data['supplierId'],
                        'settle_currency'     => $data['chargeCurrency'],
                        'settle_amount'       => amount_format((-1) * $this->businessHistoriesList[$orderSettlement['business_history_id']]['charge_cc']['transaction_fee'] * $data['chargeRate']),
                        'currency'            => 'USD',
                        'amount'              => amount_format((-1) * $this->businessHistoriesList[$orderSettlement['business_history_id']]['charge_cc']['transaction_fee']),
                        'rate'                => $data['chargeRate'],
                        'amount_type'         => SettleDetail::AMOUNT_TYPE_02,
                        'order_complete_at'   => $orderSettlement['completed_at'],
                        'settle_at'           => $date,
                        'created_at'          => date_create(),
                        'remarks'             => '拒付申诉成功重新收取原始交易单笔手续费'
                    ];
                }
                // auth处理费
                if (!$data['isSettle'] && $orderType == OrderModel::TYPES_CAPTURE) {
                    $settleDetails[] = [
                        'business_id'         => $orderSettlement['business_id'],
                        'merchant_id'         => $orderSettlement['merchant_id'],
                        'merchant_name'       => $orderSettlement['merchant_name'],
                        'related_id'          => $parentOrderSettlement['parent_order_id'],
                        'order_number'        => $orderSettlement['order_number'],
                        'channel_id'          => $orderSettlement['channel_id'],
                        'channel_supplier_id' => $data['supplierId'],
                        'settle_currency'     => $data['chargeCurrency'],
                        'settle_amount'       => amount_format((-1) * $this->businessHistoriesList[$orderSettlement['business_history_id']]['charge_cc']['transaction_fee'] * $data['chargeRate']),
                        'currency'            => 'USD',
                        'amount'              => amount_format((-1) * $this->businessHistoriesList[$orderSettlement['business_history_id']]['charge_cc']['transaction_fee']),
                        'rate'                => $data['chargeRate'],
                        'amount_type'         => SettleDetail::AMOUNT_TYPE_10,
                        'order_complete_at'   => $orderSettlement['completed_at'],
                        'settle_at'           => $date,
                        'created_at'          => date_create(),
                        'remarks'             => '拒付申诉成功重新收取原始交易单笔手续费'
                    ];
                }
            }
        }
    }

    /**
     * 生成需要退还的手续费明细
     * @param [type] $settleDetails             结算详情
     * @param [type] $orderSettlement           订单结算列
     * @param [type] $date
     * @param [type] $returnFeeType             退款类型
     * @param [type] $data
     * @param [array] $amountType               金额类型
     * @param [string] $remarks                 备注
     * @param [string] $parentOrderSettlement   原始订单结算记录
     * @return void
     */
    private function _handleFeeSettleDetail(&$settleDetails, $orderSettlement, $date, $returnFeeType, $data, array $amountType, string $remarks, $parentOrderSettlement): void
    {
        $totalAmount       = 0;
        $settleDetailTemps = $this->_getSettleDetailTemps($parentOrderSettlement, $returnFeeType, $amountType);

        foreach ($settleDetailTemps as $settleDetailTemp) {
            $totalAmount += $settleDetailTemp['amount'];
            if ($settleDetailTemp['amount'] < 0) {
                $template = $settleDetailTemp;
            }
        }
        //退款记录多一条,或同时有一条退款记录一条收款记录
        if ($totalAmount > 0 || ($totalAmount == 0 && count($settleDetailTemps) !== 0)) {
            return;
        }
        //只有一条记录且为收款记录
        if ($totalAmount < 0) {
            if (isset($template)) {
                $settleDetails[]    = [
                    'business_id'         => $template['business_id'],
                    'merchant_id'         => $template['merchant_id'],
                    'merchant_name'       => $template['merchant_name'],
                    'related_id'          => $template['related_id'],
                    'order_number'        => $template['order_number'],
                    'channel_id'          => $template['channel_id'],
                    'channel_supplier_id' => $template['channel_supplier_id'],
                    'settle_currency'     => $template['settle_currency'],
                    'currency'            => $template['currency'],
                    'rate'                => $template['rate'],
                    'amount_type'         => $template['amount_type'],
                    'order_complete_at'   => $orderSettlement['completed_at'],
                    'settle_amount'       => amount_format((-1) * $template['settle_amount']),
                    'amount'              => amount_format((-1) * $template['amount']),
                    'settle_at'           => $date,
                    'created_at'          => date_create(),
                    'remarks'             => $remarks
                ];
                return;
            }
        }

        //无退款记录也没有收款记录
        switch ($returnFeeType) {
            case MerchantBusiness::REFUND_RETURN_RATE_FEE:
                // 手动计算本订单收手续费的详情 生成比例手续费退费明细
                if ($parentOrderSettlement) {
                    $settleDetails[] = $this->_calculateHandleFeeDetail($parentOrderSettlement, $date, $data, $remarks);
                } else {
                    $orderSettlementLogStr = '退款退费原始交易数据(OrderSettlement)查询不到：退款(%s),订单号(%s),原始订单号(%s)';
                    $this->log->info(sprintf($orderSettlementLogStr, implode("|", [$orderSettlement['amount'], $orderSettlement['settle_amount']]), $orderSettlement['order_id'], $orderSettlement['parent_order_id']));
                    dispatch(new SendSlsLog(
                        ['message' => sprintf($orderSettlementLogStr, implode("|", [$orderSettlement['amount'], $orderSettlement['settle_amount']]), $orderSettlement['order_id'], $orderSettlement['parent_order_id'])],
                        [],
                        'info',
                        'task'
                    ));
                }
                break;
            case MerchantBusiness::CHARGEBACK_RETURN_RATE_FEE:
                // 手动计算本订单收手续费的详情 生成比例手续费退费明细
                if ($parentOrderSettlement) {
                    $settleDetails[] = $this->_calculateHandleFeeDetail($parentOrderSettlement, $date, $data, $remarks);
                } else {
                    $orderSettlementLogStr = '拒付退费原始交易数据(OrderSettlement)查询不到：退款(%s),订单号(%s),原始订单号(%s)';
                    $this->log->info(sprintf($orderSettlementLogStr, implode("|", [$orderSettlement['amount'], $orderSettlement['settle_amount']]), $orderSettlement['order_id'], $orderSettlement['parent_order_id']));
                    dispatch(new SendSlsLog(
                        ['message' => sprintf($orderSettlementLogStr, implode("|", [$orderSettlement['amount'], $orderSettlement['settle_amount']]), $orderSettlement['order_id'], $orderSettlement['parent_order_id'])],
                        [],
                        'info',
                        'task'
                    ));

                }
                break;
            case MerchantBusiness::REFUND_RETURN_FEE:

            case MerchantBusiness::CHARGEBACK_RETURN_FEE:
                // 使用 当前BID交易计费
                // 获取原订单交易类型
                $orderType = $parentOrderSettlement->type;
                if (!is_null($orderType)) {
                    // sale处理费
                    if (!$data['isSettle'] && $orderType == OrderModel::TYPES_SALE) {
                        $settleDetails[] = [
                            'business_id'         => $orderSettlement['business_id'],
                            'merchant_id'         => $orderSettlement['merchant_id'],
                            'merchant_name'       => $orderSettlement['merchant_name'],
                            'related_id'          => $parentOrderSettlement['order_id'],
                            'order_number'        => $orderSettlement['order_number'],
                            'channel_id'          => $orderSettlement['channel_id'],
                            'channel_supplier_id' => $data['supplierId'],
                            'settle_currency'     => $data['chargeCurrency'],
                            'settle_amount'       => amount_format((1) * $this->businessHistoriesList[$orderSettlement['business_history_id']]['charge_cc']['transaction_fee'] * $data['chargeRate']),
                            'currency'            => 'USD',
                            'amount'              => amount_format((1) * $this->businessHistoriesList[$orderSettlement['business_history_id']]['charge_cc']['transaction_fee']),
                            'rate'                => $data['chargeRate'],
                            'amount_type'         => SettleDetail::AMOUNT_TYPE_02,
                            'order_complete_at'   => $orderSettlement['completed_at'],
                            'settle_at'           => $date,
                            'created_at'          => date_create(),
                            'remarks'             => $remarks
                        ];
                    }
                    // auth处理费
                    if (!$data['isSettle'] && $orderType == OrderModel::TYPES_CAPTURE) {
                        $settleDetails[] = [
                            'business_id'         => $orderSettlement['business_id'],
                            'merchant_id'         => $orderSettlement['merchant_id'],
                            'merchant_name'       => $orderSettlement['merchant_name'],
                            'related_id'          => $parentOrderSettlement['parent_order_id'],
                            'order_number'        => $orderSettlement['order_number'],
                            'channel_id'          => $orderSettlement['channel_id'],
                            'channel_supplier_id' => $data['supplierId'],
                            'settle_currency'     => $data['chargeCurrency'],
                            'settle_amount'       => amount_format((1) * $this->businessHistoriesList[$orderSettlement['business_history_id']]['charge_cc']['transaction_fee'] * $data['chargeRate']),
                            'currency'            => 'USD',
                            'amount'              => amount_format((1) * $this->businessHistoriesList[$orderSettlement['business_history_id']]['charge_cc']['transaction_fee']),
                            'rate'                => $data['chargeRate'],
                            'amount_type'         => SettleDetail::AMOUNT_TYPE_10,
                            'order_complete_at'   => $orderSettlement['completed_at'],
                            'settle_at'           => $date,
                            'created_at'          => date_create(),
                            'remarks'             => $remarks
                        ];
                    }
                }
                break;
        }
    }

    /**
     * 根据退款orderSettlement，单独计算未入库的手续费明细
     * @param $settleDetails
     * @param $date
     * @param $data
     * @param $remarks
     * @return array|void
     */
    private function _calculateHandleFeeDetail($orderSettlement, $date, $data, $remarks)
    {
        $business = $this->businessHistoriesList[$orderSettlement['business_history_id']] ?? [];

        if (empty($business) || !isset($business['charge_cc']['transaction_fee_type'])) {
            $this->log->error(sprintf('%s:BID信息不存在', $orderSettlement['order_id']));
            dispatch(new SendSlsLog(
                ['message' => sprintf('%s:BID信息不存在', $orderSettlement['order_id'])],
                [],
                'error',
                'task'
            ));

            return;
        }

        $rate = $this->getCommissionRatio($orderSettlement);

        // 金额类型
        $amountType = $orderSettlement['type'] == OrderSettlement::TYPE_SALE ? SettleDetail::AMOUNT_TYPE_01 : SettleDetail::AMOUNT_TYPE_21;

        // 组装手续费明细返回
        $transactionRateFee = amount_format($orderSettlement['amount'] * $rate / 100, 2);
        $settleRateFee      = amount_format($orderSettlement['settle_amount'] * $rate / 100, 2);

        return [
            'business_id'         => $orderSettlement['business_id'],
            'merchant_id'         => $orderSettlement['merchant_id'],
            'merchant_name'       => $orderSettlement['merchant_name'],
            'related_id'          => $orderSettlement['order_id'],
            'order_number'        => $orderSettlement['order_number'],
            'channel_id'          => $orderSettlement['channel_id'],
            'channel_supplier_id' => $data['supplierId'],
            'settle_currency'     => $orderSettlement['settle_currency'],
            'settle_amount'       => $settleRateFee,
            'amount'              => $transactionRateFee,
            'currency'            => $orderSettlement['currency'],
            'rate'                => $transactionRateFee == '0.00' ? '1.0000' : amount_format($settleRateFee / $transactionRateFee, 4),
            'amount_type'         => $amountType,
            'order_complete_at'   => $orderSettlement['completed_at'],
            'settle_at'           => $date,
            'created_at'          => date_create(),
            'remarks'             => $remarks
        ];
    }

    /**
     * 获取比例手续费
     * @param $orderSettlement
     * @return mixed
     */
    public function getCommissionRatio($orderSettlement){
        $card    = OrderCard::find($orderSettlement['order']['card_id']);
        $ccType  = $card->cc_type ?? '*';
        $bin     = isset($card->card_mask) ? substr($card->card_mask, 0, 6) : '0';
        $binbase = DirectoryBinbase::firstWhere('bin', $bin);

        if (empty($binbase)) {
            $regionName = '*';
        } else {
            $region = DirectoryCountry::with('region')->where('isoa2', $binbase->isoa2)->first();

            if (!empty($region)) {
                $region->region->sortBy('sort')->first();
                $regionName = $region->name ?? '*';
            } else {
                $regionName = '*';
            }
        }

        // 比例手续费
        $rateList = $this->rateFeeList[$orderSettlement['business_history_id']][$regionName] ?? $this->rateFeeList[$orderSettlement['business_history_id']]['*'];
        return $rateList[$ccType] ?? $rateList['*'] ?? $this->rateFeeList[$orderSettlement['business_history_id']]['*']['*'];
    }

    /**
     * 填充历史条款和手续费数据
     * @param array $HistoriesIds
     * @return void
     */
    public function fillBusinessHistoriesListAndRateFeeList(array $HistoriesIds = []): void
    {
        $selectIds = array_diff_key($HistoriesIds, $this->businessHistoriesList);

        if (count($selectIds)) {
            MerchantBusinessHistory::whereIn('id', array_keys($selectIds))->get()->each(function ($item) {
                $item = $item->toArray();
                //处理保证金数据
                if (!empty($item['deposit'])) {
                    $tempDeposit = [];
                    foreach ($item['deposit'] as $deposit) {
                        $tempDeposit[$deposit['deposit_type']][$deposit['deposit_card']] = $deposit;
                    }

                    $item['deposit'] = $tempDeposit;
                }
                $this->businessHistoriesList[$item['id']] = $item;
                // 比例手续费
                foreach ($item['charge_rate_ccs'] as $chargeRate) {
                    $this->rateFeeList[$item['id']][$chargeRate['region']][$chargeRate['cc_type']] = $chargeRate['transaction_rate'];
                }
            });
        }
    }
}
