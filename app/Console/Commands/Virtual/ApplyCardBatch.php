<?php

namespace App\Console\Commands\Virtual;

use App\Classes\Supports\Log;
use App\Classes\Supports\Logger;
use App\Models\CardBatch;
use App\Models\CardVirtual;
use App\Models\MerchantApiNoticeTask;
use App\Services\VirtualControllerService;
use Illuminate\Console\Command;
use Illuminate\Database\Eloquent\Builder;
use App\Services\Virtual\TongLianService;

class ApplyCardBatch extends Command
{
	/**
	 * The name and signature of the console command.
	 *
	 * @var string
	 */
	protected $signature = 'card:applyCardBatch';

	/**
	 * The console command description.
	 *
	 * @var string
	 */
	protected $description = '卡批次申请-囤卡使用';

	/**
	 * Create a new command instance.
	 *
	 * @return void
	 */
	public function __construct()
	{
		parent::__construct();
		// 初始化日志
		$log    = new Logger();
		$config = ['file' => storage_path('logs/consoleCardTask.log')];
		$log->setConfig($config);
		Log::setInstance($log);
	}

	/**
	 * @throws \Exception
	 */
	public function handle()
	{
		$this->info("卡批次囤卡申请开始");
		$binCountMap  = [];
		$cardBatchArr = CardBatch::where('status', '=', CardBatch::PENDING)
			->whereHas('CardBin.CardBinSupplier', function (Builder $query) {
				$query->where('file_name', '<>', TongLianService::CHANNEL_SERVICE_CODE);
			})
			->orderBy('quantity')
			->get();
		$successCount = 0;
		foreach ($cardBatchArr as $cardBatch) {
			if (!isset($binCountMap[$cardBatch['card_bin_id']])) {
				$binCountMap[$cardBatch['card_bin_id']] = CardVirtual::where('card_bin_id', $cardBatch['card_bin_id'])->where('cards_id', '')->where('status', CardVirtual::ACTIVATION)->pluck('virtual_id')->toArray();
			}

			if (count($binCountMap[$cardBatch['card_bin_id']]) >= $cardBatch->quantity) {
				$virtualIds = array_splice($binCountMap[$cardBatch['card_bin_id']], 0, $cardBatch->quantity);
				$resultData = VirtualControllerService::HandleCardBatchAsyncApply($cardBatch->toArray(), $virtualIds);
				if ($resultData['isSuccess']) {
					$successCount += $resultData['successNum'];
					// 添加回调任务
					VirtualControllerService::createNoticeTask(
						$cardBatch['merchant_id'],
						MerchantApiNoticeTask::WEBHOOK_MESSAGE_TYPE_VIRTUAL_BATCH,
						[
							'code'    => '0000',
							'message' => '成功',
							'data'    => ['batch_id' => $cardBatch['batch_id'], 'batch_status' => CardBatch::$statusApiMap[CardBatch::PROCESS_SUCCESS]]
						]
					);
				}
			}
		}

		$this->info(sprintf("本次处理囤卡卡批次数量:%s", $successCount));
		$this->info('卡批次申请结束');
	}
}
