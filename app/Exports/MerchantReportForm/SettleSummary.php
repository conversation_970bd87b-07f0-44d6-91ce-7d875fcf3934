<?php

namespace App\Exports\MerchantReportForm;

use App\Services\ToolService;
use Illuminate\Support\Carbon;
use App\Models\SettleDetail;
use PhpOffice\PhpSpreadsheet\Cell\Cell;
use PhpOffice\PhpSpreadsheet\Cell\DataType;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;
use PhpOffice\PhpSpreadsheet\Style\Border;

trait SettleSummary
{

    protected $params;
    protected $sites;
    protected $lang;
	protected $title = [
		'en' 	=> 'MID Settlement Summary',
		'zh_CN' => 'MID当期结算汇总',
	];
    protected $fieldData = [
        'en'    => [
            'total'         => 'Total',
            'main_head_one' => [
                'Transaction Completion Date', 'Approved Transaction', '', '', '', '', '',
                'Refund', '', 'Chargeback', '', 'Merchant Discount Rate', '', '', '', '',
                'Settlement amount', 'Reserve Amount', 'Transaction Fee',
                'Refund Fee', 'Chargeback Fee', 'Chargeback Alert Fee', 'Chargeback Penalty', 'Chargeback Penalty *',
                'Threed Fee', 'Risk Fee', 'Other Fee',
                'Current transaction settlement amount', 'Reserve Release', 'Current Settlement amount',
            ],
            'main_head_two' => [
                '', 'Count', 'Visa amount', 'MC amount',
                'AE amount', 'DC amount', 'JCB amount',
                'Count', 'Amount', 'Count', 'Amount',
                'Visa', 'MC', 'AE', 'DC', 'JCB'
            ],
            'bond_head'     => [['Reserve Release'], ['Transaction Date', 'Reserve Release Amount']]
        ],
        'zh_CN' => [
            'total'         => '合计(total)',
            'main_head_one' => [
                '交易完成日期' . PHP_EOL . '(Transaction Completion Date)', '成功订单(Approved Transaction)', '', '', '', '', '',
                '退款(Refund)', '', '拒付(Chargeback)', '', '比例手续费(Merchant Discount Rate)', '', '', '', '',
                '结算金额' . PHP_EOL . '(Settlement amount)', '保证金' . PHP_EOL . '(Reserve Amount)', '单笔处理费' . PHP_EOL . '(Transaction Fee)',
                '退款处理费' . PHP_EOL . '(Refund Fee)', '拒付处理费' . PHP_EOL . '(Chargeback Fee)', '预拒付处理费' . PHP_EOL . '(Chargeback Alert Fee)' , '拒付罚金' . PHP_EOL . '(Chargeback Penalty)', '拒付罚金*' . PHP_EOL . '(Chargeback Penalty *)',
                '3D处理费' . PHP_EOL . '(Threed Fee)', '风控处理费' . PHP_EOL . '(Risk Fee)', '其他费用' . PHP_EOL . '(Other Fee)',
                '当期交易结算金额' . PHP_EOL . '(Current transaction settlement amount)', '保证金释放' . PHP_EOL . '(Reserve Release)', '当期到账金额' . PHP_EOL . '(Current Settlement amount)',
            ],
            'main_head_two' => [
                '', '笔数' . PHP_EOL . '(count)', 'V卡金额' . PHP_EOL . '(Visa amount)', 'M卡金额' . PHP_EOL . '(MC amount)',
                'A卡金额' . PHP_EOL . '(AE amount)', 'D卡金额' . PHP_EOL . '(DC amount)', 'J卡金额' . PHP_EOL . '(JCB amount)',
                '笔数' . PHP_EOL . '(count)', '金额' . PHP_EOL . '(amount)', '笔数' . PHP_EOL . '(count)', '金额' . PHP_EOL . '(amount)',
                'Visa', 'MC', 'AE', 'DC', 'JCB'
            ],
            'bond_head'     => [['保证金释放' . PHP_EOL . '(Reserve Release)'], ['交易完成日期' . PHP_EOL . '(Transaction Date)', '保证金释放金额' . PHP_EOL . '(Reserve Release Amount)']]
        ],
    ];

    public function __construct(array $params = [], string $lang = 'zh_CN')
    {
        $this->params = $params;
        $this->sites  = [];
        $this->lang   = $lang;
    }

    public function title(): string
    {
        return $this->title[$this->lang];
    }

    public function bindValue(Cell $cell, $value)
    {
        $cell->setValueExplicit($value, DataType::TYPE_STRING);

        return true;
    }

    public function array(): array
    {
        // 语言包
        $fieldData  = $this->fieldData[$this->lang];

        $mainHead[] = $fieldData['main_head_one'];
        $mainHead[] = $fieldData['main_head_two'];
        $bondHead   = $fieldData['bond_head'];

        //数据  //结算数据  //保证金释放数据
        $data = $mainData = $bondData = [];
        foreach ($this->params['query_criteria'] as $value) {
            //参数
            $nowDate = $value['str_at'];
            $endDate = date('Y-m-d', strtotime("{$value['end_at']} +1 days"));

            if (Carbon::tomorrow()->lt(Carbon::parse($endDate))) {
                $endDate = Carbon::tomorrow()->toDateString();
            }

            $ccTypeAmountMap        = ['V' => 'visaAmount', 'M' => 'mcAmount', 'A' => 'aeAmount', 'D' => 'dcAmount', 'J' => 'jcbAmount'];
            $ccTypeRateAmountMap    = ['V' => 'visaRateAmount', 'M' => 'mcRateAmount', 'A' => 'aeRateAmount', 'D' => 'dcRateAmount', 'J' => 'jcbRateAmount'];

            $basicFormat    = [
                //成功订单
                'successCount' => 0, 'visaAmount' => 0, 'mcAmount' => 0, 'aeAmount' => 0, 'dcAmount' => 0, 'jcbAmount' => 0,
                //退款
                'refundCount' => 0, 'refundAmount' => 0,
                //拒付
                'chargebackCaseCount' => 0, 'chargebackCaseAmount' => 0,
                //手续费
                'visaRateAmount' => 0, 'mcRateAmount' => 0, 'aeRateAmount' => 0, 'dcRateAmount' => 0, 'jcbRateAmount' => 0, 'settlementAmount' => 0,
                //保证金 单笔处理费 退款处理费 拒付处理费 预拒付处理费
                'rollingAmount' => 0, 'transactionFee' => 0, 'refundFee' => 0, 'chargebackFee' => 0,
                //预拒付处理费 拒付罚金 拒付罚金* 3D费用 风控费用 其他费用 当期交易结算金额
                'chargebackAlertFee' => 0, 'chargebackPenalty' => 0, 'chargebackPenaltySpecial' => 0, 'threedFee' => 0, 'riskFee' => 0, 'otherFee' => 0, 'withdrawalAmount' => 0,
                //保证金预计到账
                date('Y.m.d', strtotime("{$nowDate} -180 days")) . '-' . date('Y.m.d', strtotime("{$endDate} -181 days"))
            ];
            //结算开始时间小于当前结算时间
            while (Carbon::parse($nowDate)->lt(Carbon::parse($endDate))) {
                //获取结算明细
                $query = SettleDetail::query()->select('id', 'related_id', 'settle_currency', 'settle_amount', 'amount_type', 'order_complete_at', 'created_at')
                    ->where('business_id', $value['business_id'])
                    ->where('settle_at', $nowDate)
                    ->where('settle_amount', '<>', 0)
                    ->whereNotIn('amount_type', [SettleDetail::AMOUNT_TYPE_50, SettleDetail::AMOUNT_TYPE_51, SettleDetail::AMOUNT_TYPE_52])
                    ->with(['order:order_id,card_id', 'order.card:id,cc_type']);

                $query->chunkById(1000, static function ($successDetails) use (&$mainData, &$bondData, $basicFormat, $ccTypeAmountMap, $ccTypeRateAmountMap) {
                    foreach ($successDetails as $detail) {
                        $date       = substr($detail->order_complete_at, 0, 10);
                        $formatDate = strtotime($date);
                        if (!isset($mainData[$detail->settle_currency])) {
                            $mainData[$detail->settle_currency][$formatDate] = array_merge([$date], $basicFormat);
                        } else {
                            if (!isset($mainData[$detail->settle_currency][$formatDate])) {
                                $mainData[$detail->settle_currency][$formatDate] = array_merge([$date], $basicFormat);
                            }
                        }

                        switch ($detail->amount_type) {
                            case SettleDetail::AMOUNT_TYPE_00: //Capture金额
                            case SettleDetail::AMOUNT_TYPE_20: //Sale金额
                                $mainData[$detail->settle_currency][$formatDate]['successCount'] += 1;
                                if (isset($detail->order->card->cc_type)) {
                                    if (isset($ccTypeAmountMap[$detail->order->card->cc_type])) {
                                        $mainData[$detail->settle_currency][$formatDate][$ccTypeAmountMap[$detail->order->card->cc_type]] += $detail->settle_amount;
                                    }
                                }
                                break;
                            case SettleDetail::AMOUNT_TYPE_01: //Sale比例手续费
                            case SettleDetail::AMOUNT_TYPE_21: //Capture比例手续费
                                if (isset($detail->order->card->cc_type)) {
                                    if (isset($ccTypeRateAmountMap[$detail->order->card->cc_type])) {
                                        $mainData[$detail->settle_currency][$formatDate][$ccTypeRateAmountMap[$detail->order->card->cc_type]] += $detail->settle_amount;
                                    }
                                }
                                break;
                            case SettleDetail::AMOUNT_TYPE_02: //Sale单笔处理费
                            case SettleDetail::AMOUNT_TYPE_10: //Auth单笔处理费
                                $mainData[$detail->settle_currency][$formatDate]['transactionFee'] += $detail->settle_amount;
                                break;
                            case SettleDetail::AMOUNT_TYPE_03: //Sale保证金
                            case SettleDetail::AMOUNT_TYPE_22: //Capture保证金
                                if ($detail->settle_amount < 0) { //统计收取
                                    $mainData[$detail->settle_currency][$formatDate]['rollingAmount'] += $detail->settle_amount;
                                } else {
                                    $bondDate       = substr($detail->order_complete_at, 0, 10);
                                    $bondFormatDate = strtotime($bondDate);
                                    if (!isset($bondData[$detail->settle_currency])) {
                                        $bondData[$detail->settle_currency][$bondFormatDate] = [
                                            $bondDate, 'sumAmount' => $detail->settle_amount
                                        ];
                                    } else {
                                        if (!isset($bondData[$detail->settle_currency][$bondFormatDate])) {
                                            $bondData[$detail->settle_currency][$bondFormatDate] = [
                                                $bondDate, 'sumAmount' => $detail->settle_amount
                                            ];
                                        } else {
                                            $bondData[$detail->settle_currency][$bondFormatDate]['sumAmount'] += $detail->settle_amount;
                                        }
                                    }
                                }
                                break;
                            case SettleDetail::AMOUNT_TYPE_04: //Sale3d交易费
                            case SettleDetail::AMOUNT_TYPE_11: //Auth3d交易费
                                $mainData[$detail->settle_currency][$formatDate]['threedFee'] += $detail->settle_amount;
                                break;
                            case SettleDetail::AMOUNT_TYPE_05: //Sale风控处理费
                            case SettleDetail::AMOUNT_TYPE_12: //Auth风控处理费
                                $mainData[$detail->settle_currency][$formatDate]['riskFee'] += $detail->settle_amount;
                                break;
                            case SettleDetail::AMOUNT_TYPE_30: //退款金额
                                $mainData[$detail->settle_currency][$formatDate]['refundAmount'] += $detail->settle_amount;
                                $mainData[$detail->settle_currency][$formatDate]['refundCount'] += 1;
                                break;
                            case SettleDetail::AMOUNT_TYPE_31: //退款手续费
                                $mainData[$detail->settle_currency][$formatDate]['refundFee'] += $detail->settle_amount;
                                break;
                            case SettleDetail::AMOUNT_TYPE_40: //拒付金额
                                $mainData[$detail->settle_currency][$formatDate]['chargebackCaseAmount'] += $detail->settle_amount;
                                $mainData[$detail->settle_currency][$formatDate]['chargebackCaseCount'] += 1;
                                break;
                            case SettleDetail::AMOUNT_TYPE_41: //拒付处理费
                                $mainData[$detail->settle_currency][$formatDate]['chargebackFee'] += $detail->settle_amount;
                                break;
                            case SettleDetail::AMOUNT_TYPE_43: //预拒付处理费
                                $mainData[$detail->settle_currency][$formatDate]['chargebackAlertFee'] += $detail->settle_amount;
                                break;
                            case SettleDetail::AMOUNT_TYPE_44: //拒付罚金
                                $mainData[$detail->settle_currency][$formatDate]['chargebackPenalty'] += $detail->settle_amount;
                                break;
                            case SettleDetail::AMOUNT_TYPE_45: //拒付罚金*
                                $mainData[$detail->settle_currency][$formatDate]['chargebackPenaltySpecial'] += $detail->settle_amount;
                                break;
                            case SettleDetail::AMOUNT_TYPE_90: //结算调整
                                $mainData[$detail->settle_currency][$formatDate]['otherFee'] += $detail->settle_amount;
                                break;
                        }
                    }
                });
                $nowDate = date('Y-m-d', strtotime("{$nowDate} +1 days"));
            }
        }

        $headCount = count($mainHead[0]);
        $tail      = [$fieldData['total']];
        for ($i = 4; $i < $headCount; $i++) {
            $tail[] = '';
        }

        $currentSite = [];
        foreach ($mainData as $currency => $dates) {
            ksort($dates, SORT_NUMERIC);
            //格式化主表数据和保证金表数据
            $formateData = $formateBondData = [];
            //货币总交易结算金额
            $withdrawalTotalAmount = 0;
            foreach ($dates as $settlement) {
                //当期结算总金额
                $settlement['settlementAmount'] = $settlement['visaAmount'] + $settlement['mcAmount'] + $settlement['aeAmount'] + $settlement['dcAmount'] + $settlement['jcbAmount'] +
                    $settlement['visaRateAmount'] + $settlement['mcRateAmount'] + $settlement['aeRateAmount'] + $settlement['dcRateAmount'] + $settlement['jcbRateAmount'];
                //本日当期交易结算金额
                $settlement['withdrawalAmount'] = $settlement['settlementAmount'] + $settlement['rollingAmount'] + $settlement['transactionFee'] + $settlement['refundFee'] +
                    $settlement['chargebackFee'] + $settlement['chargebackAlertFee'] + $settlement['chargebackPenalty'] + $settlement['chargebackPenaltySpecial'] + $settlement['refundAmount'] + $settlement['chargebackCaseAmount'] + $settlement['threedFee'] + $settlement['riskFee'] + $settlement['otherFee'];
                //金额保留两位小数
                foreach ($settlement as &$amountData) {
                    if (gettype($amountData) === 'double') {
                        $amountData = amount_format($amountData);
                    }
                }

                //添加主表数据
                $formateData[]          = array_values($settlement);
                $withdrawalTotalAmount  += $settlement['withdrawalAmount'];
            }

            $mainData[$currency] = $formateData;

            //保证金数据处理
            $bondDataCount = $bondTotalAmount = 0;
            if (isset($bondData[$currency])) {
                ksort($bondData[$currency], SORT_NUMERIC);
                foreach ($bondData[$currency] as $bondDatas) {
                    //金额保留两位小数
                    foreach ($bondDatas as &$bondAmountData) {
                        if (gettype($bondAmountData) === 'double') {
                            $bondAmountData = amount_format($bondAmountData);
                        }
                    }

                    $formateBondData[]  = array_values($bondDatas);
                    $bondTotalAmount    += $bondDatas['sumAmount'];
                }

                $bondData[$currency]    = $formateBondData;
                $bondDataCount          = count($bondData[$currency]);
            }

            //风格位置拼接
            $mainCount = count($mainData[$currency]);
            if (count($currentSite)) {
                $currentSite['mainHeadOneSite'] = $currentSite['totalSite'] + 4;
                $currentSite['mainHeadTwoSite'] = $currentSite['totalSite'] + 5;
                $currentSite['tailSite'] = $currentSite['totalSite'] + 6 + $mainCount;
                $currentSite['bondHeadOneSite'] = $currentSite['totalSite'] + 8 + $mainCount;
                $currentSite['bondHeadTwoSite'] = $currentSite['totalSite'] + 9 + $mainCount;
                $currentSite['totalSite'] = $currentSite['totalSite'] + 9 + $mainCount + $bondDataCount;
            } else {
                $currentSite = [
                    'mainHeadOneSite' => 3, 'mainHeadTwoSite' => 4, 'tailSite' => 5 + $mainCount,
                    'bondHeadOneSite' => 7 + $mainCount, 'bondHeadTwoSite' => 8 + $mainCount, 'totalSite' => 8 + $mainCount + $bondDataCount,
                ];
            }
            $this->sites[] = $currentSite;

            //每张主表加表尾
            $mainTail = $tail;
            $mainTail[] = amount_format($withdrawalTotalAmount);
            $mainTail[] = amount_format($bondTotalAmount);
            $mainTail[] = amount_format($withdrawalTotalAmount + $bondTotalAmount);

            //拼接主表副表
            $data = array_merge(
                //结算主表
                $data,
                [[$currency]],
                $mainHead,
                $mainData[$currency],
                [$mainTail],
                [['']],
                //保证金附表
                $bondHead,
                $bondData[$currency] ?? [[]],
                [['']],
                [['']]
            );
        }
        return $data;
    }

    public function startCell(): string
    {
        return 'A2';
    }

    public function columnWidths(): array
    {
        return [
            'A' => 17,
            'B' => 17, 'C' => 14, 'D' => 14, 'E' => 14, 'F' => 14, 'G' => 14, 'H' => 14, 'I' => 14,
            'J' => 14, 'K' => 14, 'L' => 14, 'M' => 14, 'N' => 14, 'O' => 14, 'P' => 14,
            'Q' => 17, 'R' => 17, 'S' => 17, 'T' => 17, 'U' => 17, 'V' => 17, 'W' => 17, 'X' => 17, 'Y' => 17,
            'Z' => 23, 'AA' => 23, 'AB' => 23, 'AC' => 23, 'AD' => 24,
        ];
    }

    public function styles(Worksheet $sheet)
    {

        $maxEnd         = $sheet->getHighestColumn();
        $borderStyle    = ['borderStyle' => Border::BORDER_MEDIUM, 'color' => ['rgb' => '808080']];
        $alignmentStyle = ['horizontal' => 'center', 'vertical' => 'center'];
        $frameStyle     = ['borders' => ['bottom' => $borderStyle, 'left' => $borderStyle, 'top' => $borderStyle, 'right' => $borderStyle], 'alignment' => $alignmentStyle];

        $heads = ['A', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z', 'AA', 'AB', 'AC', 'AD'];
        foreach ($this->sites as $site) {
            //主要样式
            $sheet->getRowDimension($site['mainHeadTwoSite'])->setRowHeight(50);
            foreach ($heads as $key) {
                $sheet->mergeCells($key . $site['mainHeadOneSite'] . ':' . $key . $site['mainHeadTwoSite'])->getStyle($key . $site['mainHeadOneSite'] . ':' . $key . $site['mainHeadTwoSite'])->applyFromArray([
                    'borders' => ['bottom' => $borderStyle, 'left' => $borderStyle, 'top' => $borderStyle, 'right' => $borderStyle],
                    'alignment' => $alignmentStyle
                ])->getAlignment()->setWrapText(true);
                if (!in_array($key, ['R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y'])) {
                    if ($key == 'AC' && $site['mainHeadTwoSite'] != 0) {
                        $sheet->mergeCells('AC' . ($site['mainHeadTwoSite'] + 1) . ':AC' . ($site['tailSite'] - 1))->getStyle('AC' . ($site['mainHeadTwoSite'] + 1) . ':AC' . ($site['tailSite'] - 1))->applyFromArray($frameStyle)->getAlignment()->setWrapText(true);
                        $sheet->getStyle('AC' . $site['tailSite'])->applyFromArray($frameStyle);
                    } else {
                        $sheet->getStyle($key . $site['mainHeadOneSite'] . ':' . $key . ($site['tailSite'] - 1))->applyFromArray($frameStyle);
                    }
                } else {
                    $sheet->getStyle($key . $site['mainHeadOneSite'] . ':' . $key . ($site['tailSite'] - 1))->applyFromArray(['alignment' => $alignmentStyle]);
                }
            }

            $sheet->mergeCells('B' . $site['mainHeadOneSite'] . ':G' . $site['mainHeadOneSite'])->getStyle('B' . $site['mainHeadOneSite'] . ':G' . $site['mainHeadOneSite'])->applyFromArray($frameStyle);
            $sheet->mergeCells('L' . $site['mainHeadOneSite'] . ':P' . $site['mainHeadOneSite'])->getStyle('L' . $site['mainHeadOneSite'] . ':P' . $site['mainHeadOneSite'])->applyFromArray($frameStyle);

            $sheet->mergeCells('H' . $site['mainHeadOneSite'] . ':I' . $site['mainHeadOneSite'])->getStyle('H' . $site['mainHeadOneSite'] . ':I' . $site['mainHeadOneSite'])->applyFromArray($frameStyle);
            $sheet->mergeCells('J' . $site['mainHeadOneSite'] . ':K' . $site['mainHeadOneSite'])->getStyle('J' . $site['mainHeadOneSite'] . ':K' . $site['mainHeadOneSite'])->applyFromArray($frameStyle);
            for ($i = 1; $i < 16; $i++) {
                $sheet->getStyle(ToolService::stringFromColumnIndex($i) . $site['mainHeadTwoSite'])->applyFromArray($frameStyle)->getAlignment()->setWrapText(true);
            }
            $sheet->getStyle('B' . $site['mainHeadTwoSite'] . ':G' . $site['tailSite'])->applyFromArray($frameStyle);
            $sheet->getStyle('L' . $site['mainHeadOneSite'] . ':P' . $site['tailSite'])->applyFromArray($frameStyle);
            $sheet->getStyle('H' . $site['mainHeadOneSite'] . ':I' . $site['tailSite'])->applyFromArray($frameStyle);
            $sheet->getStyle('J' . $site['mainHeadOneSite'] . ':K' . $site['tailSite'])->applyFromArray($frameStyle);
            $sheet->getStyle('A' . $site['tailSite'] . ':' . $maxEnd . $site['tailSite'])->applyFromArray($frameStyle);

            $sheet->getStyle('Q' . $site['tailSite'])->applyFromArray($frameStyle);
            $sheet->getStyle('Z' . $site['tailSite'])->applyFromArray($frameStyle);

            //保证金样式
            $sheet->getRowDimension($site['bondHeadOneSite'])->setRowHeight(45);
            $sheet->mergeCells('A' . $site['bondHeadOneSite'] . ':B' . $site['bondHeadOneSite'])->getStyle('A' . $site['bondHeadOneSite'] . ':B' . $site['bondHeadOneSite'])->applyFromArray($frameStyle)->getAlignment()->setWrapText(true);
            $sheet->getStyle('A' . $site['bondHeadTwoSite'] . ':B' . $site['bondHeadTwoSite'])->applyFromArray($frameStyle)->getAlignment()->setWrapText(true);
            $sheet->getRowDimension($site['bondHeadTwoSite'])->setRowHeight(50);

            $sheet->getStyle('A' . $site['bondHeadTwoSite'] . ':A' . $site['totalSite'])->applyFromArray($frameStyle)->getAlignment()->setWrapText(true);
            $sheet->getStyle('B' . $site['bondHeadTwoSite'] . ':B' . $site['totalSite'])->applyFromArray($frameStyle)->getAlignment()->setWrapText(true);
        }
    }
}
