<?php

namespace App\Merchant\Controllers\Transfer;

use App\Jobs\SendNotice;
use App\Merchant\Actions\Tools\Transfer\CancelBatch;
use App\Merchant\Repositories\TransferTicket;
use App\Models\DirectoryCurrency;
use App\Models\Merchant;
use App\Models\TransferAccount;
use App\Models\TransferTicket as TransferTicketModel;
use App\Services\MerchantService;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Http\Controllers\AdminController;
use Illuminate\Support\Facades\Auth;
use App\Http\Requests\Request;
use Dcat\Admin\Admin;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;

class TicketController extends AdminController
{
    public function title()
    {
        return admin_trans_label('提现信息');
    }

    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        return Grid::make(new TransferTicket(['accounts']), function (Grid $grid) {
            $grid->model()
                ->where('applicant_id', Auth::user()->merchant_id)
                ->orderBy('created_at', 'desc')
                ->orderBy('updated_at', 'desc')
                ->orderBy('id', 'desc');

            $grid->column('id')->sortable();
            $grid->column('accounts.nickname')->display(function () {
                $accountsInfo = '';
                foreach ($this->accounts as $account) {
                    $accountsInfo .= $account->nickname . '<br />';
                }
                return $accountsInfo;
            });
            $grid->column('accounts.cardholder')->display(function () {
                $accountsInfo = '';
                foreach ($this->accounts as $account) {
                    $accountsInfo .= $account->cardholder . '<br />';
                }
                return $accountsInfo;
            });
            $grid->column('accounts.bank_account')->display(function () {
                $accountsInfo = '';
                foreach ($this->accounts as $account) {
                    $accountsInfo .= $account->bank_account . '<br />';
                }
                return $accountsInfo;
            });
            $grid->column('accounts.merchant_note')->display(function () {
                $accountsInfo = '';
                foreach ($this->accounts as $account) {
                    $accountsInfo .= $account->pivot->note . '<br />';
                }
                return $accountsInfo;
            });
            $grid->column('currency');
            $grid->column('amount');
            $grid->column('check_amount')->display(function ($value) {
                if (in_array($this->status, [TransferTicketModel::TRANSFER_STATUS_CHECK, TransferTicketModel::TRANSFER_STATUS_REJECT, TransferTicketModel::TRANSFER_STATUS_CANCEL])) {
                    return '0.00';
                }
                return $value;
            });
            $grid->column('fee');
            $grid->column('fee_currency');
            $grid->column('deduction_amount');
            $grid->column('convert_currency');
            $grid->column('convert_amount');
            $grid->column('status')->display(function ($value) {
                return admin_trans_option(TransferTicketModel::$transferStatusMap[$value] ?? admin_trans_field('未知'), 'merchant_ticket_status_map');
            })->dot(['0' => 'danger', '1' => 'success', '2' => 'primary', '3' => 'primary', '4' => 'primary', '5' => 'gray', '6' => 'danger']);
            $grid->column('remarks');
            $grid->column('created_at');
            $grid->column('updated_at')->sortable();
            $grid->column('transfer_at')->display(function ($value) {
                return $this->status == TransferTicketModel::TRANSFER_STATUS_SUCCESS ? substr($this->updated_at, 0, 10) : '';
            });

            $grid->fixColumns(0, 0);
            $grid->disableActions();
            $grid->disableBatchDelete();

            $grid->tools(new CancelBatch());

            $grid->filter(function (Grid\Filter $filter) {
                $filter->equal('id');
                $filter->equal('status')->select(admin_trans_label('merchant_ticket_status_map', TransferTicketModel::$transferStatusMap));
                $filter->between('created_at')->datetime(['sideBySide'=>true]);
                $filter->between('updated_at')->datetime(['sideBySide'=>true]);

            });
        });
    }

    /**
     * Make a form builder.
     *
     * @return Form
     */
    protected function form()
    {
        $merchantId = Auth::user()->merchant_id;

        // 提现币种获取
        $transferCurrencyList = MerchantService::getMerchantCurrencyList($merchantId);

        // 获取可提现金额
        $amountData = MerchantService::getAvailableAmountData($merchantId, false);

        //可提现币种处理
        $transfer = Merchant::where('merchant_id', Auth::user()->merchant_id)->value('transfer');
        $transfer = empty($transfer) ? [] : array_column($transfer, 'transfer_currency', 'transfer_currency');

        // 提现账户
        $transferAccountTypeList = [];
		$transferAccountIdList   = [];
        $tempList                = TransferAccount::select(['id', 'bank_name', 'convert_currency', 'type', 'bank_account'])->where('cardholder_id', $merchantId)->get()->toArray();

        foreach ($tempList as $value) {
            $transferAccountTypeList[$value['type']][$value['id']] = $value['bank_name'];
			$transferAccountIdList[$value['id']] = $value['bank_account'];
        }
        //中间表信息
        $relation_info = [];
        return Form::make(new TransferTicket(), function (Form $form) use ($transferCurrencyList, $amountData, $transfer) {
            $form->display('id');
            $form->hidden('applicant_id');
            $form->hidden('applicant_name');
            $form->hidden('check_amount');
            $form->hidden('fee');
            $form->hidden('fee_currency');
            $form->hidden('fee_type');
            $form->hidden('actual_amount');
            $form->hidden('status');
            $form->hidden('deduction_amount');

            // 余额动态显示
            $currencyInput = $form->select('currency')->options($transferCurrencyList)->default(current($transferCurrencyList));

            //出款货币
            $convertInput = $form->select('convert_currency')->options($transfer)
            ->load('transfer_account_id', '/transfer/Tickets/accounts/' . Auth::user()->merchant_id)->required();

            foreach ($amountData as $currency => $amount) {
                $currencyInput->when($currency, function (Form $form) use ($amount, $currency) {
                    $form->text('avaiable_balance')->default($amount['available_balance'])->disable();
                    $form->text('available_amount')->default($amount['available_amount'])->disable();
                    $form->hidden($currency . 'available_amount')->default($amount['available_amount']);
                });
            }

            $merchant         = auth()->user()->merchant;
            $transCurrencyFer = $merchant->transfer;

            $form->text('amount')->rules('required|numeric')->default('0.00')->help();
            $form->select('transfer_account_id')->attribute(['id' => 'account_id']);

            $tips      = [];
            $tipHead   = admin_trans_field('tip.withdrawal_charge');
            $tipTail   = ' ' . admin_trans_field('tip.currency');

            if (!empty($merchant->transfer) && is_array($transCurrencyFer)) {
                foreach ($transCurrencyFer as $value) {
                    $tips[$value['transfer_currency']] = [
                        'transfer_fee'  => $value['transfer_fee'],
                        'tip_middle'    => $value['transfer_type'] ? '% ' . admin_trans_field('amount') . ': ' : '',
                        'transfer_type' => $value['transfer_type'],
                    ];
                    $convertInput->when($value['transfer_currency'], function (Form $form) use ($value) {
                        if ($value['transfer_currency'] == 'CNY') {
                            $accountIds = $this->getCNYAccountIds();
                            $form->table('transfer_info', function ($table) use ($accountIds) {
                                $table->select('transfer_account_id')->options($accountIds);
                                $table->text('note');
                            });
                        }
                    });
                }
            }
            $tips = $tips ? json_encode($tips) : '{}';
            Admin::Script(
                <<<JS
                    var tips      = JSON.parse('{$tips}');
                    var tipHead   = '{$tipHead}';
                    var tipTail   = '{$tipTail}';
                    $("select[name='convert_currency']").change(function (){
                        $("input[name='amount']").val(0);
                        let currency = $(this).val();
                        if (currency == 'CNY') {
                            $('#select2-account_id-container').closest('.form-group').hide();
                        } else {
                            $('#select2-account_id-container').closest('.form-group').show();
                        }

                        if(currency){
                            var initialize = '';
                            if(tips[currency]['transfer_type'] == 1){
                                initialize = '0.00';
                            }
                            $(".icon-help-circle").text(tipHead + tips[currency]['transfer_fee'] + tips[currency]['tip_middle'] + initialize + tipTail + currency);
                        }
                    })

                    $("input[name='amount']").keyup(function (){
                        let currency = $("select[name='convert_currency']").val();

                        if(tips[currency]['transfer_type']  == 1 && currency){
                            let amount   = toThousands((Math.floor($(this).val() * tips[currency]['transfer_fee'] * 100) / 10000).toFixed(2));
                            $(".icon-help-circle").text(tipHead + tips[currency]['transfer_fee'] + tips[currency]['tip_middle'] + amount + tipTail + currency);
                        }
                    })

                    function toThousands(num, decimals = 2) {
                        var num   = (num || 0).toString(), result = '';
                        let slice = decimals - 5;
                        let tail  = num.slice(slice);
                        num = num.slice(0, num.length + slice);
                        while (num.length > 3) {
                            result = '.' + num.slice(-3) + result;
                            num = num.slice(0, num.length - 3);
                        }
                        if (num) { result = num + result; }
                        return result + tail;
                    }
                JS
            );
        })->saving(function (Form $form) use ($merchantId, $transferAccountTypeList, $transferCurrencyList, &$relation_info) {
            // 计算提现手续费
            $resultData = MerchantService::getTransferFee($merchantId, $form->currency, $form->amount, $form->convert_currency);

            if ($resultData['error']) {
                return $form->response()->error($resultData['msg']);
            }

            // 获取可提现金额
            $amountData = MerchantService::getAvailableAmountData($merchantId, false);
            $available_amount = $amountData[$form->currency]['available_amount'];
            //通用规则判断
            if ($form->amount > $available_amount) {
                return $form->response()->error(admin_trans_field('tip.transfer_amount_low'));
            }
            //外扣规则判断
            if ($resultData['data']['deduction_type'] == TransferTicketModel::EXTERNAL_BUCKLE && $form->amount + $resultData['data']['fee'] > $available_amount) {
                return $form->response()->error(admin_trans_field('tip.transfer_amount_low'));
            }
            //内扣最小金额判断
            if ($resultData['data']['deduction_type'] == TransferTicketModel::INNER_BUCKLE && $form->amount <= $resultData['data']['change_fee']) {
                return $form->response()->error(admin_trans_field('tip.amount_lt') . $resultData['data']['change_fee']);
            }

            switch ($form->convert_currency) {
                case 'CNY':
                    $type = TransferAccount::TYPE_DOMESTIC;
                    break;

                case 'T':
                    $type = TransferAccount::TYPE_CRYPTO;
                    break;

                default:
                    $type =  TransferAccount::TYPE_ABROAD;
                    break;
            }

            $typeList = $transferAccountTypeList[$type] ?? [];

            if ($type === TransferAccount::TYPE_DOMESTIC) {
                $transfer_info = $form->transfer_info;

                if (empty($transfer_info)) {
                    return $form->response()->error(admin_trans_field('tip.transfer_account_id'));
                }

                foreach ($transfer_info as $accountInfo) {
                    $is_remove = $accountInfo['_remove_'] ?? 1;

                    if (!$is_remove) {
                        if (!isset($typeList[$accountInfo['transfer_account_id']])) {
                            return $form->response()->error(admin_trans_field('tip.transfer_account_id'));
                        }

                        if (isset($relation_info[$accountInfo['transfer_account_id']])) {
                            return $form->response()->error(admin_trans_field('tip.transfer_account_id_repeat'));
                        }
                        $relation_info[] = ['transfer_account_id' => $accountInfo['transfer_account_id'], 'note' => $accountInfo['note']];
                    }
                }
            } else {
                if (!isset($typeList[$form->transfer_account_id])) {
                    return $form->response()->error(admin_trans_field('tip.transfer_account_id'));
                }
                $relation_info = [['transfer_account_id' => $form->transfer_account_id, 'note' => '']];
            }

            if (!count($relation_info)) {
                return $form->response()->error(admin_trans_field('tip.transfer_account_id_null'));
            }

            // 提交加锁
            $lock = Cache::lock('Transfer_Tickets_Any_Lock_' . $merchantId, 30);

            if (!$lock->get()) {
                return $form->response()->error('重复提交');
            }

            // 删除多余数据
            $form->deleteInput('transfer_account_id');
            $form->deleteInput('transfer_info');

            foreach ($transferCurrencyList as $currency) {
                $form->deleteInput($currency.'available_amount');
            }

            // 组装数据
			$now = now();
            $form->applicant_id     = $merchantId;
            $form->applicant_name   = $resultData['merchant_name'];
            $form->check_amount     = $form->amount;
            $form->deduction_amount = $resultData['data']['deduction_amount'] ?? $form->amount;
            $form->fee              = $resultData['data']['fee'] ?? '0.00';
            $form->fee_currency     = $resultData['data']['fee_currency'] ?? $form->convert_currency;
            $form->fee_type         = $resultData['data']['deduction_type'] ?? TransferTicketModel::INNER_BUCKLE;
            $form->actual_amount    = $resultData['data']['actual_amount'] ?? '0.00';
            $form->status           = TransferTicketModel::TRANSFER_STATUS_CHECK;
			$form->created_at       = $now;
			$form->updated_at       = $now;
        })->saved(function (Form $form) use (&$relation_info, $merchantId, $transferAccountIdList) {
            if ($form->isCreating()) {
				$bankAccounts = [];
                $ticketId     = ['ticket_id' => $form->getKey()];
                array_walk($relation_info, function (&$data, $key, $ticketId) use ($transferAccountIdList, &$bankAccounts) {
                    $data           = array_merge($data, $ticketId);
					$bankAccounts[] = $transferAccountIdList[$data['transfer_account_id']];
                }, $ticketId);

                DB::table('transfer_account_relation')->insert($relation_info);

				// 发送企业微信通知到 Withdrawal reminder 角色
				$noticeData = [
					'level'             => 1,
					'contents'          =>
						'[商户提现申请通知]' . PHP_EOL .
						'MID: ' . $form->applicant_id . PHP_EOL .
						'商户名称: ' . $form->applicant_name . PHP_EOL .
						'银行账号: ' . implode(',', $bankAccounts) . PHP_EOL .
						'货币: ' . $form->currency . PHP_EOL .
						'金额: ' . amount_format($form->amount) . PHP_EOL .
						'创建时间: ' . $form->created_at . PHP_EOL .
                        '来源:' . config('app.url'),
					'notice_user_roles' => 'Withdrawal reminder',
					'type'              => 3,
					'status'            => 2,
				];
				dispatch(new SendNotice($noticeData, 5, 'workNoticeStorage'));
            }

            // 释放锁
            Cache::lock('Transfer_Tickets_Any_Lock_' . $merchantId)->forceRelease();
        });
    }

    /**
     * 返回提现账户
     * @param string $cardholder_id
     * @return array
     */
    protected function accounts($cardholder_id)
    {
        $convert_currency   = $_GET['q'] ?: '';
        $transferAccountArr = TransferAccount::where('convert_currency', $convert_currency)->where('cardholder_id', $cardholder_id)->where('status', TransferAccount::STATUS_ADOPT)->selectRaw('id, bank_name, bank_account, type')->get()->toArray();
        $transferAccount    = [];

        if (!empty($transferAccountArr)) {
            foreach ($transferAccountArr as $vo) {
                if ($vo['type'] == TransferAccount::TYPE_CRYPTO) {
                    $transferAccount[] = [
                        'id' => $vo['id'],
                        'text' => $vo['bank_account'],
                    ];
                } else {
                    $transferAccount[] = [
                        'id' => $vo['id'],
                        'text' => $vo['bank_name'],
                    ];
                }
            }
        }

        return $transferAccount;
    }

    /**
     * 返回CNY提现账户
     * @return array
     */
    protected function getCNYAccountIds()
    {
        $accounts = TransferAccount::query()->select('id', 'bank_name')
            ->where('convert_currency', 'CNY')
            ->where('cardholder_id', Auth::user()->merchant_id)
            ->where('status', TransferAccount::STATUS_ADOPT)
            ->get()->toArray();

        $accountIds = [];
        foreach ($accounts as $account) {
            $accountIds[$account['id']] = $account['bank_name'];
        }

        return $accountIds;
    }
}
