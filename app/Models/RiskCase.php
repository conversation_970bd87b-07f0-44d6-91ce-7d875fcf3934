<?php

namespace App\Models;

use Dcat\Admin\Traits\HasDateTimeFormatter;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Model;

class RiskCase extends Model
{
    use HasDateTimeFormatter;
    use SoftDeletes;

    protected $table = 'risk_cases';

    protected $guarded = [];

    protected static $role = 'Risk Control Operation';

    const CASE_TYPE_KYC     = 0;
    const CASE_TYPE_CSC     = 1;
    const CASE_TYPE_CRA     = 2;
    const CASE_TYPE_FA      = 3;
    const CASE_TYPE_FRR     = 4;
    const CASE_TYPE_SUMMARY = 5;

    public static $caseTypeMap = [
        self::CASE_TYPE_KYC     => '商户背景审查 ',
        self::CASE_TYPE_CSC     => '卡组合规',
        self::CASE_TYPE_CRA     => '信用风险评估报告',
        self::CASE_TYPE_FA      => '财务分析',
        self::CASE_TYPE_FRR     => '欺诈风险规则',
        self::CASE_TYPE_SUMMARY => '商户案例总结',
    ];

    public static $countryMap = [
        -1                                 => '',
        MerchantKyc::TYPE_CHINESE_MAINLAND => '中国大陆',
        MerchantKyc::TYPE_CHINESE_HONGKONG => '中国香港',
        MerchantKyc::TYPE_OVERSEAS         => '海外',
    ];

    const CASE_STATUS_UNDER    = 0;
    const CASE_STATUS_PENDING  = 1;
    const CASE_STATUS_COMPLETE = 2;
    const CASE_STATUS_RE       = 3;

    public static $caseStatusMap = [
        self::CASE_STATUS_UNDER    => '待审核',
        self::CASE_STATUS_PENDING  => '审核中',
        self::CASE_STATUS_COMPLETE => '审核完成',
        self::CASE_STATUS_RE       => '复审中',
    ];

    const CASE_AUDIT_UNDER   = 0;
    const CASE_AUDIT_APPROVE = 1;
    const CASE_AUDIT_REJECT  = 2;
    const CASE_AUDIT_REVOKE  = 3;

    public static $auditResultMap = [
        self::CASE_AUDIT_UNDER   => '',
        self::CASE_AUDIT_APPROVE => '审核通过',
        self::CASE_AUDIT_REJECT  => '审核不通过',
        self::CASE_AUDIT_REVOKE  => '商户撤销',
    ];

    public static $auditResultSelectMap = [
        self::CASE_AUDIT_APPROVE => '审核通过',
        self::CASE_AUDIT_REJECT  => '审核不通过',
    ];

    public static function getRole(): string
    {
        return self::$role;
    }

    //根据不同的 type 获取模型
    public static function getModelByCaseType($type)
    {
        switch ($type) {
            case self::CASE_TYPE_KYC:
                return new MerchantKyc();
            case self::CASE_TYPE_CSC:
                return new MerchantUrl();
            case self::CASE_TYPE_CRA:
                return new RiskCreditAssessment();
            case self::CASE_TYPE_FA:
            case self::CASE_TYPE_FRR:
                // todo
            case self::CASE_TYPE_SUMMARY:
                return new RiskSummary();
            default:
                return null;
        }
    }

    public function riskCasesBid(): HasMany
    {
        return $this->hasMany(RiskCasesBid::class);
    }

    /**
     * 与 BID 的多对多关系
     * 通过中间表 risk_cases_bid 关联
     */
    public function bids(): BelongsToMany
    {
        return $this->belongsToMany(
            \App\Models\MerchantBusiness::class,
            'risk_cases_bid',
            'risk_case_id',
            'business_id',
            'id',
            'business_id'
        );
    }

    public function creditAssessment(): HasOne
    {
        return $this->hasOne(RiskCreditAssessment::class, 'risk_case_id', 'id');
    }

}
