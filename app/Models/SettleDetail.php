<?php

namespace App\Models;

use Dcat\Admin\Traits\HasDateTimeFormatter;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\DB;

class SettleDetail extends Model
{
	use HasDateTimeFormatter;

    protected $table = 'settle_details';

    protected $guarded = [];

    // 金额类型
    const AMOUNT_TYPE_00 = '00';
    const AMOUNT_TYPE_01 = '01';
    const AMOUNT_TYPE_02 = '02';
    const AMOUNT_TYPE_03 = '03';
    const AMOUNT_TYPE_04 = '04';
    const AMOUNT_TYPE_05 = '05';
    const AMOUNT_TYPE_10 = '10';
    const AMOUNT_TYPE_11 = '11';
    const AMOUNT_TYPE_12 = '12';
    const AMOUNT_TYPE_20 = '20';
    const AMOUNT_TYPE_21 = '21';
    const AMOUNT_TYPE_22 = '22';
    const AMOUNT_TYPE_30 = '30';
    const AMOUNT_TYPE_31 = '31';
    const AMOUNT_TYPE_40 = '40';
    const AMOUNT_TYPE_41 = '41';
    const AMOUNT_TYPE_42 = '42';
    const AMOUNT_TYPE_43 = '43';
    const AMOUNT_TYPE_44 = '44';
    const AMOUNT_TYPE_45 = '45';
    const AMOUNT_TYPE_50 = '50';
    const AMOUNT_TYPE_51 = '51';
    const AMOUNT_TYPE_52 = '52';
    const AMOUNT_TYPE_90 = '90';

    // 金额类型列表
	public static $amountTypeMap = [
		self::AMOUNT_TYPE_00 => 'Sale金额',
		self::AMOUNT_TYPE_01 => 'Sale比例手续费',
		self::AMOUNT_TYPE_02 => 'Sale单笔处理费',
		self::AMOUNT_TYPE_03 => 'Sale保证金',
		self::AMOUNT_TYPE_04 => 'Sale3d交易费',
		self::AMOUNT_TYPE_05 => 'Sale风控处理费',
		self::AMOUNT_TYPE_10 => 'Auth单笔处理费',
		self::AMOUNT_TYPE_11 => 'Auth3d交易费',
		self::AMOUNT_TYPE_12 => 'Auth风控处理费',
		self::AMOUNT_TYPE_20 => 'Capture金额',
		self::AMOUNT_TYPE_21 => 'Capture比例手续费',
		self::AMOUNT_TYPE_22 => 'Capture保证金',
		self::AMOUNT_TYPE_30 => '退款金额',
		self::AMOUNT_TYPE_31 => '退款手续费',
		self::AMOUNT_TYPE_40 => '拒付金额',
		self::AMOUNT_TYPE_41 => '拒付处理费',
		self::AMOUNT_TYPE_42 => '申诉成功金额',
		self::AMOUNT_TYPE_43 => '预拒付处理费',
		self::AMOUNT_TYPE_44 => '拒付罚金',
		self::AMOUNT_TYPE_45 => '拒付罚金*',
		self::AMOUNT_TYPE_50 => '结转MID到账金额',
		self::AMOUNT_TYPE_51 => '结转MID保证金入账',
		self::AMOUNT_TYPE_52 => '结转MID保证金返还',
		self::AMOUNT_TYPE_90 => '其他'
	];

	public static $amountTypeMapEn = [
		self::AMOUNT_TYPE_00 => 'Sale Amount',
		self::AMOUNT_TYPE_01 => 'Sale Rate Fee',
		self::AMOUNT_TYPE_02 => 'Sale Fee',
		self::AMOUNT_TYPE_03 => 'Sale Deposit Fee',
		self::AMOUNT_TYPE_04 => 'Sale 3d Fee',
		self::AMOUNT_TYPE_05 => 'Sale Risk Fee',
		self::AMOUNT_TYPE_10 => 'Auth Fee',
		self::AMOUNT_TYPE_11 => 'Auth 3d Fee',
		self::AMOUNT_TYPE_12 => 'Auth Risk Fee',
		self::AMOUNT_TYPE_20 => 'Capture Amount',
		self::AMOUNT_TYPE_21 => 'Capture Rate Fee',
		self::AMOUNT_TYPE_22 => 'Capture Deposit Fee',
		self::AMOUNT_TYPE_30 => 'Refund Amount',
		self::AMOUNT_TYPE_31 => 'Refund Fee',
		self::AMOUNT_TYPE_40 => 'Chargeback Amount',
		self::AMOUNT_TYPE_41 => 'Chargeback Fee',
		self::AMOUNT_TYPE_42 => 'Chargeback Reversal Amount',
		self::AMOUNT_TYPE_43 => 'Pre Chargeback Fee',
		self::AMOUNT_TYPE_44 => 'Chargeback Penalty',
		self::AMOUNT_TYPE_45 => 'Chargeback Penalty Special',
		self::AMOUNT_TYPE_50 => 'Transfer Amount To MID',
		self::AMOUNT_TYPE_51 => 'Transfer Deposit To MID',
		self::AMOUNT_TYPE_52 => 'Transfer Return Deposit To MID',
		self::AMOUNT_TYPE_90 => 'Other'
	];

    // 对账单金额类型列表
    public static $settlementAmountTypeMap = [
        self::AMOUNT_TYPE_00 => 'transaction_amount',
        self::AMOUNT_TYPE_01 => 'transaction_rate_fee',
        self::AMOUNT_TYPE_02 => 'transaction_fee',
        self::AMOUNT_TYPE_03 => 'transaction_deposit',
        self::AMOUNT_TYPE_04 => 'transaction_3d_fee',
        self::AMOUNT_TYPE_05 => 'transaction_risk_fee',
        self::AMOUNT_TYPE_10 => 'transaction_fee',
        self::AMOUNT_TYPE_11 => 'transaction_3d_fee',
        self::AMOUNT_TYPE_12 => 'transaction_risk_fee',
        self::AMOUNT_TYPE_20 => 'transaction_amount',
        self::AMOUNT_TYPE_21 => 'transaction_rate_fee',
        self::AMOUNT_TYPE_22 => 'transaction_deposit',
        self::AMOUNT_TYPE_30 => 'refund_amount',
        self::AMOUNT_TYPE_31 => 'refund_fee',
        self::AMOUNT_TYPE_40 => 'chargeback_amount',
        self::AMOUNT_TYPE_41 => 'chargeback_fee',
        self::AMOUNT_TYPE_42 => 'chargeback_reversal_amount',
        self::AMOUNT_TYPE_43 => 'pre_chargeback_fee',
        self::AMOUNT_TYPE_44 => 'chargeback_penalty',
        self::AMOUNT_TYPE_45 => 'chargeback_penalty_special',
        self::AMOUNT_TYPE_90 => 'settle_adjust_amount'
    ];

    /**
     * 结转结算账户
     *
     * @param $date
     * @return array
     */
    public static function getSettleBalanceToMid($date)
    {
        $data     = array();
        $tempData = self::select(['business_id', 'settle_currency', DB::raw('SUM(settle_amount) as settle_amount')])
            ->where('settle_at', $date)
            ->whereNotIn('amount_type', ['50', '51', '52'])
            ->groupBy(['business_id', 'settle_currency'])
            ->get()->toArray();

        foreach ($tempData as $val) {
            $data[$val['business_id']][$val['settle_currency']] = $val['settle_amount'];
        }

        return $data;
    }

    /**
     * 结转保证金账户(入账)
     *
     * @param $date
     * @return array
     */
    public static function getInDepositBalanceToMid($date)
    {
        $data     = array();
        $tempData = self::select(['business_id', 'settle_currency', DB::raw('SUM(settle_amount) as settle_amount')])
            ->where('settle_at', $date)
            ->whereIn('amount_type', ['03', '22'])
            ->where('settle_amount', '<', '0')
            ->groupBy(['business_id', 'settle_currency'])
            ->get()->toArray();

        foreach ($tempData as $val) {
            $data[$val['business_id']][$val['settle_currency']] = $val['settle_amount'];
        }

        return $data;
    }

    /**
     * 结转保证金账户(返还)
     *
     * @param $date
     * @return array
     */
    public static function getOutDepositBalanceToMid($date)
    {
        $data     = array();
        $tempData = self::select(['business_id', 'settle_currency', DB::raw('SUM(settle_amount) as settle_amount')])
            ->where('settle_at', $date)
            ->whereIn('amount_type', ['03', '22'])
            ->where('settle_amount', '>', '0')
            ->groupBy(['business_id', 'settle_currency'])
            ->get()->toArray();

        foreach ($tempData as $val) {
            $data[$val['business_id']][$val['settle_currency']] = $val['settle_amount'];
        }

        return $data;
    }

    public function order()
    {
        return $this->belongsTo(Order::class, 'related_id', 'order_id');
    }
}
