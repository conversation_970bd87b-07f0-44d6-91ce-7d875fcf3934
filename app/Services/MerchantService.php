<?php

namespace App\Services;

use App\Models\DirectoryCurrency;
use App\Models\Merchant;
use App\Models\MerchantBusiness;
use App\Models\OrderSettlement;
use App\Models\SettleMerchant;
use App\Models\TransferTicket;
use App\Models\MerchantTicket;
use App\Models\MerchantCardTicket;
use App\Models\MerchantRiskFrozen;
use App\Models\SettleDetail;
use App\Models\SettleDetailMerchant;
use Illuminate\Support\Facades\DB;
use App\Classes\Supports\Str;
use Maatwebsite\Excel\Facades\Excel;
use App\Admin\Extensions\MerchantReportForm as ExtensionsMerchantReportForm;
use App\Models\MerchantReportForm;

class MerchantService
{
    public static $dateList = [
        '164791825957292' => '2024-05-08',
        '166520855232531' => '2024-05-06',
        '168612884744097' => '2024-05-08',
    ];

    // 特殊商户语言处理 note 如果经常变动则考虑存储到数据库或缓存中，方便快速增减
	public static $specialMerchantLang = [
        '164791825957292' => 'en',
        '166520855232531' => 'en',
        '168612884744097' => 'en',
	];

    /**
     * mid提现币种
     *
     * @param $merchantId
     * @return array
     */
    public static function getMerchantCurrencyList($merchantId)
    {
        $businessList = MerchantBusiness::where('internal_status', MerchantBusiness::INTERNAL_STATUS_ENABLE)
            ->where('merchant_id', $merchantId)
            ->get()
            ->toArray();

        $tempData     = array_filter(array_unique(explode(',', implode(',', array_column($businessList, 'settle_currencies')))));
        $currencyList = [];

        // 添加默认币种
        $currencyList['USD'] = 'USD';

        foreach ($tempData as $value) {
            $currencyList[$value] = $value;
        }

        return $currencyList;
    }

    /**
     * 获取mid可提现金额
     *
     * @param $merchantId
     * @param bool $isCheck
     * @return array
     */
    public static function getAvailableAmountData($merchantId, $isCheck = true)
    {
        $currencyList = self::getMerchantCurrencyList($merchantId);
        $returnData   = [];

        if (empty($currencyList)) {
            return $returnData;
        }

        if (in_array($merchantId, array_keys(self::$dateList))) {
            $date = self::$dateList[$merchantId];
            if (empty($date)) {
                return $returnData;
            }

            $dateTime    = date('Y-m-d 23:59:59', strtotime($date));
            $whereSettle = [
                ['merchant_id', '=', $merchantId],
                ['settle_at', '>', $date],
            ];
            $whereTransferTicket = [
                ['applicant_id', '=', $merchantId],
                ['created_at', '>', $dateTime]
            ];
            $whereMerchant = [
                ['merchant_id', '=', $merchantId],
                ['created_at', '>', $dateTime]
            ];
        } else {
            $whereSettle = [
                ['merchant_id', '=', $merchantId]
            ];
            $whereTransferTicket = [
                ['applicant_id', '=', $merchantId]
            ];
            $whereMerchant = [
                ['merchant_id', '=', $merchantId]
            ];
        }

        // 获取BID结转MID结算账户
        $settleMerchant = SettleMerchant::select('settle_currency', DB::raw('SUM(settle_amount) as settle_amount'))
            ->where($whereSettle)
            ->where('settle_at', '<=', date('Y-m-d'))
            ->groupBy(['settle_currency'])
            ->get()
            ->pluck('settle_amount', 'settle_currency')
            ->toArray();

        // 已完成、待完成的提现
        $statusList = [
            TransferTicket::TRANSFER_STATUS_SUCCESS,
            TransferTicket::TRANSFER_STATUS_HANDLE,
            TransferTicket::TRANSFER_STATUS_PROCESS
        ];

        if (!$isCheck) {
            $statusList[] = TransferTicket::TRANSFER_STATUS_CHECK;
        }

        $transferList = TransferTicket::select('currency', DB::raw('SUM(deduction_amount) as amount'))
            ->where($whereTransferTicket)
            ->whereIn('status', $statusList)
            ->groupBy(['currency'])
            ->get()
            ->pluck('amount', 'currency')
            ->toArray();

        // 获取已完成但未添加结算的退款、拒付
        $typeList          = [
            OrderSettlement::TYPE_REFUND,
            OrderSettlement::TYPE_CHARGEBACK,
            OrderSettlement::TYPE_CHARGEBACK_REVERSAL
        ];
        $refundStatusList = [
            OrderSettlement::TYPE_REFUND,

        ];
        $disHonorStatusList = [
            OrderSettlement::TYPE_CHARGEBACK,
            OrderSettlement::TYPE_CHARGEBACK_REVERSAL
        ];

        $refundSettleOrder = OrderSettlement::select(
            'settle_currency',
            DB::raw('SUM(IF(type=5, settle_amount, -settle_amount)) settle_amount')
        )->where($whereSettle)
            ->whereIn('type', $typeList)
            ->where('status', OrderSettlement::STATUS_APPROVED)
            ->where('is_settle', '0')
            ->groupBy(['settle_currency'])
            ->get()
            ->pluck('settle_amount', 'settle_currency')
            ->toArray();

        $refundList = OrderSettlement::select(
            'settle_currency',
            DB::raw('SUM(IF(type=5, settle_amount, -settle_amount)) settle_amount')
        )->where($whereSettle)
            ->whereIn('type', $refundStatusList)
            ->where('status', OrderSettlement::STATUS_APPROVED)
            ->where('is_settle', '0')
            ->groupBy(['settle_currency'])
            ->get()
            ->pluck('settle_amount', 'settle_currency')
            ->toArray();

        $disHonorList = OrderSettlement::select(
            'settle_currency',
            DB::raw('SUM(IF(type=5, settle_amount, -settle_amount)) settle_amount')
        )->where($whereSettle)
            ->whereIn('type', $disHonorStatusList)
            ->where('status', OrderSettlement::STATUS_APPROVED)
            ->where('is_settle', '0')
            ->groupBy(['settle_currency'])
            ->get()
            ->pluck('settle_amount', 'settle_currency')
            ->toArray();

        //预估待结算金额
        $amountToBeSettledType = [
            OrderSettlement::TYPE_SALE,
            OrderSettlement::TYPE_CAPTURE,
            OrderSettlement::TYPE_CHARGEBACK_REVERSAL,
        ];

        $amountToBeSettledList = OrderSettlement::selectRaw(
            'settle_currency, SUM(settle_amount) as settle_amount'
        )->where($whereSettle)
            ->where('status', OrderSettlement::STATUS_APPROVED)
            ->whereIn('type', $amountToBeSettledType)
            ->where('is_settle', OrderSettlement::IS_SETTLE_WAITING)
            ->groupBy(['settle_currency'])
            ->pluck('settle_amount', 'settle_currency')
            ->toArray();

        $amountToBeSettledType = [
            OrderSettlement::TYPE_REFUND,
            OrderSettlement::TYPE_CHARGEBACK,
        ];

        $deductionList = OrderSettlement::selectRaw(
            'settle_currency, SUM(settle_amount) as settle_amount'
        )->where($whereSettle)
            ->where('status', OrderSettlement::STATUS_APPROVED)
            ->whereIn('type', $amountToBeSettledType)
            ->where('is_settle', OrderSettlement::IS_SETTLE_WAITING)
            ->groupBy(['settle_currency'])
            ->pluck('settle_amount', 'settle_currency')
            ->toArray();

        //mid充值
        $merchantTicket = MerchantTicket::select(
            'refill_currency',
            DB::raw('SUM(reality_refill_amount) as settle_amount')
        )->where($whereSettle)
            ->where('status', MerchantTicket::MERCHANT_TICKET_STATUS_SUCCESS)
            ->where('is_settle', MerchantTicket::STATUS_DECLINED)
            ->groupBy(['refill_currency'])
            ->get()
            ->pluck('settle_amount', 'refill_currency')
            ->toArray();

        //cid充退值
        $merchantCardTicket     = [];
        $tempMerchantCardTicket = MerchantCardTicket::select(
            'currency',
            DB::raw('SUM(IF(type=0, amount, -amount)) as settle_amount'),
            DB::raw('SUM(IF(type="+", fee, -fee)) fee')
        )->where($whereSettle)
            ->where('status', MerchantCardTicket::MERCHANT_TICKET_STATUS_SUCCESS)
            ->where('is_settle', MerchantTicket::STATUS_DECLINED)
            ->groupBy(['currency'])
            ->get()
            ->toArray();

        foreach ($tempMerchantCardTicket as $value) {
            $merchantCardTicket[$value['currency']] = $value;
        }

        //cid充值待审核金额
        $merchantCardTicketCheck     = [];
        $tempMerchantCardTicketCheck = MerchantCardTicket::select(
            'currency',
            DB::raw('SUM(amount) as settle_amount'),
            DB::raw('SUM(fee) as fee')
        )->where($whereSettle)
            ->where('status', MerchantCardTicket::MERCHANT_TICKET_STATUS_CHECK)
            ->where('is_settle', MerchantTicket::STATUS_DECLINED)
            ->where('type', MerchantCardTicket::MERCHANT_TICKET_TYPE_IN)
            ->groupBy(['currency'])
            ->get()
            ->toArray();

        foreach ($tempMerchantCardTicketCheck as $value) {
            $merchantCardTicketCheck[$value['currency']] = $value;
        }

        //mid冻结资金
        $merchantFrozenCurrencyList = self::getMerchantFrozenCurrencyList($merchantId, $currencyList);

        // 固定保证金
        $deposit     = [];
        $tempDeposit = RegularDepositService::getReleaseDeposit($whereMerchant);

        foreach ($tempDeposit as $value) {
            $deposit[$value['actual_currency']] = $value['actual_amount'];
        }

        foreach ($currencyList as $currency) {
            $settleAmount   = isset($settleMerchant[$currency]) ? $settleMerchant[$currency] : '0.00';
            $transferAmount = isset($transferList[$currency]) ? $transferList[$currency] : '0.00';
            $refundAmount   = isset($refundSettleOrder[$currency]) ? $refundSettleOrder[$currency] : '0.00';
            $frozenAmount   = $merchantFrozenCurrencyList[$currency];
            $depositAmount  = $deposit[$currency] ?? '0.00';

            $merchantTicketAmount          = isset($merchantTicket[$currency]) ? $merchantTicket[$currency] : '0.00';
            $merchantCardTicketAmount      = isset($merchantCardTicket[$currency]['settle_amount']) ? $merchantCardTicket[$currency]['settle_amount'] : '0.00';
            $merchantCardTicketFee         = isset($merchantCardTicket[$currency]['fee']) ? $merchantCardTicket[$currency]['fee'] : '0.00';
            $merchantCardTicketCheckAmount = isset($merchantCardTicketCheck[$currency]['settle_amount']) ? $merchantCardTicketCheck[$currency]['settle_amount'] : '0.00';
            $merchantCardTicketCheckFee    = isset($merchantCardTicketCheck[$currency]['fee']) ? $merchantCardTicketCheck[$currency]['fee'] : '0.00';

            $returnData[$currency]['available_balance'] = amount_format($settleAmount - $transferAmount + $merchantTicketAmount - $merchantCardTicketAmount - $merchantCardTicketFee - $merchantCardTicketCheckAmount - $merchantCardTicketCheckFee);
            $returnData[$currency]['available_amount']  = amount_format($returnData[$currency]['available_balance'] + $refundAmount - $frozenAmount - $depositAmount);
            $returnData[$currency]['refund_amount']  = amount_format( $refundList[$currency] ?? '0.00');
            $returnData[$currency]['disHonor_amount']  = amount_format($disHonorList[$currency] ?? '0.00');
            $returnData[$currency]['amount_to_be_settled']  = amount_format(($amountToBeSettledList[$currency] ?? '0.00') - ($deductionList[$currency] ?? '0.00'));
            $returnData[$currency]['frozen_amount']  = $frozenAmount;
            $returnData[$currency]['deposit_amount'] = $depositAmount;

        }

        return $returnData;
    }

    /**
     * 计算提现手续费、实际提现金额
     *
     * @param $merchantId
     * @param $currency
     * @param $amount
     * @return array
     */
    public static function getTransferFee($merchantId, $currency, $amount, $convertCurrency)
    {
        $merchant   = Merchant::find($merchantId);
        $returnData = [
            'error' => false,
            'msg'   => '',
            'data'  => [
                'merchant_name'    => '',
                'deduction_type'   => $merchant->deduction_type ?? Merchant::INNER_BUCKLE,
                'fee'              => '0.00',
                'fee_currency'     => $convertCurrency,
                'actual_amount'    => '0.00',
                'deduction_amount' => $amount,
                'change_fee'       => '0.00',
            ]
        ];

        if (empty($merchant)) {
            $returnData['error'] = true;
            $returnData['msg']   = 'MID信息不存在';

            return $returnData;
        }

        //获取提现配置
        $transferArr = Merchant::where('merchant_id', $merchantId)->value('transfer');

        if (empty($transferArr)) {
            $returnData['error'] = true;
            $returnData['msg']   = '提现配置信息不存在';

            return $returnData;
        }

        $transfer = [];
        foreach ($transferArr as $vo) {
            $transfer[$vo['transfer_currency']] = $vo;
        }

        //出款货币对应手续费
        $transfer_fee = $transfer[$convertCurrency]['transfer_fee'];
        //比例手续费
        if ($transfer[$convertCurrency]['transfer_type'] == Merchant::RATIO_FEE) {
            $transfer_fee = amount_format($transfer_fee * $amount / 100);
        }

        //转换手续费用于判断
        $change_fee = $transfer_fee;
        if ($currency != $convertCurrency) {
            $directoryCurrencyArr = DirectoryCurrency::whereIn('code', [$currency, $convertCurrency])->get()->pluck('rate', 'code');
            $change_fee           = amount_format($transfer_fee * ($directoryCurrencyArr[$currency] / $directoryCurrencyArr[$convertCurrency]));
        }

        //外扣需要转换手续费币种为提现币种
        if ($merchant->deduction_type == Merchant::EXTERNAL_BUCKLE) {
            $transfer_fee = $change_fee;
            //外扣扣款金额需加上手续费
            $returnData['data']['deduction_amount'] = $amount + $transfer_fee;
            $returnData['data']['fee_currency']     = $currency;
        }

        //不计算实际到帐金额 手续费只做显示（目前实际到账金额就等于审核金额）
        $returnData['merchant_name']         = $merchant->merchant_name;
        $returnData['data']['fee']           = $transfer_fee;
        $returnData['data']['actual_amount'] = $amount;
        $returnData['data']['change_fee']    = $change_fee;

        return $returnData;
    }

    /**
     * 获取商家冻结货币金额列表
     *
     * @param [type] $merchantId
     * @return array
     */
    public static function getMerchantFrozenCurrencyList($merchantId, $currencyList)
    {
        $whereMerchant = [
            ['merchant_id', '=', $merchantId]
        ];
        if (in_array($merchantId, array_keys(self::$dateList))) {
            $date            = self::$dateList[$merchantId];
            $dateTime        = date('Y-m-d 23:59:59', strtotime($date));
            $whereMerchant[] = ['created_at', '>', $dateTime];
        }

        $frozenCurrencyList = MerchantRiskFrozen::query()->select('frozen_currency', 'frozen_amount')
            ->where($whereMerchant)
            ->get()
            ->keyBy('frozen_currency')
            ->toArray();

        foreach ($currencyList as $currency) {
            $currencyList[$currency] = $frozenCurrencyList[$currency]['frozen_amount'] ?? '0.00';
        }

        return $currencyList;
    }

    /**
     * 默认生成国际卡收单计费 默认生成保底规则0-*-*
     *
     * @param $merchantId
     * @param $businessId
     */
    public static function createChargeRateCc($merchantId, $businessId)
    {
        $insert = [
            'business_id'      => $businessId,
            'merchant_id'      => $merchantId,
            'region'           => '*',
            'cc_type'          => '*',
            'transaction_rate' => 0.00,
            'created_at'       => now(),
            'updated_at'       => now()
        ];

        DB::table('merchant_charge_rate_ccs')->insert($insert);
    }

    public static function getAddBusinessInfo($data)
    {
        $businessInfo = [];
        if (isset($data['merchant_id'], $data['merchant_name'], $data['business_id'])) {
            $businessInfo = [
                'business_id'     => $data['business_id'],
                'merchant_id'     => $data['merchant_id'],
                'merchant_name'   => $data['merchant_name'],
                'open_blacklist'  => MerchantBusiness::BLACKLIST_LIST_OPEN,
                'internal_status' => MerchantBusiness::INTERNAL_STATUS_CONFIG,
                'fail_limits'     => [['cc_type' => '*', 'fail_limit' => '3', 'limit_time' => 24]],
                '_remove_'        => '0'
            ];
        }

        return $businessInfo;
    }

    public static function getDetailMerchantList($params)
    {
		$tempData      = [];
		$settleDetails = [];
		$staSettleAt   = '2200-01-01';
		$endSettleAt   = '1970-01-01';
        if (empty($params['query_criteria']) || empty($params['merchant_id'])) {
            return $settleDetails;
        }

		foreach ($params['query_criteria'] as $value) {
			$staSettleAt = SettlementService::mixDate($value['str_at'], $staSettleAt);
			$endSettleAt = SettlementService::maxDate($value['end_at'], $endSettleAt);
			//BID结转MID明细
			SettleDetail::query()->where('business_id', $value['business_id'])
				->whereBetween('settle_at', [$value['str_at'], $value['end_at']])
				->whereIn('amount_type', [SettleDetail::AMOUNT_TYPE_50, SettleDetail::AMOUNT_TYPE_51, SettleDetail::AMOUNT_TYPE_52])
				->chunkById(1000, function ($settleDetails) use (&$tempData) {
					foreach ($settleDetails as $settleDetail) {
						$key = $settleDetail['merchant_id'] . $settleDetail['currency'] . $settleDetail['settle_at'];
						if (!isset($tempData[$key])) {
							$tempData[$key] = [
								'merchant_id'        => $settleDetail['merchant_id'],
								'merchant_name'      => $settleDetail['merchant_name'],
								'amount_type'        => SettleDetailMerchant::AMOUNT_TYPE_0,
								'settle_currency'    => $settleDetail['settle_currency'],
								'settle_amount'      => 0,
								'in_deposit_amount'  => 0,
								'out_deposit_amount' => 0,
								'settle_at'          => $settleDetail['settle_at'],
							];
						}

						switch ($settleDetail['amount_type']) {
							case SettleDetail::AMOUNT_TYPE_50:
								$tempData[$key]['settle_amount'] += -1 * $settleDetail['settle_amount'];
								break;

							case SettleDetail::AMOUNT_TYPE_51:
								$tempData[$key]['in_deposit_amount'] += $settleDetail['settle_amount'];
								break;

							case SettleDetail::AMOUNT_TYPE_52:
								$tempData[$key]['out_deposit_amount'] += $settleDetail['settle_amount'];
								break;
						}
					}
				});
		}

		// MID结算明细汇总
		$tempSettleDetails = SettleDetailMerchant::query()->where('merchant_id', $params['merchant_id'])
			->where('settle_at', '<=', date('Y-m-d'))
			->whereBetween('settle_at', [$staSettleAt, $endSettleAt])
			->where('amount_type', '<>', SettleDetailMerchant::AMOUNT_TYPE_0)
			->get()->toArray();

		$settleDetails = array_merge($tempSettleDetails, $tempData);
        return $settleDetails;
    }

    public static function reportFormExport(array $data)
    {
        $fileName = 'settlement-' . Str::random(10) . '.xlsx';
        $lang     = self::$specialMerchantLang[$data['merchant_id']] ?? 'zh_CN';
        $result   = Excel::store(new ExtensionsMerchantReportForm($data, $lang), '/export/' . $fileName, 'data');
        if ($result) {
            MerchantReportForm::query()->where('id', $data['id'])->update(['file_name' => $fileName, 'url' =>'/data/export/' . $fileName]);
        }
    }
}
