<?php


namespace App\Services\Virtual;

use App\Classes\Card\Contracts\CardSupplierGateWay;

abstract class VirtualAbstractService
{
    protected $gateway;

    public function __construct(CardSupplierGateWay $cardGateway)
    {
        $this->setLogger();
        $this->gateway = $cardGateway;
    }

    /**
     * 设置记录文件日志
     *
     * @return void
     */
    abstract protected function setLogger();

    /**
     * 设置网关文件配置
     *
     * @return void
     */
    abstract protected function setGatewayConfig(string $config = ''): VirtualServiceInterface;

    /**
     * 批量申请开卡
     *
     * @param array $params
     * @param integer $num
     * @return array
     */
    public function batchApplyCard(array $params = [], int $num = 0): array
    {
        return [];
    }

    /**
     * 批量获取卡信息
     *
     * @param array $cardIds
     * @return array
     */
    public function batchGetCardInfo(array $transIds = []): void
    {
    }

    /**
     * 查询卡余额
     *
     * @param array $virtual
     * @return array
     */
    public function inquireBalance(array $virtual = []): array
    {
        return [];
    }

    /**
     * 查询共享卡余额
     *
     * @param array $virtual
     * @return array
     */
    public function inquireShareBalance(array $virtual = []): array
    {
        return [];
    }

    /**
     * 查询卡结算信息
     *
     * @param array $data
     * @return array
     */
    public function inquirySettlement(array $data = []): array
    {
        return [];
    }

    /**
     * 查询卡交易信息
     *
     * @param array $data
     * @return array
     */
    public function inquiryTrade(array $data = []): array
    {
        return [];
    }

    /**
     * 充值
     *
     * @param array $cardTicketData
     * @return array
     */
    public function recharge(array $cardTicketData = []): array
    {
        return [];
    }

    /**
     * 卡退值
     *
     * @param array $cardTicketData
     * @return array
     */
    public function refund(array $cardTicketData = []): array
    {
        return [];
    }

    /**
     * 销卡
     *
     * @param array $virtual
     * @return array
     */
    public function destroy(array $virtual = []): array
    {
        return [];
    }

    /**
     * 冻结
     *
     * @param array $virtual
     * @param int $blockType
     * @return array
     */
    public function block(array $virtual = [], int $blockType = 0): bool
    {
        return false;
    }

    /**
     * 解冻
     *
     * @param array $virtual
     * @return array
     */
    public function unblock(array $virtual = []): bool
    {
        return false;
    }

    /**
     * 查询卡操作
     *
     * @param array $virtual
     * @return array
     */
    public function inquiryCardOperation(array $virtual = []): array
    {
        return [];
    }

    /**
     * 共享卡限制
     *
     * @param array $params
     * @return void
     * @throws \App\Classes\Card\Exceptions\CardException
     */
    public function shareCardLimit(array $params): void
    {
    }

    /**
     * 账户余额查询
     *
     * @param array $params
     * @return array
     * @throws \App\Classes\Card\Exceptions\CardException
     */
    public function accountBalance(array $params = []): array
    {
        return [];
    }

    /**
     * 卡交易通知
     * @param array $content
     * @return string
     */
    public function cardTradeNotify(array $content): string
    {
        return '';
    }

    /**
     * 卡操作通知
     * @param array $content
     * @return string
     */
    public function cardOperateNotify(array $cardTickets, array $content): string
    {
        return '';
    }

    /**
     * 卡转账通知
     * @param array $content
     * @return array
     */
    public function cardTransferNotify(array $cardVirtualInfo, array $content): array
    {
        return ['respond' => 'fail', 'card_ticket_data' => []];
    }

    /**
     * 获取黑名单
     * @return int
     */
    public function getFraudList(): int
    {
        return 0;
    }

    /**
     * 设置黑名单
     * @param array $cardBlackInfo
     * @return int
     */
    public function setFraud(array $cardBlackInfo): int
    {
        return 0;
    }

    /**
     * 创建持卡人
     * @param array $cardHolder
     * @return array
     */
    public function createHolder(array $cardHolder): array
    {
        return [];
    }

    /**
     * 绑定持卡人
     * @param array $virtualInfo
     * @return array
     */
    public function bindCardHolder(array $virtualInfo): array
    {
        return [];
    }
}
