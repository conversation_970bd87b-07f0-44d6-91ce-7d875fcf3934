<?php

use Carbon\Carbon;

/**
 * 是否生产
 * @return bool
 */
function is_production()
{
    return config('app.env') == 'production';
}

/**
 * 是否本地
 * @return bool
 */
function is_local()
{
    return config('app.env') == 'local';
}

/**
 * 信用卡处理
 * @param string $value
 * @return boolean
 */
function get_markcard($value)
{
    return substr_replace(trim($value), 'XXXXXX', 6, strlen(trim($value)) - 10);
}

/**
 * 数据脱敏
 *
 * @param string $value
 * @return string
 */
function get_mark_data($value)
{
    return str_repeat('X', strlen($value));
}

/**
 * 信用卡验证
 * @param string $value
 * @return boolean
 */
function v_creditcard($value)
{
    if (preg_match('/[^0-9 \-]+/', $value)
        || !in_array(strlen($value), array(14, 15, 16, 17, 18, 19))) {
        return false;
    }

    $nCheck = 0;
    $nDigit = 0;
    $bEven  = false;
    $value  = preg_replace('/\D/', '', $value);

    for ($n = strlen($value) - 1; $n >= 0; $n--) {
        $cDigit = $value[$n];
        $nDigit = intval($cDigit);

        if ($bEven) {
            if (($nDigit *= 2) > 9) {
                $nDigit -= 9;
            }
        }

        $nCheck += $nDigit;
        $bEven  = !$bEven;
    }

    return ($nCheck % 10) === 0;
}

/**
 * 获取信用卡卡种类型
 *
 * @param $value
 * @return string
 */
function get_cc_type($value)
{
    switch (strlen($value)) {
        case '14':
            return 'O';
        case '17':
        case '18':
        case '19':
            return 'D';
            break;
        default:
            switch ($value[0]) {
                case '0':
                    return 'O';
                    break;
                case '5':
                    return 'M';
                    break;
                case '3':
                    if (in_array($value[1], array(4, 7))) {
                        return 'A';
                    } else {
                        return 'J';
                    }
                    break;
                case '6':
                    return 'D';
                    break;
                default:
                    return 'V';
            }
    }
}

/**
 * Get merchant url.
 *
 * @param string $path
 * @param mixed $parameters
 * @param bool $secure
 *
 * @return string
 */
function merchant_url($path = '', $parameters = [], $secure = null)
{
    if (url()->isValidUrl($path)) {
        return $path;
    }

    $secure = $secure ?: (config('merchant.https') || config('merchant.secure'));

    return url(merchant_base_path($path), $parameters, $secure);
}

/**
 * Get merchant url.
 *
 * @param string $path
 *
 * @return string
 */
function merchant_base_path($path = '')
{
    $prefix = '/' . trim(config('merchant.route.prefix'), '/');

    $prefix = ($prefix == '/') ? '' : $prefix;

    $path = trim($path, '/');

    if (is_null($path) || strlen($path) == 0) {
        return $prefix ?: '/';
    }

    return $prefix . '/' . $path;
}

/**
 * 金额格式化
 *
 * @param $amount
 * @param int $decimals
 * @return string
 */
function amount_format($amount, $decimals = 2)
{
    if (!is_numeric($amount)) {
        // 记录请求方法位置
        $debugList = debug_backtrace();
        trigger_error(sprintf('amount_format参数错误 %s 第%s行', $debugList[0]['file'], $debugList[0]['line']), E_USER_WARNING);
        return '0.00';
    }

    return number_format($amount, $decimals, '.', '');
}

/**
 * 结算日期
 *
 * @return mixed
 */
function get_settle_date()
{
    return (now('PRC')->toDateString() . ' 11:15:00') > now('PRC')->toDateTimeString() ? now('PRC')->toDateString() : now('PRC')->addDay()->toDateString();
}

/**
 * 默认日期
 *
 * @return string
 */
function get_default_date()
{
    return '1970-01-01';
}

/**
 * 获取当前时间微秒时间戳
 *
 * @return string
 */
function micronow()
{
    $mtimestamp   = sprintf("%.3f", microtime(true)); // 带毫秒的时间戳
    $timestamp    = floor($mtimestamp); // 时间戳
    $milliseconds = round(($mtimestamp - $timestamp) * 1000); // 毫秒

    return date("Y-m-d H:i:s", $timestamp) . '.' . $milliseconds;
}

/**
 * 获取当前微秒级时间戳
 * @return string
 */
function microsecond()
{
    $time = explode(' ', microtime());
    return $time[1] . str_pad(intval($time[0] * 1000000), 6, '0', STR_PAD_LEFT);
}

/**
 * @param $string
 * @param string $trim_chars
 * @return string|string[]|null
 *
 * 指定字符替換
 */
function get_mb_trim($string, $trim_chars = '\s')
{
    $result = preg_replace('/^[' . $trim_chars . ']*(?U)(.*)[' . $trim_chars . ']*$/u', '\\1', $string);
    if (is_null($result)) {
        $result = mb_convert_encoding($string, 'UTF-8', ['ISO-8859-1', 'GB18030', 'Big5', 'Windows-1251']);
        $result = $result ? $result : htmlspecialchars($string);
    }

    return $result;
}

/**
 * 身份证验证
 * @param string $id_card
 * @return boolean
 */
function is_id_card($id_card)
{
    $pattern = '/^([\d]{17}[xX\d]|[\d]{15})$/';
    return preg_match($pattern, $id_card);
}


/**
 * 手机号验证
 * @param string $mobile
 * @return boolean
 */
function is_mobile($mobile)
{
    return preg_match("/^1\d{10}$/", $mobile);
}

/**
 * 根据系统返回响应吗
 *
 * @param string $code
 * @return string
 */
function get_system_code(string $code)
{
    $str = env('APP_NAME', 'Laravel') == 'PunctualPay' ? 'c' : 'p';

    return $str . $code;
}

/**
 * 密码强度检测
 *
 * @param string $password
 * @return string
 */
function check_password_strength(string $password)
{
    return preg_match("/^(?![a-zA-Z]+$)(?!\d+$)(?!\W_+$)[a-zA-Z\d\W_]{8,14}$/u", $password);
}

/**
 * 随机生成14位强度密码
 *
 * @param string $length
 * @return string
 */
function random_password(string $length)
{
    $letters    = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
    $numbers    = '123456789';
    $marks      = "~!@#$%^&*()_+-=[]{};'|,.<>/?";
    $characters = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789~!@#$%^&*()_+-=[]{};'|,.<>/?"; //所有可能的字符集合
    $length     -= 3;
    $letter     = substr($letters, rand(0, strlen($letters) - 1), 1);
    $number     = substr($numbers, rand(0, strlen($numbers) - 1), 1);
    $mark       = substr($marks, rand(0, strlen($marks) - 1), 1);
    $password   = $letter . $number . $mark; //密码初始先满足强度要求

    for ($i = 0; $i < $length; $i++) {
        $index     = rand(0, strlen($characters) - 1);
        $character = substr($characters, $index, 1);
        $password  .= $character;
    }

    return $password;
}

function get_config($configData, $channelSupplierName)
{
    $config = [];

    // 循环组装数据
    foreach ($configData as $value) {
        $config[$value['key']] = $value['value'];
    }

    // 增加日志file
    if (!empty($config)) {
        $config['log'] = ['file' => storage_path('logs/' . $channelSupplierName . '.log')];
    }

    return $config;
}

/**
 * 验证base64字符串
 * @param $str
 * @return bool
 */
function is_base64($str)
{
    // 解码字符串
    $decoded = base64_decode($str, true);

    // 检查解码是否成功
    if ($decoded === false) {
        return false;
    }

    // 重新编码解码后的字符串
    $reEncoded = base64_encode($decoded);

    // 比较原始字符串与重新编码后的字符串
    return $reEncoded === $str;
}

/**
 * 获取主域名
 * @param $url
 * @return false|mixed|string
 */
function get_business($url)
{
    // 查找第一个和最后一个 '.' 的位置
    $firstDot = strpos($url, '.');
    $lastDot  = strrpos($url, '.');

    // 如果没有 '.' 或只有一个 '.'，返回完整的或第一个 '.' 前面的内容
    if ($firstDot === false) {
        return $url; // 没有点，返回完整内容
    } elseif ($firstDot === $lastDot) {
        return substr($url, 0, $firstDot); // 只有一个点，返回点前面的内容
    }

    // 返回第一个 '.' 和最后一个 '.' 之间的内容
    return substr($url, $firstDot + 1, $lastDot - $firstDot - 1);
}

/**
 * 获取Cloudflare 的 Ray ID
 * @return string
 */
function get_cf_ray()
{
    return $_SERVER['HTTP_CF_RAY'] ?? '';
}

/**
 * 验证时区是否合法
 * @param string $timezone
 * @return bool
 */
function validate_timezone(string $timezone): bool {
    try {
        new \DateTimeZone($timezone);
        return true;
    } catch (\Exception $e) {
        return false;
    }
}

function get_carbon()
{
    $timezone = config('app.timezone', 'UTC');
    // 创建一个指定时区的 Carbon 实例
    $carbon = Carbon::now($timezone);

    return $carbon;
}

if (!function_exists('add_office_preview_links')) {
    /**
     * 为HTML中的文件链接添加微软Office在线预览功能
     *
     * @param string $htmlContent HTML内容
     * @param array|null $supportedTypes 支持的文件类型，默认为Office文件类型
     * @return string 处理后的HTML内容
     */
    function add_office_preview_links(string $htmlContent, array $supportedTypes = null): string
    {
        if (empty($htmlContent)) {
            return $htmlContent;
        }

        // 默认支持的文件类型
        if ($supportedTypes === null) {
            $supportedTypes = ['doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx', 'pdf', 'csv'];
        }

        // 微软Office在线预览服务URL
        $previewBaseUrl = 'https://view.officeapps.live.com/op/view.aspx?src=';

        // 正则表达式匹配href属性
        $pattern = '/href=[\'"]([^\'"]+)[\'"]([^>]*>.*?<\/a>)/is';

        return preg_replace_callback($pattern, function ($matches) use ($supportedTypes, $previewBaseUrl) {
            $url = $matches[1];
            $restOfLink = $matches[2];

            // 获取文件扩展名
            $urlWithoutParams = strtok($url, '?');
            $pathInfo = pathinfo($urlWithoutParams);
            $fileExtension = $pathInfo['extension'] ?? '';

            // 检查是否支持预览
            if (in_array(strtolower($fileExtension), array_map('strtolower', $supportedTypes))) {
                $url = $previewBaseUrl . urlencode($url);
            }

            return 'href="' . $url . '"' . $restOfLink;
        }, $htmlContent);
    }
}