{"name": "laravel/laravel", "type": "project", "description": "The Laravel Framework.", "keywords": ["framework", "laravel"], "license": "MIT", "require": {"php": "^7.2.5", "alibabacloud/aliyun-log-php-sdk": "^0.6.3", "amenadiel/jpgraph": "4.0.x", "chillerlan/php-qrcode": "~3.4", "dcat/easy-excel": "^1.0", "dcat/laravel-admin": "^2.2@beta", "dcat/laravel-wherehasin": "^0.3.0", "doctrine/dbal": "2.12.1", "fideloper/proxy": "^4.2", "fruitcake/laravel-cors": "^1.0", "guzzlehttp/guzzle": "^7.4", "jeremeamia/superclosure": "^2.4", "jeremykendall/php-domain-parser": "^5.7", "laravel/framework": "^7.0", "laravel/horizon": "^4.0", "laravel/tinker": "^2.0", "lnatpunblhna/in-textarea": "^1.0", "maatwebsite/excel": "^3.1", "mews/purifier": "~3.0", "mosiboom/dcat-iframe-tab": "^1.2", "nick322/secure-spreadsheet": "^1.0", "orangehill/iseed": "^2.6", "overtrue/laravel-lang": "^4.0", "overtrue/laravel-wechat": "^5.0", "pragmarx/google2fa-laravel": "^2.1", "predis/predis": "~1.1", "spatie/laravel-schemaless-attributes": "^1.7", "youthage/laravel-3des": "^4.0"}, "require-dev": {"barryvdh/laravel-debugbar": "~3.2", "facade/ignition": "^2.0", "fzaninotto/faker": "^1.9.1", "mockery/mockery": "^1.3.1", "nunomaduro/collision": "^4.1", "overtrue/laravel-query-logger": "^1.2", "phpunit/phpunit": "^8.5", "rap2hpoutre/laravel-log-viewer": "^1.6"}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true, "allow-plugins": {"easywechat-composer/easywechat-composer": true}}, "extra": {"laravel": {"dont-discover": []}}, "autoload": {"psr-4": {"App\\": "app/"}, "classmap": ["database/seeds", "database/factories"], "files": ["bootstrap/helpers.php"]}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "minimum-stability": "dev", "prefer-stable": true, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi"], "post-update-cmd": ["@php artisan horizon:publish --ansi"]}}