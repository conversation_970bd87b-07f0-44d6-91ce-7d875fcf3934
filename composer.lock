{"_readme": ["This file locks the dependencies of your project to a known state", "Read more about it at https://getcomposer.org/doc/01-basic-usage.md#installing-dependencies", "This file is @generated automatically"], "content-hash": "95243b89bcda80f1144e3f1372b0d896", "packages": [{"name": "alibabacloud/aliyun-log-php-sdk", "version": "0.6.3", "source": {"type": "git", "url": "https://github.com/alibabacloud-sdk-php/aliyun-log-php-sdk.git", "reference": "ed0bedc246666688c4360cb2fa78216905476f70"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/alibabacloud-sdk-php/aliyun-log-php-sdk/zipball/ed0bedc246666688c4360cb2fa78216905476f70", "reference": "ed0bedc246666688c4360cb2fa78216905476f70", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=7.1.7"}, "type": "library", "autoload": {"files": ["Log_Autoload.php"]}, "notification-url": "https://packagist.org/downloads/", "authors": [{"name": "<PERSON>"}, {"name": "TingTao Sun"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>"}], "description": "The Php SDK of Alibaba log service", "keywords": ["SLS", "alibaba", "sdk"], "support": {"source": "https://github.com/alibabacloud-sdk-php/aliyun-log-php-sdk/tree/0.6.3"}, "time": "2021-09-28T12:59:17+00:00"}, {"name": "amenadiel/jpgraph", "version": "4.0.3", "source": {"type": "git", "url": "https://github.com/HuasoFoundries/jpgraph.git", "reference": "1328d5a2cf0f1ce8f83b79f17d339b34241ace74"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/HuasoFoundries/jpgraph/zipball/1328d5a2cf0f1ce8f83b79f17d339b34241ace74", "reference": "1328d5a2cf0f1ce8f83b79f17d339b34241ace74", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"ext-gd": "*", "php": ">=7.0.0"}, "require-dev": {"codeception/codeception": "^4.0", "codeception/module-asserts": "^1.1", "kint-php/kint": "^3.3", "monolog/monolog": "^2.0", "squizlabs/php_codesniffer": "^3.5", "symfony/dotenv": "~4.2.0", "symfony/event-dispatcher": "~4.2", "symfony/yaml": "4.*"}, "type": "library", "autoload": {"psr-4": {"Amenadiel\\JpGraph\\": "src/", "Amenadiel\\JpGraph\\Plot\\": "src/plot", "Amenadiel\\JpGraph\\Text\\": "src/text", "Amenadiel\\JpGraph\\Util\\": "src/util", "Amenadiel\\JpGraph\\Graph\\": "src/graph", "Amenadiel\\JpGraph\\Image\\": "src/image", "Amenadiel\\JpGraph\\Themes\\": "src/themes", "Amenadiel\\JpGraph\\UnitTest\\": "tests", "Amenadiel\\JpGraph\\Graph\\Axis\\": "src/graph/axis", "Amenadiel\\JpGraph\\Graph\\Tick\\": "src/graph/tick", "Amenadiel\\JpGraph\\Graph\\Scale\\": "src/graph/scale"}}, "notification-url": "https://packagist.org/downloads/", "license": ["QPL-1.0"], "description": "jpGraph, library to make graphs and charts", "homepage": "http://jpgraph.net/", "keywords": ["chart", "data", "graph", "jpgraph", "pie"], "support": {"issues": "https://github.com/huasofoundries/jpgraph/issues", "source": "https://github.com/HuasoFoundries/jpgraph/tree/master"}, "time": "2020-01-07T08:39:02+00:00"}, {"name": "asm89/stack-cors", "version": "1.3.0", "source": {"type": "git", "url": "https://github.com/asm89/stack-cors.git", "reference": "b9c31def6a83f84b4d4a40d35996d375755f0e08"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/asm89/stack-cors/zipball/b9c31def6a83f84b4d4a40d35996d375755f0e08", "reference": "b9c31def6a83f84b4d4a40d35996d375755f0e08", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=5.5.9", "symfony/http-foundation": "~2.7|~3.0|~4.0|~5.0", "symfony/http-kernel": "~2.7|~3.0|~4.0|~5.0"}, "require-dev": {"phpunit/phpunit": "^5.0 || ^4.8.10", "squizlabs/php_codesniffer": "^2.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.2-dev"}}, "autoload": {"psr-4": {"Asm89\\Stack\\": "src/Asm89/Stack/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Cross-origin resource sharing library and stack middleware", "homepage": "https://github.com/asm89/stack-cors", "keywords": ["cors", "stack"], "time": "2019-12-24T22:41:47+00:00"}, {"name": "box/spout", "version": "v3.1.0", "source": {"type": "git", "url": "https://github.com/box/spout.git", "reference": "7964dadc2128f3a00ffa393395b618ea115c8032"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/box/spout/zipball/7964dadc2128f3a00ffa393395b618ea115c8032", "reference": "7964dadc2128f3a00ffa393395b618ea115c8032", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"ext-xmlreader": "*", "ext-zip": "*", "php": ">=7.1.0"}, "require-dev": {"friendsofphp/php-cs-fixer": "^2", "phpunit/phpunit": "^7"}, "suggest": {"ext-iconv": "To handle non UTF-8 CSV files (if \"php-intl\" is not already installed or is too limited)", "ext-intl": "To handle non UTF-8 CSV files (if \"iconv\" is not already installed)"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.1.x-dev"}}, "autoload": {"psr-4": {"Box\\Spout\\": "src/Spout"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "PHP Library to read and write spreadsheet files (CSV, XLSX and ODS), in a fast and scalable way", "homepage": "https://www.github.com/box/spout", "keywords": ["OOXML", "csv", "excel", "memory", "odf", "ods", "office", "open", "php", "read", "scale", "spreadsheet", "stream", "write", "xlsx"], "time": "2019-12-02T21:21:41+00:00"}, {"name": "brick/math", "version": "0.8.15", "source": {"type": "git", "url": "https://github.com/brick/math.git", "reference": "9b08d412b9da9455b210459ff71414de7e6241cd"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/brick/math/zipball/9b08d412b9da9455b210459ff71414de7e6241cd", "reference": "9b08d412b9da9455b210459ff71414de7e6241cd", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"ext-json": "*", "php": "^7.1|^8.0"}, "require-dev": {"php-coveralls/php-coveralls": "^2.2", "phpunit/phpunit": "^7.5.15|^8.5", "vimeo/psalm": "^3.5"}, "type": "library", "autoload": {"psr-4": {"Brick\\Math\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "Arbitrary-precision arithmetic library", "keywords": ["Arbitrary-precision", "BigInteger", "BigRational", "arithmetic", "bigdecimal", "bignum", "brick", "math"], "time": "2020-04-15T15:59:35+00:00"}, {"name": "cakephp/chronos", "version": "2.3.0", "source": {"type": "git", "url": "https://github.com/cakephp/chronos.git", "reference": "3ecd6e7ae191c676570cd1bed51fd561de4606dd"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/cakephp/chronos/zipball/3ecd6e7ae191c676570cd1bed51fd561de4606dd", "reference": "3ecd6e7ae191c676570cd1bed51fd561de4606dd", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=7.2"}, "require-dev": {"cakephp/cakephp-codesniffer": "^4.5", "phpunit/phpunit": "^8.0 || ^9.0"}, "type": "library", "autoload": {"files": ["src/carbon_compat.php"], "psr-4": {"Cake\\Chronos\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://nesbot.com"}, {"name": "The CakePHP Team", "homepage": "http://cakephp.org"}], "description": "A simple API extension for DateTime.", "homepage": "http://cakephp.org", "keywords": ["date", "datetime", "time"], "support": {"irc": "irc://irc.freenode.org/cakephp", "issues": "https://github.com/cakephp/chronos/issues", "source": "https://github.com/cakephp/chronos"}, "time": "2021-10-17T02:44:05+00:00"}, {"name": "caouecs/laravel-lang", "version": "6.1.4", "source": {"type": "git", "url": "https://github.com/caouecs/lang.git", "reference": "18a7845e813e737a56a7f164301d5014b536950c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/caouecs/lang/zipball/18a7845e813e737a56a7f164301d5014b536950c", "reference": "18a7845e813e737a56a7f164301d5014b536950c", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"ext-json": "*"}, "suggest": {"andrey-helldar/laravel-lang-publisher": "Easy installation and update of translation files for your project", "arcanedev/laravel-lang": "Translations manager and checker for <PERSON><PERSON> 5", "overtrue/laravel-lang": "Command to add languages in your project"}, "type": "library", "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "caouecs", "email": "<EMAIL>"}], "description": "Languages for Lara<PERSON>", "keywords": ["lang", "languages", "laravel", "lpm"], "abandoned": "https://github.com/<PERSON><PERSON>-<PERSON>/lang", "time": "2020-07-13T14:35:32+00:00"}, {"name": "chillerlan/php-qrcode", "version": "3.4.1", "source": {"type": "git", "url": "https://github.com/chillerlan/php-qrcode.git", "reference": "468603b687a5fe75c1ff33857a45f1726c7b95a9"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/chillerlan/php-qrcode/zipball/468603b687a5fe75c1ff33857a45f1726c7b95a9", "reference": "468603b687a5fe75c1ff33857a45f1726c7b95a9", "shasum": ""}, "require": {"chillerlan/php-settings-container": "^1.2.2", "ext-mbstring": "*", "php": "^7.2 || ^8.0"}, "require-dev": {"phan/phan": "^3.2.2", "phpunit/phpunit": "^8.5", "setasign/fpdf": "^1.8.2"}, "suggest": {"chillerlan/php-authenticator": "Yet another Google authenticator! Also creates URIs for mobile apps.", "setasign/fpdf": "Required to use the QR FPDF output."}, "type": "library", "autoload": {"psr-4": {"chillerlan\\QRCode\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "homepage": "https://github.com/kazu<PERSON><PERSON>se"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/codemasher"}, {"name": "Contributors", "homepage": "https://github.com/chillerlan/php-qrcode/graphs/contributors"}], "description": "A QR code generator. PHP 7.2+", "homepage": "https://github.com/chillerlan/php-qrcode", "keywords": ["phpqrcode", "qr", "qr code", "qrcode", "qrcode-generator"], "support": {"issues": "https://github.com/chillerlan/php-qrcode/issues", "source": "https://github.com/chillerlan/php-qrcode/tree/3.4.1"}, "funding": [{"url": "https://www.paypal.com/donate?hosted_button_id=WLYUNAT9ZTJZ4", "type": "custom"}, {"url": "https://ko-fi.com/codemasher", "type": "ko_fi"}], "time": "2021-09-03T17:54:45+00:00"}, {"name": "chillerlan/php-settings-container", "version": "1.2.3", "source": {"type": "git", "url": "https://github.com/chillerlan/php-settings-container.git", "reference": "92636df53ad1bc903521d29993de0631e07ca931"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/chillerlan/php-settings-container/zipball/92636df53ad1bc903521d29993de0631e07ca931", "reference": "92636df53ad1bc903521d29993de0631e07ca931", "shasum": ""}, "require": {"ext-json": "*", "php": "^7.2 || ^8.0"}, "require-dev": {"phpunit/phpunit": "^8.5"}, "type": "library", "autoload": {"psr-4": {"chillerlan\\Settings\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/codemasher"}], "description": "A container class for immutable settings objects. Not a DI container. PHP 7.2+", "homepage": "https://github.com/chillerlan/php-settings-container", "keywords": ["PHP7", "Settings", "container", "helper"], "support": {"issues": "https://github.com/chillerlan/php-settings-container/issues", "source": "https://github.com/chillerlan/php-settings-container"}, "funding": [{"url": "https://www.paypal.com/donate?hosted_button_id=WLYUNAT9ZTJZ4", "type": "custom"}, {"url": "https://ko-fi.com/codemasher", "type": "ko_fi"}], "time": "2023-12-12T13:50:01+00:00"}, {"name": "dcat/easy-excel", "version": "1.0.3", "source": {"type": "git", "url": "https://github.com/jqhph/easy-excel.git", "reference": "badb6734420a77263f82e35ac218d2b4223fe833"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/jqhph/easy-excel/zipball/badb6734420a77263f82e35ac218d2b4223fe833", "reference": "badb6734420a77263f82e35ac218d2b4223fe833", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"box/spout": "~3", "league/flysystem": "~1", "php": ">=7.1.0"}, "require-dev": {"friendsofphp/php-cs-fixer": "^2", "phpunit/phpunit": "~7|~8.0"}, "type": "library", "autoload": {"psr-4": {"Dcat\\EasyExcel\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "jqh", "email": "<EMAIL>"}], "description": "使用简单实用的语义化接口快速读写Excel文件", "homepage": "https://github.com/jqhph/easy-excel", "keywords": ["box spout", "csv", "easy excel", "excel", "ods", "office", "read", "spreadsheet", "stream", "xlsx"], "time": "2020-09-21T02:19:34+00:00"}, {"name": "dcat/laravel-admin", "version": "2.2.3-beta", "source": {"type": "git", "url": "https://github.com/jqhph/dcat-admin.git", "reference": "f8ef27cc4d6a79dc346f89d0efb925d4e28ee763"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/jqhph/dcat-admin/zipball/f8ef27cc4d6a79dc346f89d0efb925d4e28ee763", "reference": "f8ef27cc4d6a79dc346f89d0efb925d4e28ee763", "shasum": ""}, "require": {"doctrine/dbal": "^2.6|^3.0", "laravel/framework": "~5.5|~6.0|~7.0|~8.0|~9.0|~10.0", "php": ">=7.1.0", "spatie/eloquent-sortable": "3.*|4.*"}, "require-dev": {"fzaninotto/faker": "^1.4", "laravel/dusk": "~5.9|~6", "mockery/mockery": "^1.0", "phpstan/phpstan": "^0.12.0", "phpunit/phpunit": "^7.5|~9"}, "type": "library", "extra": {"laravel": {"providers": ["Dcat\\Admin\\AdminServiceProvider"]}}, "autoload": {"files": ["src/Support/helpers.php"], "psr-4": {"Dcat\\Admin\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "jqh", "email": "<EMAIL>"}], "description": "dcat admin", "homepage": "https://github.com/jqhph/dcat-admin", "keywords": ["admin", "dcat", "form", "grid", "laravel", "laravel admin"], "support": {"issues": "https://github.com/jqhph/dcat-admin/issues", "source": "https://github.com/jqhph/dcat-admin/tree/2.2.3-beta"}, "time": "2023-02-15T08:59:42+00:00"}, {"name": "dcat/laravel-where<PERSON>in", "version": "0.3.0", "source": {"type": "git", "url": "https://github.com/jqhph/laravel-wherehasin.git", "reference": "aaffd4de1b0293d895ca887839af63054b94a059"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/jqhph/laravel-wherehasin/zipball/aaffd4de1b0293d895ca887839af63054b94a059", "reference": "aaffd4de1b0293d895ca887839af63054b94a059", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=7.0"}, "require-dev": {"fzaninotto/faker": "^1.4", "laravel/laravel": ">=5.5", "mockery/mockery": "^1.0", "phpunit/phpunit": "^7.5"}, "type": "library", "extra": {"laravel": {"providers": ["Dcat\\Laravel\\Database\\WhereHasInServiceProvider"]}}, "autoload": {"psr-4": {"Dcat\\Laravel\\Database\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "jqh", "email": "<EMAIL>"}], "description": "Laravel ORM whereHasIn", "homepage": "https://github.com/jqhph/dcat-admin", "keywords": ["laravel", "orm", "relation", "whereHas"], "time": "2020-07-29T09:58:02+00:00"}, {"name": "dnoegel/php-xdg-base-dir", "version": "v0.1.1", "source": {"type": "git", "url": "https://github.com/dnoegel/php-xdg-base-dir.git", "reference": "8f8a6e48c5ecb0f991c2fdcf5f154a47d85f9ffd"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/dnoegel/php-xdg-base-dir/zipball/8f8a6e48c5ecb0f991c2fdcf5f154a47d85f9ffd", "reference": "8f8a6e48c5ecb0f991c2fdcf5f154a47d85f9ffd", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=5.3.2"}, "require-dev": {"phpunit/phpunit": "~7.0|~6.0|~5.0|~4.8.35"}, "type": "library", "autoload": {"psr-4": {"XdgBaseDir\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "implementation of xdg base directory specification for php", "time": "2019-12-04T15:06:13+00:00"}, {"name": "doctrine/cache", "version": "1.10.2", "source": {"type": "git", "url": "https://github.com/doctrine/cache.git", "reference": "13e3381b25847283a91948d04640543941309727"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/cache/zipball/13e3381b25847283a91948d04640543941309727", "reference": "13e3381b25847283a91948d04640543941309727", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": "~7.1 || ^8.0"}, "conflict": {"doctrine/common": ">2.2,<2.4"}, "require-dev": {"alcaeus/mongo-php-adapter": "^1.1", "doctrine/coding-standard": "^6.0", "mongodb/mongodb": "^1.1", "phpunit/phpunit": "^7.0", "predis/predis": "~1.0"}, "suggest": {"alcaeus/mongo-php-adapter": "Required to use legacy MongoDB driver"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.9.x-dev"}}, "autoload": {"psr-4": {"Doctrine\\Common\\Cache\\": "lib/Doctrine/Common/Cache"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}], "description": "PHP Doctrine Cache library is a popular cache implementation that supports many different drivers such as redis, memcache, apc, mongodb and others.", "homepage": "https://www.doctrine-project.org/projects/cache.html", "keywords": ["abstraction", "apcu", "cache", "caching", "couchdb", "memcached", "php", "redis", "xcache"], "support": {"issues": "https://github.com/doctrine/cache/issues", "source": "https://github.com/doctrine/cache/tree/1.10.x"}, "funding": [{"url": "https://www.doctrine-project.org/sponsorship.html", "type": "custom"}, {"url": "https://www.patreon.com/phpdoctrine", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/doctrine%2Fcache", "type": "tidelift"}], "time": "2020-07-07T18:54:01+00:00"}, {"name": "doctrine/dbal", "version": "2.12.1", "source": {"type": "git", "url": "https://github.com/doctrine/dbal.git", "reference": "adce7a954a1c2f14f85e94aed90c8489af204086"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/dbal/zipball/adce7a954a1c2f14f85e94aed90c8489af204086", "reference": "adce7a954a1c2f14f85e94aed90c8489af204086", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"doctrine/cache": "^1.0", "doctrine/event-manager": "^1.0", "ext-pdo": "*", "php": "^7.3 || ^8"}, "require-dev": {"doctrine/coding-standard": "^8.1", "jetbrains/phpstorm-stubs": "^2019.1", "phpstan/phpstan": "^0.12.40", "phpunit/phpunit": "^9.4", "psalm/plugin-phpunit": "^0.10.0", "symfony/console": "^2.0.5|^3.0|^4.0|^5.0", "vimeo/psalm": "^3.17.2"}, "suggest": {"symfony/console": "For helpful console commands such as SQL execution and import of files."}, "bin": ["bin/doctrine-dbal"], "type": "library", "extra": {"branch-alias": {"dev-master": "4.0.x-dev"}}, "autoload": {"psr-4": {"Doctrine\\DBAL\\": "lib/Doctrine/DBAL"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Powerful PHP database abstraction layer (DBAL) with many features for database schema introspection and management.", "homepage": "https://www.doctrine-project.org/projects/dbal.html", "keywords": ["abstraction", "database", "db2", "dbal", "ma<PERSON>b", "mssql", "mysql", "oci8", "oracle", "pdo", "pgsql", "postgresql", "queryobject", "sasql", "sql", "sqlanywhere", "sqlite", "sqlserver", "sqlsrv"], "support": {"issues": "https://github.com/doctrine/dbal/issues", "source": "https://github.com/doctrine/dbal/tree/2.12.1"}, "funding": [{"url": "https://www.doctrine-project.org/sponsorship.html", "type": "custom"}, {"url": "https://www.patreon.com/phpdoctrine", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/doctrine%2Fdbal", "type": "tidelift"}], "time": "2020-11-14T20:26:58+00:00"}, {"name": "doctrine/event-manager", "version": "1.1.1", "source": {"type": "git", "url": "https://github.com/doctrine/event-manager.git", "reference": "41370af6a30faa9dc0368c4a6814d596e81aba7f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/event-manager/zipball/41370af6a30faa9dc0368c4a6814d596e81aba7f", "reference": "41370af6a30faa9dc0368c4a6814d596e81aba7f", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": "^7.1 || ^8.0"}, "conflict": {"doctrine/common": "<2.9@dev"}, "require-dev": {"doctrine/coding-standard": "^6.0", "phpunit/phpunit": "^7.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Doctrine\\Common\\": "lib/Doctrine/Common"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The Doctrine Event Manager is a simple PHP event system that was built to be used with the various Doctrine projects.", "homepage": "https://www.doctrine-project.org/projects/event-manager.html", "keywords": ["event", "event dispatcher", "event manager", "event system", "events"], "support": {"issues": "https://github.com/doctrine/event-manager/issues", "source": "https://github.com/doctrine/event-manager/tree/1.1.x"}, "funding": [{"url": "https://www.doctrine-project.org/sponsorship.html", "type": "custom"}, {"url": "https://www.patreon.com/phpdoctrine", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/doctrine%2Fevent-manager", "type": "tidelift"}], "time": "2020-05-29T18:28:51+00:00"}, {"name": "doctrine/inflector", "version": "2.0.3", "source": {"type": "git", "url": "https://github.com/doctrine/inflector.git", "reference": "9cf661f4eb38f7c881cac67c75ea9b00bf97b210"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/inflector/zipball/9cf661f4eb38f7c881cac67c75ea9b00bf97b210", "reference": "9cf661f4eb38f7c881cac67c75ea9b00bf97b210", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": "^7.2 || ^8.0"}, "require-dev": {"doctrine/coding-standard": "^7.0", "phpstan/phpstan": "^0.11", "phpstan/phpstan-phpunit": "^0.11", "phpstan/phpstan-strict-rules": "^0.11", "phpunit/phpunit": "^7.0 || ^8.0 || ^9.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "autoload": {"psr-4": {"Doctrine\\Inflector\\": "lib/Doctrine/Inflector"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}], "description": "PHP Doctrine Inflector is a small library that can perform string manipulations with regard to upper/lowercase and singular/plural forms of words.", "homepage": "https://www.doctrine-project.org/projects/inflector.html", "keywords": ["inflection", "inflector", "lowercase", "manipulation", "php", "plural", "singular", "strings", "uppercase", "words"], "time": "2020-05-29T15:13:26+00:00"}, {"name": "doctrine/lexer", "version": "1.2.1", "source": {"type": "git", "url": "https://github.com/doctrine/lexer.git", "reference": "e864bbf5904cb8f5bb334f99209b48018522f042"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/lexer/zipball/e864bbf5904cb8f5bb334f99209b48018522f042", "reference": "e864bbf5904cb8f5bb334f99209b48018522f042", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": "^7.2 || ^8.0"}, "require-dev": {"doctrine/coding-standard": "^6.0", "phpstan/phpstan": "^0.11.8", "phpunit/phpunit": "^8.2"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.2.x-dev"}}, "autoload": {"psr-4": {"Doctrine\\Common\\Lexer\\": "lib/Doctrine/Common/Lexer"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}], "description": "PHP Doctrine Lexer parser library that can be used in Top-Down, Recursive Descent Parsers.", "homepage": "https://www.doctrine-project.org/projects/lexer.html", "keywords": ["annotations", "doc<PERSON>", "lexer", "parser", "php"], "time": "2020-05-25T17:44:05+00:00"}, {"name": "dragonmantank/cron-expression", "version": "v2.3.0", "source": {"type": "git", "url": "https://github.com/dragonmantank/cron-expression.git", "reference": "72b6fbf76adb3cf5bc0db68559b33d41219aba27"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/dragonmantank/cron-expression/zipball/72b6fbf76adb3cf5bc0db68559b33d41219aba27", "reference": "72b6fbf76adb3cf5bc0db68559b33d41219aba27", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": "^7.0"}, "require-dev": {"phpunit/phpunit": "^6.4|^7.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.3-dev"}}, "autoload": {"psr-4": {"Cron\\": "src/Cron/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/dragonmantank"}], "description": "CRON for PHP: Calculate the next or previous run date and determine if a CRON expression is due", "keywords": ["cron", "schedule"], "time": "2019-03-31T00:38:28+00:00"}, {"name": "<PERSON><PERSON><PERSON><PERSON>-composer/easywechat-composer", "version": "1.4.1", "source": {"type": "git", "url": "https://github.com/mingyoung/easywechat-composer.git", "reference": "3fc6a7ab6d3853c0f4e2922539b56cc37ef361cd"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/mingyoung/easywechat-composer/zipball/3fc6a7ab6d3853c0f4e2922539b56cc37ef361cd", "reference": "3fc6a7ab6d3853c0f4e2922539b56cc37ef361cd", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"composer-plugin-api": "^1.0 || ^2.0", "php": ">=7.0"}, "require-dev": {"composer/composer": "^1.0 || ^2.0", "phpunit/phpunit": "^6.5 || ^7.0"}, "type": "composer-plugin", "extra": {"class": "EasyWeChatComposer\\Plugin"}, "autoload": {"psr-4": {"EasyWeChatComposer\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "张铭阳", "email": "mingyoung<PERSON><EMAIL>"}], "description": "The composer plugin for EasyWeChat", "support": {"issues": "https://github.com/mingyoung/easywechat-composer/issues", "source": "https://github.com/mingyoung/easywechat-composer/tree/1.4.1"}, "time": "2021-07-05T04:03:22+00:00"}, {"name": "egulias/email-validator", "version": "2.1.18", "source": {"type": "git", "url": "https://github.com/egulias/EmailValidator.git", "reference": "cfa3d44471c7f5bfb684ac2b0da7114283d78441"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/egulias/EmailValidator/zipball/cfa3d44471c7f5bfb684ac2b0da7114283d78441", "reference": "cfa3d44471c7f5bfb684ac2b0da7114283d78441", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"doctrine/lexer": "^1.0.1", "php": ">=5.5", "symfony/polyfill-intl-idn": "^1.10"}, "require-dev": {"dominicsayers/isemail": "^3.0.7", "phpunit/phpunit": "^4.8.36|^7.5.15", "satooshi/php-coveralls": "^1.0.1"}, "suggest": {"ext-intl": "PHP Internationalization Libraries are required to use the SpoofChecking validation"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.1.x-dev"}}, "autoload": {"psr-4": {"Egulias\\EmailValidator\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>"}], "description": "A library for validating emails against several RFCs", "homepage": "https://github.com/egulias/EmailValidator", "keywords": ["email", "emailvalidation", "emailvalidator", "validation", "validator"], "time": "2020-06-16T20:11:17+00:00"}, {"name": "ezyang/htmlpurifier", "version": "v4.13.0", "source": {"type": "git", "url": "https://github.com/ezyang/htmlpurifier.git", "reference": "08e27c97e4c6ed02f37c5b2b20488046c8d90d75"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/ezyang/htmlpurifier/zipball/08e27c97e4c6ed02f37c5b2b20488046c8d90d75", "reference": "08e27c97e4c6ed02f37c5b2b20488046c8d90d75", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=5.2"}, "require-dev": {"simpletest/simpletest": "dev-master#72de02a7b80c6bb8864ef9bf66d41d2f58f826bd"}, "type": "library", "autoload": {"psr-0": {"HTMLPurifier": "library/"}, "files": ["library/HTMLPurifier.composer.php"], "exclude-from-classmap": ["/library/HTMLPurifier/Language/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["LGPL-2.1-or-later"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://ezyang.com"}], "description": "Standards compliant HTML filter written in PHP", "homepage": "http://htmlpurifier.org/", "keywords": ["html"], "time": "2020-06-29T00:56:53+00:00"}, {"name": "fideloper/proxy", "version": "4.4.0", "source": {"type": "git", "url": "https://github.com/fideloper/TrustedProxy.git", "reference": "9beebf48a1c344ed67c1d36bb1b8709db7c3c1a8"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/fideloper/TrustedProxy/zipball/9beebf48a1c344ed67c1d36bb1b8709db7c3c1a8", "reference": "9beebf48a1c344ed67c1d36bb1b8709db7c3c1a8", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"illuminate/contracts": "^5.0|^6.0|^7.0|^8.0", "php": ">=5.4.0"}, "require-dev": {"illuminate/http": "^5.0|^6.0|^7.0|^8.0", "mockery/mockery": "^1.0", "phpunit/phpunit": "^6.0"}, "type": "library", "extra": {"laravel": {"providers": ["Fideloper\\Proxy\\TrustedProxyServiceProvider"]}}, "autoload": {"psr-4": {"Fideloper\\Proxy\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Set trusted proxies for <PERSON><PERSON>", "keywords": ["load balancing", "proxy", "trusted proxy"], "time": "2020-06-23T01:36:47+00:00"}, {"name": "fruitcake/laravel-cors", "version": "v1.0.6", "source": {"type": "git", "url": "https://github.com/fruitcake/laravel-cors.git", "reference": "1d127dbec313e2e227d65e0c483765d8d7559bf6"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/fruitcake/laravel-cors/zipball/1d127dbec313e2e227d65e0c483765d8d7559bf6", "reference": "1d127dbec313e2e227d65e0c483765d8d7559bf6", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"asm89/stack-cors": "^1.3", "illuminate/contracts": "^5.5|^6.0|^7.0|^8.0", "illuminate/support": "^5.5|^6.0|^7.0|^8.0", "php": ">=7", "symfony/http-foundation": "^3.3|^4.0|^5.0", "symfony/http-kernel": "^3.3|^4.0|^5.0"}, "require-dev": {"laravel/framework": "^5.5|^6.0|^7.0|^8.0", "orchestra/testbench": "^3.5|^4.0|^5.0|^6.0", "phpro/grumphp": "^0.16|^0.17", "phpunit/phpunit": "^6.0|^7.0|^8.0", "squizlabs/php_codesniffer": "^3.5"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0-dev"}, "laravel": {"providers": ["Fruitcake\\Cors\\CorsServiceProvider"]}}, "autoload": {"psr-4": {"Fruitcake\\Cors\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Fruitcake", "homepage": "https://fruitcake.nl"}, {"name": "Barry vd. Heuvel", "email": "<EMAIL>"}], "description": "Adds CORS (Cross-Origin Resource Sharing) headers support in your Laravel application", "keywords": ["api", "cors", "crossdomain", "laravel"], "time": "2020-04-28T08:47:37+00:00"}, {"name": "guzzlehttp/guzzle", "version": "7.4.0", "source": {"type": "git", "url": "https://github.com/guzzle/guzzle.git", "reference": "868b3571a039f0ebc11ac8f344f4080babe2cb94"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/guzzle/zipball/868b3571a039f0ebc11ac8f344f4080babe2cb94", "reference": "868b3571a039f0ebc11ac8f344f4080babe2cb94", "shasum": ""}, "require": {"ext-json": "*", "guzzlehttp/promises": "^1.5", "guzzlehttp/psr7": "^1.8.3 || ^2.1", "php": "^7.2.5 || ^8.0", "psr/http-client": "^1.0", "symfony/deprecation-contracts": "^2.2"}, "provide": {"psr/http-client-implementation": "1.0"}, "require-dev": {"bamarni/composer-bin-plugin": "^1.4.1", "ext-curl": "*", "php-http/client-integration-tests": "^3.0", "phpunit/phpunit": "^8.5.5 || ^9.3.5", "psr/log": "^1.1 || ^2.0 || ^3.0"}, "suggest": {"ext-curl": "Required for CURL handler support", "ext-intl": "Required for Internationalized Domain Name (IDN) support", "psr/log": "Required for using the Log middleware"}, "type": "library", "extra": {"branch-alias": {"dev-master": "7.4-dev"}}, "autoload": {"psr-4": {"GuzzleHttp\\": "src/"}, "files": ["src/functions_include.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}, {"name": "<PERSON>", "email": "jereme<PERSON>@gmail.com", "homepage": "https://github.com/jeremeamia"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/gmponos"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Nyholm"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/sagikazarmark"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Tobion"}], "description": "Guzzle is a PHP HTTP client library", "keywords": ["client", "curl", "framework", "http", "http client", "psr-18", "psr-7", "rest", "web service"], "support": {"issues": "https://github.com/guzzle/guzzle/issues", "source": "https://github.com/guzzle/guzzle/tree/7.4.0"}, "funding": [{"url": "https://github.com/GrahamCampbell", "type": "github"}, {"url": "https://github.com/Nyholm", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/guzzlehttp/guzzle", "type": "tidelift"}], "time": "2021-10-18T09:52:00+00:00"}, {"name": "guzzlehttp/promises", "version": "1.5.1", "source": {"type": "git", "url": "https://github.com/guzzle/promises.git", "reference": "fe752aedc9fd8fcca3fe7ad05d419d32998a06da"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/promises/zipball/fe752aedc9fd8fcca3fe7ad05d419d32998a06da", "reference": "fe752aedc9fd8fcca3fe7ad05d419d32998a06da", "shasum": ""}, "require": {"php": ">=5.5"}, "require-dev": {"symfony/phpunit-bridge": "^4.4 || ^5.1"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.5-dev"}}, "autoload": {"psr-4": {"GuzzleHttp\\Promise\\": "src/"}, "files": ["src/functions_include.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Nyholm"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Tobion"}], "description": "Guzzle promises library", "keywords": ["promise"], "support": {"issues": "https://github.com/guzzle/promises/issues", "source": "https://github.com/guzzle/promises/tree/1.5.1"}, "funding": [{"url": "https://github.com/GrahamCampbell", "type": "github"}, {"url": "https://github.com/Nyholm", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/guzzlehttp/promises", "type": "tidelift"}], "time": "2021-10-22T20:56:57+00:00"}, {"name": "guzzlehttp/psr7", "version": "2.1.0", "source": {"type": "git", "url": "https://github.com/guzzle/psr7.git", "reference": "089edd38f5b8abba6cb01567c2a8aaa47cec4c72"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/psr7/zipball/089edd38f5b8abba6cb01567c2a8aaa47cec4c72", "reference": "089edd38f5b8abba6cb01567c2a8aaa47cec4c72", "shasum": ""}, "require": {"php": "^7.2.5 || ^8.0", "psr/http-factory": "^1.0", "psr/http-message": "^1.0", "ralouphie/getallheaders": "^3.0"}, "provide": {"psr/http-factory-implementation": "1.0", "psr/http-message-implementation": "1.0"}, "require-dev": {"bamarni/composer-bin-plugin": "^1.4.1", "http-interop/http-factory-tests": "^0.9", "phpunit/phpunit": "^8.5.8 || ^9.3.10"}, "suggest": {"laminas/laminas-httphandlerrunner": "Emit PSR-7 responses"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.1-dev"}}, "autoload": {"psr-4": {"GuzzleHttp\\Psr7\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/gmponos"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Nyholm"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/sagikazarmark"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Tobion"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://sagikazarmark.hu"}], "description": "PSR-7 message implementation that also provides common utility methods", "keywords": ["http", "message", "psr-7", "request", "response", "stream", "uri", "url"], "support": {"issues": "https://github.com/guzzle/psr7/issues", "source": "https://github.com/guzzle/psr7/tree/2.1.0"}, "funding": [{"url": "https://github.com/GrahamCampbell", "type": "github"}, {"url": "https://github.com/Nyholm", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/guzzlehttp/psr7", "type": "tidelift"}], "time": "2021-10-06T17:43:30+00:00"}, {"name": "jeremeamia/superclosure", "version": "2.4.0", "source": {"type": "git", "url": "https://github.com/jeremeamia/super_closure.git", "reference": "5707d5821b30b9a07acfb4d76949784aaa0e9ce9"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/jeremeamia/super_closure/zipball/5707d5821b30b9a07acfb4d76949784aaa0e9ce9", "reference": "5707d5821b30b9a07acfb4d76949784aaa0e9ce9", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"nikic/php-parser": "^1.2|^2.0|^3.0|^4.0", "php": ">=5.4", "symfony/polyfill-php56": "^1.0"}, "require-dev": {"phpunit/phpunit": "^4.0|^5.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.4-dev"}}, "autoload": {"psr-4": {"SuperClosure\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "jereme<PERSON>@gmail.com", "homepage": "https://github.com/jeremeamia", "role": "Developer"}], "description": "Serialize Closure objects, including their context and binding", "homepage": "https://github.com/jeremeamia/super_closure", "keywords": ["closure", "function", "lambda", "parser", "serializable", "serialize", "tokenizer"], "abandoned": "opis/closure", "time": "2018-03-21T22:21:57+00:00"}, {"name": "jere<PERSON><PERSON><PERSON>l/php-domain-parser", "version": "5.7.2", "source": {"type": "git", "url": "https://github.com/jeremykendall/php-domain-parser.git", "reference": "a2f61eb1fa1ebe0ea67a64b5d631495c304a189b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/jeremy<PERSON><PERSON>l/php-domain-parser/zipball/a2f61eb1fa1ebe0ea67a64b5d631495c304a189b", "reference": "a2f61eb1fa1ebe0ea67a64b5d631495c304a189b", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"ext-intl": "*", "ext-json": "*", "php": "^7.2.5 || ^8.0", "psr/log": "^1.1", "psr/simple-cache": "^1.0.1"}, "require-dev": {"composer/composer": "^1.6 || ^2.0", "friendsofphp/php-cs-fixer": "^2.16", "mikey179/vfsstream": "^1.6.7", "phpstan/phpstan": "^0.12", "phpstan/phpstan-phpunit": "^0.12", "phpstan/phpstan-strict-rules": "^0.12", "phpunit/phpunit": "^8.0 || ^9.3"}, "suggest": {"ext-curl": "To use the package http client", "league/uri": "To parse URL and validate host", "psr/simple-cache-implementation": "To enable using other cache providers"}, "bin": ["bin/update-psl"], "type": "library", "extra": {"branch-alias": {"dev-master": "5.x-dev"}}, "autoload": {"psr-4": {"Pdp\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "homepage": "http://about.me/jere<PERSON><PERSON><PERSON>l", "role": "Developer"}, {"name": "<PERSON><PERSON><PERSON>", "homepage": "http://nyamsprod.com", "role": "Developer"}, {"name": "Contributors", "homepage": "https://github.com/jeremy<PERSON><PERSON>l/php-domain-parser/graphs/contributors"}], "description": "Public Suffix List based URL parsing implemented in PHP.", "homepage": "https://github.com/jeremykendall/php-domain-parser", "keywords": ["PSL", "Public Suffix List", "Top Level Domains", "domain parsing", "iana", "icann", "idn", "tld"], "support": {"issues": "https://github.com/jeremy<PERSON>dall/php-domain-parser/issues", "source": "https://github.com/jeremykendall/php-domain-parser"}, "funding": [{"url": "https://github.com/sponsors/nyamsprod", "type": "github"}], "time": "2020-10-26T06:41:32+00:00"}, {"name": "laravel/framework", "version": "v7.20.0", "source": {"type": "git", "url": "https://github.com/laravel/framework.git", "reference": "682ea946bc136aa686d5a64940ab3d4a24d5a613"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/laravel/framework/zipball/682ea946bc136aa686d5a64940ab3d4a24d5a613", "reference": "682ea946bc136aa686d5a64940ab3d4a24d5a613", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"doctrine/inflector": "^1.4|^2.0", "dragonmantank/cron-expression": "^2.0", "egulias/email-validator": "^2.1.10", "ext-json": "*", "ext-mbstring": "*", "ext-openssl": "*", "league/commonmark": "^1.3", "league/flysystem": "^1.0.34", "monolog/monolog": "^2.0", "nesbot/carbon": "^2.17", "opis/closure": "^3.1", "php": "^7.2.5", "psr/container": "^1.0", "psr/simple-cache": "^1.0", "ramsey/uuid": "^3.7|^4.0", "swiftmailer/swiftmailer": "^6.0", "symfony/console": "^5.0", "symfony/error-handler": "^5.0", "symfony/finder": "^5.0", "symfony/http-foundation": "^5.0", "symfony/http-kernel": "^5.0", "symfony/mime": "^5.0", "symfony/polyfill-php73": "^1.17", "symfony/process": "^5.0", "symfony/routing": "^5.0", "symfony/var-dumper": "^5.0", "tijsverkoyen/css-to-inline-styles": "^2.2.2", "vlucas/phpdotenv": "^4.0", "voku/portable-ascii": "^1.4.8"}, "conflict": {"tightenco/collect": "<5.5.33"}, "provide": {"psr/container-implementation": "1.0"}, "replace": {"illuminate/auth": "self.version", "illuminate/broadcasting": "self.version", "illuminate/bus": "self.version", "illuminate/cache": "self.version", "illuminate/config": "self.version", "illuminate/console": "self.version", "illuminate/container": "self.version", "illuminate/contracts": "self.version", "illuminate/cookie": "self.version", "illuminate/database": "self.version", "illuminate/encryption": "self.version", "illuminate/events": "self.version", "illuminate/filesystem": "self.version", "illuminate/hashing": "self.version", "illuminate/http": "self.version", "illuminate/log": "self.version", "illuminate/mail": "self.version", "illuminate/notifications": "self.version", "illuminate/pagination": "self.version", "illuminate/pipeline": "self.version", "illuminate/queue": "self.version", "illuminate/redis": "self.version", "illuminate/routing": "self.version", "illuminate/session": "self.version", "illuminate/support": "self.version", "illuminate/testing": "self.version", "illuminate/translation": "self.version", "illuminate/validation": "self.version", "illuminate/view": "self.version"}, "require-dev": {"aws/aws-sdk-php": "^3.0", "doctrine/dbal": "^2.6", "filp/whoops": "^2.4", "guzzlehttp/guzzle": "^6.3.1|^7.0", "league/flysystem-cached-adapter": "^1.0", "mockery/mockery": "^1.3.1", "moontoast/math": "^1.1", "orchestra/testbench-core": "^5.0", "pda/pheanstalk": "^4.0", "phpunit/phpunit": "^8.4|^9.0", "predis/predis": "^1.1.1", "symfony/cache": "^5.0"}, "suggest": {"aws/aws-sdk-php": "Required to use the SQS queue driver, DynamoDb failed job storage and SES mail driver (^3.0).", "doctrine/dbal": "Required to rename columns and drop SQLite columns (^2.6).", "ext-ftp": "Required to use the Flysystem FTP driver.", "ext-gd": "Required to use Illuminate\\Http\\Testing\\FileFactory::image().", "ext-memcached": "Required to use the memcache cache driver.", "ext-pcntl": "Required to use all features of the queue worker.", "ext-posix": "Required to use all features of the queue worker.", "ext-redis": "Required to use the Redis cache and queue drivers (^4.0|^5.0).", "filp/whoops": "Required for friendly error pages in development (^2.4).", "fzaninotto/faker": "Required to use the eloquent factory builder (^1.9.1).", "guzzlehttp/guzzle": "Required to use the HTTP Client, Mailgun mail driver and the ping methods on schedules (^6.3.1|^7.0).", "laravel/tinker": "Required to use the tinker console command (^2.0).", "league/flysystem-aws-s3-v3": "Required to use the Flysystem S3 driver (^1.0).", "league/flysystem-cached-adapter": "Required to use the Flysystem cache (^1.0).", "league/flysystem-sftp": "Required to use the Flysystem SFTP driver (^1.0).", "mockery/mockery": "Required to use mocking (^1.3.1).", "moontoast/math": "Required to use ordered UUIDs (^1.1).", "nyholm/psr7": "Required to use PSR-7 bridging features (^1.2).", "pda/pheanstalk": "Required to use the beanstalk queue driver (^4.0).", "phpunit/phpunit": "Required to use assertions and run tests (^8.4|^9.0).", "psr/http-message": "Required to allow Storage::put to accept a StreamInterface (^1.0).", "pusher/pusher-php-server": "Required to use the <PERSON><PERSON><PERSON> broadcast driver (^4.0).", "symfony/cache": "Required to PSR-6 cache bridge (^5.0).", "symfony/filesystem": "Required to create relative storage directory symbolic links (^5.0).", "symfony/psr-http-message-bridge": "Required to use PSR-7 bridging features (^2.0).", "wildbit/swiftmailer-postmark": "Required to use Postmark mail driver (^3.0)."}, "type": "library", "extra": {"branch-alias": {"dev-master": "7.x-dev"}}, "autoload": {"files": ["src/Illuminate/Foundation/helpers.php", "src/Illuminate/Support/helpers.php"], "psr-4": {"Illuminate\\": "src/Illuminate/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The Laravel Framework.", "homepage": "https://laravel.com", "keywords": ["framework", "laravel"], "time": "2020-07-14T13:42:44+00:00"}, {"name": "laravel/horizon", "version": "v4.3.5", "source": {"type": "git", "url": "https://github.com/laravel/horizon.git", "reference": "b3fba0daaaaf5e84197b06dd25f3b27bb7301171"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/laravel/horizon/zipball/b3fba0daaaaf5e84197b06dd25f3b27bb7301171", "reference": "b3fba0daaaaf5e84197b06dd25f3b27bb7301171", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"cakephp/chronos": "^2.0", "ext-json": "*", "ext-pcntl": "*", "ext-posix": "*", "illuminate/contracts": "^7.0", "illuminate/queue": "^7.0", "illuminate/support": "^7.0", "php": "^7.2", "ramsey/uuid": "^3.5|^4.0", "symfony/error-handler": "^5.0", "symfony/process": "^5.0"}, "require-dev": {"mockery/mockery": "^1.0", "orchestra/testbench": "^5.0", "phpunit/phpunit": "^8.0", "predis/predis": "^1.1"}, "suggest": {"ext-redis": "Required to use the Redis PHP driver.", "predis/predis": "Required when not using the Redis PHP driver (^1.1)."}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.x-dev"}, "laravel": {"providers": ["Laravel\\Horizon\\HorizonServiceProvider"], "aliases": {"Horizon": "Laravel\\Horizon\\Horizon"}}}, "autoload": {"psr-4": {"Laravel\\Horizon\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Dashboard and code-driven configuration for Laravel queues.", "keywords": ["laravel", "queue"], "support": {"issues": "https://github.com/laravel/horizon/issues", "source": "https://github.com/laravel/horizon/tree/4.x"}, "time": "2020-09-08T13:19:23+00:00"}, {"name": "laravel/tinker", "version": "v2.4.1", "source": {"type": "git", "url": "https://github.com/laravel/tinker.git", "reference": "3c9ef136ca59366bc1b50b7f2500a946d5149c62"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/laravel/tinker/zipball/3c9ef136ca59366bc1b50b7f2500a946d5149c62", "reference": "3c9ef136ca59366bc1b50b7f2500a946d5149c62", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"illuminate/console": "^6.0|^7.0|^8.0", "illuminate/contracts": "^6.0|^7.0|^8.0", "illuminate/support": "^6.0|^7.0|^8.0", "php": "^7.2", "psy/psysh": "^0.10.3", "symfony/var-dumper": "^4.3|^5.0"}, "require-dev": {"mockery/mockery": "^1.3.1", "phpunit/phpunit": "^8.4|^9.0"}, "suggest": {"illuminate/database": "The Illuminate Database package (^6.0|^7.0|^8.0)."}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.x-dev"}, "laravel": {"providers": ["Laravel\\Tinker\\TinkerServiceProvider"]}}, "autoload": {"psr-4": {"Laravel\\Tinker\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Powerful REPL for the Laravel framework.", "keywords": ["REPL", "Tinker", "laravel", "psysh"], "time": "2020-07-07T15:10:00+00:00"}, {"name": "league/commonmark", "version": "1.5.3", "source": {"type": "git", "url": "https://github.com/thephpleague/commonmark.git", "reference": "2574454b97e4103dc4e36917bd783b25624aefcd"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/thephpleague/commonmark/zipball/2574454b97e4103dc4e36917bd783b25624aefcd", "reference": "2574454b97e4103dc4e36917bd783b25624aefcd", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"ext-mbstring": "*", "php": "^7.1 || ^8.0"}, "conflict": {"scrutinizer/ocular": "1.7.*"}, "require-dev": {"cebe/markdown": "~1.0", "commonmark/commonmark.js": "0.29.1", "erusev/parsedown": "~1.0", "ext-json": "*", "github/gfm": "0.29.0", "michelf/php-markdown": "~1.4", "mikehaertl/php-shellcommand": "^1.4", "phpstan/phpstan": "^0.12", "phpunit/phpunit": "^7.5 || ^8.5 || ^9.2", "scrutinizer/ocular": "^1.5", "symfony/finder": "^4.2"}, "bin": ["bin/commonmark"], "type": "library", "autoload": {"psr-4": {"League\\CommonMark\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://www.colinodell.com", "role": "Lead Developer"}], "description": "Highly-extensible PHP Markdown parser which fully supports the CommonMark spec and Github-Flavored Markdown (GFM)", "homepage": "https://commonmark.thephpleague.com", "keywords": ["commonmark", "flavored", "gfm", "github", "github-flavored", "markdown", "md", "parser"], "time": "2020-07-19T22:47:30+00:00"}, {"name": "league/flysystem", "version": "1.0.69", "source": {"type": "git", "url": "https://github.com/thephpleague/flysystem.git", "reference": "7106f78428a344bc4f643c233a94e48795f10967"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/thephpleague/flysystem/zipball/7106f78428a344bc4f643c233a94e48795f10967", "reference": "7106f78428a344bc4f643c233a94e48795f10967", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"ext-fileinfo": "*", "php": ">=5.5.9"}, "conflict": {"league/flysystem-sftp": "<1.0.6"}, "require-dev": {"phpspec/phpspec": "^3.4", "phpunit/phpunit": "^5.7.26"}, "suggest": {"ext-fileinfo": "Required for MimeType", "ext-ftp": "Allows you to use FTP server storage", "ext-openssl": "Allows you to use FTPS server storage", "league/flysystem-aws-s3-v2": "Allows you to use S3 storage with AWS SDK v2", "league/flysystem-aws-s3-v3": "Allows you to use S3 storage with AWS SDK v3", "league/flysystem-azure": "Allows you to use Windows Azure Blob storage", "league/flysystem-cached-adapter": "Flysystem adapter decorator for metadata caching", "league/flysystem-eventable-filesystem": "Allows you to use EventableFilesystem", "league/flysystem-rackspace": "Allows you to use Rackspace Cloud Files", "league/flysystem-sftp": "Allows you to use SFTP server storage via phpseclib", "league/flysystem-webdav": "Allows you to use WebDAV storage", "league/flysystem-ziparchive": "Allows you to use ZipArchive adapter", "spatie/flysystem-dropbox": "Allows you to use Dropbox storage", "srmklive/flysystem-dropbox-v2": "Allows you to use Dropbox storage for PHP 5 applications"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.1-dev"}}, "autoload": {"psr-4": {"League\\Flysystem\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Filesystem abstraction: Many filesystems, one API.", "keywords": ["Cloud Files", "WebDAV", "abstraction", "aws", "cloud", "copy.com", "dropbox", "file systems", "files", "filesystem", "filesystems", "ftp", "rackspace", "remote", "s3", "sftp", "storage"], "time": "2020-05-18T15:13:39+00:00"}, {"name": "lnatpunblhna/in-textarea", "version": "v1.0.6", "source": {"type": "git", "url": "https://github.com/lnatpunblhna/in-textarea.git", "reference": "aefbc764e52a948299411112bb58448e60debe88"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/lnatpunblhna/in-textarea/zipball/aefbc764e52a948299411112bb58448e60debe88", "reference": "aefbc764e52a948299411112bb58448e60debe88", "shasum": ""}, "require": {"dcat/laravel-admin": "~2.0", "php": ">=7.1.0"}, "type": "library", "extra": {"laravel": {"providers": ["Lnatpunblhna\\InTextarea\\InTextareaServiceProvider"]}, "dcat-admin": "Lnatpunblhna\\InTextarea\\InTextareaServiceProvider"}, "autoload": {"psr-4": {"Lnatpunblhna\\InTextarea\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "lnatpunblhna", "email": "<EMAIL>"}], "description": "Textarea Filter Extension for Dcat Admin", "homepage": "https://github.com/lnatpunblhna/in-textarea", "keywords": ["dcat-admin", "extension"], "support": {"issues": "https://github.com/lnatpunblhna/in-textarea/issues", "source": "https://github.com/lnatpunblhna/in-textarea/tree/v1.0.6"}, "time": "2025-04-15T10:49:40+00:00"}, {"name": "maatwebsite/excel", "version": "3.1.26", "source": {"type": "git", "url": "https://github.com/Maatwebsite/Laravel-Excel.git", "reference": "66f7c9584304ad0b6a267a5d8ffbfa2ff4272e85"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Maatwebsite/Laravel-Excel/zipball/66f7c9584304ad0b6a267a5d8ffbfa2ff4272e85", "reference": "66f7c9584304ad0b6a267a5d8ffbfa2ff4272e85", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"ext-json": "*", "illuminate/support": "5.8.*|^6.0|^7.0|^8.0", "php": "^7.0|^8.0", "phpoffice/phpspreadsheet": "^1.15"}, "require-dev": {"orchestra/testbench": "^6.0", "predis/predis": "^1.1"}, "type": "library", "extra": {"laravel": {"providers": ["Maatwebsite\\Excel\\ExcelServiceProvider"], "aliases": {"Excel": "Maatwebsite\\Excel\\Facades\\Excel"}}}, "autoload": {"psr-4": {"Maatwebsite\\Excel\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Supercharged Excel exports and imports in Laravel", "keywords": ["PHPExcel", "batch", "csv", "excel", "export", "import", "laravel", "php", "phpspreadsheet"], "funding": [{"url": "https://laravel-excel.com/commercial-support", "type": "custom"}, {"url": "https://github.com/patrickbrouwers", "type": "github"}], "time": "2020-11-27T16:17:38+00:00"}, {"name": "maennchen/zipstream-php", "version": "2.1.0", "source": {"type": "git", "url": "https://github.com/maennchen/ZipStream-PHP.git", "reference": "c4c5803cc1f93df3d2448478ef79394a5981cc58"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/maennchen/ZipStream-PHP/zipball/c4c5803cc1f93df3d2448478ef79394a5981cc58", "reference": "c4c5803cc1f93df3d2448478ef79394a5981cc58", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"myclabs/php-enum": "^1.5", "php": ">= 7.1", "psr/http-message": "^1.0", "symfony/polyfill-mbstring": "^1.0"}, "require-dev": {"ext-zip": "*", "guzzlehttp/guzzle": ">= 6.3", "mikey179/vfsstream": "^1.6", "phpunit/phpunit": ">= 7.5"}, "type": "library", "autoload": {"psr-4": {"ZipStream\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Jonatan Männchen", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "ZipStream is a library for dynamically streaming dynamic zip files from PHP without writing to the disk at all on the server.", "keywords": ["stream", "zip"], "funding": [{"url": "https://opencollective.com/zipstream", "type": "open_collective"}], "time": "2020-05-30T13:11:16+00:00"}, {"name": "markbaker/complex", "version": "2.0.0", "source": {"type": "git", "url": "https://github.com/MarkBaker/PHPComplex.git", "reference": "9999f1432fae467bc93c53f357105b4c31bb994c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/MarkBaker/PHPComplex/zipball/9999f1432fae467bc93c53f357105b4c31bb994c", "reference": "9999f1432fae467bc93c53f357105b4c31bb994c", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": "^7.2 || ^8.0"}, "require-dev": {"dealerdirect/phpcodesniffer-composer-installer": "^0.7.0", "phpcompatibility/php-compatibility": "^9.0", "phpdocumentor/phpdocumentor": "2.*", "phploc/phploc": "^4.0", "phpmd/phpmd": "2.*", "phpunit/phpunit": "^7.0 || ^8.0 || ^9.3", "sebastian/phpcpd": "^4.0", "squizlabs/php_codesniffer": "^3.4"}, "type": "library", "autoload": {"psr-4": {"Complex\\": "classes/src/"}, "files": ["classes/src/functions/abs.php", "classes/src/functions/acos.php", "classes/src/functions/acosh.php", "classes/src/functions/acot.php", "classes/src/functions/acoth.php", "classes/src/functions/acsc.php", "classes/src/functions/acsch.php", "classes/src/functions/argument.php", "classes/src/functions/asec.php", "classes/src/functions/asech.php", "classes/src/functions/asin.php", "classes/src/functions/asinh.php", "classes/src/functions/atan.php", "classes/src/functions/atanh.php", "classes/src/functions/conjugate.php", "classes/src/functions/cos.php", "classes/src/functions/cosh.php", "classes/src/functions/cot.php", "classes/src/functions/coth.php", "classes/src/functions/csc.php", "classes/src/functions/csch.php", "classes/src/functions/exp.php", "classes/src/functions/inverse.php", "classes/src/functions/ln.php", "classes/src/functions/log2.php", "classes/src/functions/log10.php", "classes/src/functions/negative.php", "classes/src/functions/pow.php", "classes/src/functions/rho.php", "classes/src/functions/sec.php", "classes/src/functions/sech.php", "classes/src/functions/sin.php", "classes/src/functions/sinh.php", "classes/src/functions/sqrt.php", "classes/src/functions/tan.php", "classes/src/functions/tanh.php", "classes/src/functions/theta.php", "classes/src/operations/add.php", "classes/src/operations/subtract.php", "classes/src/operations/multiply.php", "classes/src/operations/divideby.php", "classes/src/operations/divideinto.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "PHP Class for working with complex numbers", "homepage": "https://github.com/MarkBaker/PHPComplex", "keywords": ["complex", "mathematics"], "time": "2020-08-26T10:42:07+00:00"}, {"name": "markbaker/matrix", "version": "2.0.0", "source": {"type": "git", "url": "https://github.com/MarkBaker/PHPMatrix.git", "reference": "9567d9c4c519fbe40de01dbd1e4469dbbb66f46a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/MarkBaker/PHPMatrix/zipball/9567d9c4c519fbe40de01dbd1e4469dbbb66f46a", "reference": "9567d9c4c519fbe40de01dbd1e4469dbbb66f46a", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": "^7.2 || ^8.0"}, "require-dev": {"dealerdirect/phpcodesniffer-composer-installer": "^0.7.0", "phpcompatibility/php-compatibility": "^9.0", "phpdocumentor/phpdocumentor": "2.*", "phploc/phploc": "^4.0", "phpmd/phpmd": "2.*", "phpunit/phpunit": "^7.0 || ^8.0 || ^9.3", "sebastian/phpcpd": "^4.0", "squizlabs/php_codesniffer": "^3.4"}, "type": "library", "autoload": {"psr-4": {"Matrix\\": "classes/src/"}, "files": ["classes/src/functions/adjoint.php", "classes/src/functions/antidiagonal.php", "classes/src/functions/cofactors.php", "classes/src/functions/determinant.php", "classes/src/functions/diagonal.php", "classes/src/functions/identity.php", "classes/src/functions/inverse.php", "classes/src/functions/minors.php", "classes/src/functions/trace.php", "classes/src/functions/transpose.php", "classes/src/operations/add.php", "classes/src/operations/directsum.php", "classes/src/operations/subtract.php", "classes/src/operations/multiply.php", "classes/src/operations/divideby.php", "classes/src/operations/divideinto.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "PHP Class for working with matrices", "homepage": "https://github.com/MarkBaker/PHPMatrix", "keywords": ["mathematics", "matrix", "vector"], "time": "2020-08-28T17:11:00+00:00"}, {"name": "mews/purifier", "version": "3.3.2", "source": {"type": "git", "url": "https://github.com/mewebstudio/Purifier.git", "reference": "d9c3bd86cb911138535a6598af8323879d11719a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/mewebstudio/Purifier/zipball/d9c3bd86cb911138535a6598af8323879d11719a", "reference": "d9c3bd86cb911138535a6598af8323879d11719a", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"ezyang/htmlpurifier": "4.13.*", "illuminate/config": "^5.8|^6.0|^7.0|^8.0", "illuminate/filesystem": "^5.8|^6.0|^7.0|^8.0", "illuminate/support": "^5.8|^6.0|^7.0|^8.0", "php": "^7.2"}, "require-dev": {"graham-campbell/testbench": "^3.2|^5.5.1", "mockery/mockery": "^1.0", "phpunit/phpunit": "^8.0|^9.0"}, "suggest": {"laravel/framework": "To test the Laravel bindings", "laravel/lumen-framework": "To test the Lumen bindings"}, "type": "package", "extra": {"laravel": {"providers": ["Mews\\Purifier\\PurifierServiceProvider"], "aliases": {"Purifier": "Mews\\Purifier\\Facades\\Purifier"}}}, "autoload": {"psr-4": {"Mews\\Purifier\\": "src/"}, "files": ["src/helpers.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Muharrem ERİN", "email": "<EMAIL>", "homepage": "https://github.com/mewebstudio", "role": "Developer"}], "description": "Laravel 5/6/7 HtmlPurifier Package", "homepage": "https://github.com/mewebstudio/purifier", "keywords": ["Purifier", "htmlpurifier", "laravel5 HtmlPurifier", "laravel5 Purifier", "laravel5 Security", "laravel6 HtmlPurifier", "laravel6 Purifier", "laravel6 Security", "security", "xss"], "time": "2020-10-27T23:16:49+00:00"}, {"name": "mnapoli/silly", "version": "1.7.3", "source": {"type": "git", "url": "https://github.com/mnapoli/silly.git", "reference": "e437baa502c3e1691d342374e71446d4bac1cad5"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/mnapoli/silly/zipball/e437baa502c3e1691d342374e71446d4bac1cad5", "reference": "e437baa502c3e1691d342374e71446d4bac1cad5", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=7.0", "php-di/invoker": "~2.0", "psr/container": "^1.0|^2.0", "symfony/console": "~3.0|~4.0|~5.0"}, "require-dev": {"friendsofphp/php-cs-fixer": "^2.12", "mnapoli/phpunit-easymock": "~1.0", "phpunit/phpunit": "~6.4"}, "type": "library", "autoload": {"psr-4": {"Silly\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "Silly CLI micro-framework based on Symfony Console", "keywords": ["PSR-11", "cli", "console", "framework", "micro-framework", "silly"], "support": {"issues": "https://github.com/mnapoli/silly/issues", "source": "https://github.com/mnapoli/silly/tree/1.7.3"}, "funding": [{"url": "https://github.com/mnapoli", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/mnapoli/silly", "type": "tidelift"}], "time": "2021-12-13T09:21:21+00:00"}, {"name": "monolog/monolog", "version": "2.1.0", "source": {"type": "git", "url": "https://github.com/Seldaek/monolog.git", "reference": "38914429aac460e8e4616c8cb486ecb40ec90bb1"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Seldaek/monolog/zipball/38914429aac460e8e4616c8cb486ecb40ec90bb1", "reference": "38914429aac460e8e4616c8cb486ecb40ec90bb1", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=7.2", "psr/log": "^1.0.1"}, "provide": {"psr/log-implementation": "1.0.0"}, "require-dev": {"aws/aws-sdk-php": "^2.4.9 || ^3.0", "doctrine/couchdb": "~1.0@dev", "elasticsearch/elasticsearch": "^6.0", "graylog2/gelf-php": "^1.4.2", "php-amqplib/php-amqplib": "~2.4", "php-console/php-console": "^3.1.3", "php-parallel-lint/php-parallel-lint": "^1.0", "phpspec/prophecy": "^1.6.1", "phpunit/phpunit": "^8.5", "predis/predis": "^1.1", "rollbar/rollbar": "^1.3", "ruflin/elastica": ">=0.90 <3.0", "swiftmailer/swiftmailer": "^5.3|^6.0"}, "suggest": {"aws/aws-sdk-php": "Allow sending log messages to AWS services like DynamoDB", "doctrine/couchdb": "Allow sending log messages to a CouchDB server", "elasticsearch/elasticsearch": "Allow sending log messages to an Elasticsearch server via official client", "ext-amqp": "Allow sending log messages to an AMQP server (1.0+ required)", "ext-mbstring": "Allow to work properly with unicode symbols", "ext-mongodb": "Allow sending log messages to a MongoDB server (via driver)", "graylog2/gelf-php": "Allow sending log messages to a GrayLog2 server", "mongodb/mongodb": "Allow sending log messages to a MongoDB server (via library)", "php-amqplib/php-amqplib": "Allow sending log messages to an AMQP server using php-amqplib", "php-console/php-console": "Allow sending log messages to Google Chrome", "rollbar/rollbar": "Allow sending log messages to Rollbar", "ruflin/elastica": "Allow sending log messages to an Elastic Search server"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.x-dev"}}, "autoload": {"psr-4": {"Monolog\\": "src/Monolog"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "j.bog<PERSON><PERSON>@seld.be", "homepage": "http://seld.be"}], "description": "Sends your logs to files, sockets, inboxes, databases and various web services", "homepage": "http://github.com/Seldaek/monolog", "keywords": ["log", "logging", "psr-3"], "time": "2020-05-22T08:12:19+00:00"}, {"name": "mosiboom/dcat-iframe-tab", "version": "1.2.1", "source": {"type": "git", "url": "https://github.com/mosiboom/dcat-iframe-tab.git", "reference": "805adbc8af578dbde103b5cf81f04348113980ce"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/mosiboom/dcat-iframe-tab/zipball/805adbc8af578dbde103b5cf81f04348113980ce", "reference": "805adbc8af578dbde103b5cf81f04348113980ce", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=7.2.5"}, "type": "laravel-extension", "extra": {"laravel": {"providers": ["Mosiboom\\DcatIframeTab\\IframeTabProvider"], "aliases": {"IframeTab": "Mosiboom\\DcatIframeTab\\IframeTab"}, "dont-discover": []}}, "autoload": {"files": ["src/helpers.php"], "psr-4": {"Mosiboom\\DcatIframeTab\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "iframe-tab framework for dcat-admin", "support": {"issues": "https://github.com/mosiboom/dcat-iframe-tab/issues", "source": "https://github.com/mosiboom/dcat-iframe-tab/tree/1.2.1"}, "time": "2021-09-17T10:08:28+00:00"}, {"name": "myclabs/php-enum", "version": "1.7.7", "source": {"type": "git", "url": "https://github.com/myclabs/php-enum.git", "reference": "d178027d1e679832db9f38248fcc7200647dc2b7"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/myclabs/php-enum/zipball/d178027d1e679832db9f38248fcc7200647dc2b7", "reference": "d178027d1e679832db9f38248fcc7200647dc2b7", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"ext-json": "*", "php": ">=7.1"}, "require-dev": {"phpunit/phpunit": "^7", "squizlabs/php_codesniffer": "1.*", "vimeo/psalm": "^3.8"}, "type": "library", "autoload": {"psr-4": {"MyCLabs\\Enum\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP Enum contributors", "homepage": "https://github.com/myclabs/php-enum/graphs/contributors"}], "description": "PHP Enum implementation", "homepage": "http://github.com/myclabs/php-enum", "keywords": ["enum"], "funding": [{"url": "https://github.com/mnapoli", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/myclabs/php-enum", "type": "tidelift"}], "time": "2020-11-14T18:14:52+00:00"}, {"name": "nesbot/carbon", "version": "2.36.1", "source": {"type": "git", "url": "https://github.com/briannesbitt/Carbon.git", "reference": "ee7378a36cc62952100e718bcc58be4c7210e55f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/briannesbitt/Carbon/zipball/ee7378a36cc62952100e718bcc58be4c7210e55f", "reference": "ee7378a36cc62952100e718bcc58be4c7210e55f", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"ext-json": "*", "php": "^7.1.8 || ^8.0", "symfony/polyfill-mbstring": "^1.0", "symfony/translation": "^3.4 || ^4.0 || ^5.0"}, "require-dev": {"doctrine/orm": "^2.7", "friendsofphp/php-cs-fixer": "^2.14 || ^3.0", "kylekatarnls/multi-tester": "^2.0", "phpmd/phpmd": "^2.8", "phpstan/extension-installer": "^1.0", "phpstan/phpstan": "^0.12.30", "phpunit/phpunit": "^7.5 || ^8.0", "squizlabs/php_codesniffer": "^3.4"}, "bin": ["bin/carbon"], "type": "library", "extra": {"branch-alias": {"dev-master": "2.x-dev", "dev-3.x": "3.x-dev"}, "laravel": {"providers": ["Carbon\\Laravel\\ServiceProvider"]}, "phpstan": {"includes": ["extension.neon"]}}, "autoload": {"psr-4": {"Carbon\\": "src/Carbon/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://nesbot.com"}, {"name": "kylekatarnls", "homepage": "http://github.com/kylekatarnls"}], "description": "An API extension for DateTime that supports 281 different languages.", "homepage": "http://carbon.nesbot.com", "keywords": ["date", "datetime", "time"], "time": "2020-07-04T12:29:56+00:00"}, {"name": "nick322/secure-spreadsheet", "version": "1.0.11", "source": {"type": "git", "url": "https://github.com/nick322/secure-spreadsheet.git", "reference": "80db69b2d8a2ef0982bad1306360bdeab926e143"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nick322/secure-spreadsheet/zipball/80db69b2d8a2ef0982bad1306360bdeab926e143", "reference": "80db69b2d8a2ef0982bad1306360bdeab926e143", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"ext-hash": "*", "ext-openssl": "*", "ext-simplexml": "*", "mnapoli/silly": "^1.0", "pear/ole": "v1.0.0RC9@dev", "php": "^7.3 || ^7.4 || ^8.0 || ^8.1 || ^8.2"}, "require-dev": {"ext-hash": "*", "ext-openssl": "*", "php": "^7.3 || ^7.4 || ^8.0 || ^8.1 || ^8.2", "phpunit/phpunit": "^9.5 || ^10.0"}, "bin": ["secure-spreadsheet"], "type": "library", "autoload": {"psr-0": {"OLE": "vendor/pear/ole/OLE"}, "psr-4": {"Nick\\SecureSpreadsheet\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Encrypt and password protect sensitive XLSX files", "support": {"issues": "https://github.com/nick322/secure-spreadsheet/issues", "source": "https://github.com/nick322/secure-spreadsheet/tree/1.0.11"}, "time": "2023-11-01T08:15:03+00:00"}, {"name": "nikic/php-parser", "version": "v4.6.0", "source": {"type": "git", "url": "https://github.com/nikic/PHP-Parser.git", "reference": "c346bbfafe2ff60680258b631afb730d186ed864"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nikic/PHP-Parser/zipball/c346bbfafe2ff60680258b631afb730d186ed864", "reference": "c346bbfafe2ff60680258b631afb730d186ed864", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"ext-tokenizer": "*", "php": ">=7.0"}, "require-dev": {"ircmaxell/php-yacc": "0.0.5", "phpunit/phpunit": "^6.5 || ^7.0 || ^8.0"}, "bin": ["bin/php-parse"], "type": "library", "extra": {"branch-alias": {"dev-master": "4.3-dev"}}, "autoload": {"psr-4": {"PhpParser\\": "lib/Php<PERSON><PERSON>er"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>"}], "description": "A PHP parser written in PHP", "keywords": ["parser", "php"], "time": "2020-07-02T17:12:47+00:00"}, {"name": "opis/closure", "version": "3.5.5", "source": {"type": "git", "url": "https://github.com/opis/closure.git", "reference": "dec9fc5ecfca93f45cd6121f8e6f14457dff372c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/opis/closure/zipball/dec9fc5ecfca93f45cd6121f8e6f14457dff372c", "reference": "dec9fc5ecfca93f45cd6121f8e6f14457dff372c", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": "^5.4 || ^7.0"}, "require-dev": {"jeremeamia/superclosure": "^2.0", "phpunit/phpunit": "^4.0 || ^5.0 || ^6.0 || ^7.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.5.x-dev"}}, "autoload": {"psr-4": {"Opis\\Closure\\": "src/"}, "files": ["functions.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Sorin Sarca", "email": "<EMAIL>"}], "description": "A library that can be used to serialize closures (anonymous functions) and arbitrary objects.", "homepage": "https://opis.io/closure", "keywords": ["anonymous functions", "closure", "function", "serializable", "serialization", "serialize"], "time": "2020-06-17T14:59:55+00:00"}, {"name": "orangehill/iseed", "version": "v2.6.3", "source": {"type": "git", "url": "https://github.com/orangehill/iseed.git", "reference": "454d4aa0eacedf02d7bcbb2633e363170d3db49b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/orangehill/iseed/zipball/454d4aa0eacedf02d7bcbb2633e363170d3db49b", "reference": "454d4aa0eacedf02d7bcbb2633e363170d3db49b", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"illuminate/support": "~5.5.0|~5.6.0|~5.7.0|~5.8.0|^6.0|^7.0", "php": "^7.2"}, "require-dev": {"illuminate/filesystem": "~5.5.0|~5.6.0|~5.7.0|~5.8.0|^6.0|^7.0", "laravel/framework": "~5.5.0|~5.6.0|~5.7.0|~5.8.0|^6.0|^7.0", "mockery/mockery": "^1.0.0", "phpunit/phpunit": "^8.0"}, "type": "library", "extra": {"laravel": {"providers": ["Orangehill\\Iseed\\IseedServiceProvider"]}}, "autoload": {"psr-0": {"Orangehill\\Iseed": "src/"}, "classmap": ["src/Orangehill/Iseed/Exceptions.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-2-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Generate a new Laravel database seed file based on data from the existing database table.", "keywords": ["artisan", "generators", "laravel", "seed"], "time": "2020-03-06T08:28:39+00:00"}, {"name": "overtrue/laravel-lang", "version": "4.0.0", "source": {"type": "git", "url": "https://github.com/overtrue/laravel-lang.git", "reference": "dd071356d066e05f6151bae2dd8c9fe0bb506061"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/overtrue/laravel-lang/zipball/dd071356d066e05f6151bae2dd8c9fe0bb506061", "reference": "dd071356d066e05f6151bae2dd8c9fe0bb506061", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"caouecs/laravel-lang": "~6.0", "symfony/process": "^5.0.0"}, "require-dev": {"laravel/framework": "~7.0"}, "type": "library", "extra": {"laravel": {"providers": ["Overtrue\\LaravelLang\\TranslationServiceProvider"]}}, "autoload": {"psr-4": {"Overtrue\\LaravelLang\\": "src/"}, "files": ["src/helpers.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "overtrue", "email": "<EMAIL>"}], "description": "List of 52 languages for Lara<PERSON> 5", "keywords": ["i18n", "languages", "laravel", "locale", "overtrue"], "time": "2020-03-17T23:56:43+00:00"}, {"name": "overtrue/laravel-wechat", "version": "5.1.0", "source": {"type": "git", "url": "https://github.com/overtrue/laravel-wechat.git", "reference": "1bc59aa52cf6bae2f4f388e9f20f7893305f2fe8"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/overtrue/laravel-wechat/zipball/1bc59aa52cf6bae2f4f388e9f20f7893305f2fe8", "reference": "1bc59aa52cf6bae2f4f388e9f20f7893305f2fe8", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"illuminate/container": "^5.1 || ^6.0 || ^7.0 || ^8.0", "overtrue/wechat": "^4.0"}, "require-dev": {"friendsofphp/php-cs-fixer": "^2.16", "laravel/framework": "^8.5"}, "type": "library", "extra": {"laravel": {"providers": ["Overtrue\\LaravelWeChat\\ServiceProvider"], "aliases": {"EasyWeChat": "Overtrue\\LaravelWeChat\\Facade"}}}, "autoload": {"psr-4": {"Overtrue\\LaravelWeChat\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "overtrue", "email": "<EMAIL>"}], "description": "微信 SDK for Laravel", "keywords": ["laravel", "sdk", "wechat", "weixin"], "support": {"issues": "https://github.com/overtrue/laravel-wechat/issues", "source": "https://github.com/overtrue/laravel-wechat/tree/5.1.0"}, "time": "2020-09-27T08:32:30+00:00"}, {"name": "overtrue/socialite", "version": "2.0.24", "source": {"type": "git", "url": "https://github.com/overtrue/socialite.git", "reference": "ee7e7b000ec7d64f2b8aba1f6a2eec5cdf3f8bec"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/overtrue/socialite/zipball/ee7e7b000ec7d64f2b8aba1f6a2eec5cdf3f8bec", "reference": "ee7e7b000ec7d64f2b8aba1f6a2eec5cdf3f8bec", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"ext-json": "*", "guzzlehttp/guzzle": "^5.0|^6.0|^7.0", "php": ">=5.6", "symfony/http-foundation": "^2.7|^3.0|^4.0|^5.0"}, "require-dev": {"mockery/mockery": "~1.2", "phpunit/phpunit": "^6.0|^7.0|^8.0|^9.0"}, "type": "library", "autoload": {"psr-4": {"Overtrue\\Socialite\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "overtrue", "email": "<EMAIL>"}], "description": "A collection of OAuth 2 packages that extracts from laravel/socialite.", "keywords": ["login", "o<PERSON>h", "qq", "social", "wechat", "weibo"], "support": {"issues": "https://github.com/overtrue/socialite/issues", "source": "https://github.com/overtrue/socialite/tree/2.0.24"}, "funding": [{"url": "https://www.patreon.com/overtrue", "type": "patreon"}], "time": "2021-05-13T16:04:48+00:00"}, {"name": "overtrue/wechat", "version": "4.5.0", "source": {"type": "git", "url": "https://github.com/w7corp/easywechat.git", "reference": "04a940f97d6812a67bb8d5f2dbaebf9ad78ae776"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/w7corp/easywechat/zipball/04a940f97d6812a67bb8d5f2dbaebf9ad78ae776", "reference": "04a940f97d6812a67bb8d5f2dbaebf9ad78ae776", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"easywechat-composer/easywechat-composer": "^1.1", "ext-fileinfo": "*", "ext-openssl": "*", "ext-simplexml": "*", "guzzlehttp/guzzle": "^6.2 || ^7.0", "monolog/monolog": "^1.22 || ^2.0", "overtrue/socialite": "~2.0", "php": ">=7.2", "pimple/pimple": "^3.0", "psr/simple-cache": "^1.0", "symfony/cache": "^3.3 || ^4.3 || ^5.0", "symfony/event-dispatcher": "^4.3 || ^5.0", "symfony/http-foundation": "^2.7 || ^3.0 || ^4.0 || ^5.0", "symfony/psr-http-message-bridge": "^0.3 || ^1.0 || ^2.0"}, "require-dev": {"friendsofphp/php-cs-fixer": "^2.15", "mikey179/vfsstream": "^1.6", "mockery/mockery": "^1.2.3", "phpstan/phpstan": "^0.12.0", "phpunit/phpunit": "^7.5"}, "type": "library", "autoload": {"files": ["src/Kernel/Support/Helpers.php", "src/Kernel/Helpers.php"], "psr-4": {"EasyWeChat\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "overtrue", "email": "<EMAIL>"}], "description": "微信SDK", "keywords": ["easywechat", "sdk", "wechat", "weixin", "weixin-sdk"], "support": {"issues": "https://github.com/w7corp/easywechat/issues", "source": "https://github.com/w7corp/easywechat/tree/4.5.0"}, "funding": [{"url": "https://github.com/overtrue", "type": "github"}], "time": "2021-12-27T13:56:47+00:00"}, {"name": "paragonie/constant_time_encoding", "version": "v2.6.3", "source": {"type": "git", "url": "https://github.com/paragonie/constant_time_encoding.git", "reference": "58c3f47f650c94ec05a151692652a868995d2938"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/paragonie/constant_time_encoding/zipball/58c3f47f650c94ec05a151692652a868995d2938", "reference": "58c3f47f650c94ec05a151692652a868995d2938", "shasum": ""}, "require": {"php": "^7|^8"}, "require-dev": {"phpunit/phpunit": "^6|^7|^8|^9", "vimeo/psalm": "^1|^2|^3|^4"}, "type": "library", "autoload": {"psr-4": {"ParagonIE\\ConstantTime\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Paragon Initiative Enterprises", "email": "<EMAIL>", "homepage": "https://paragonie.com", "role": "Maintainer"}, {"name": "<PERSON> 'Sc00bz' <PERSON>", "email": "<EMAIL>", "homepage": "https://www.tobtu.com", "role": "Original Developer"}], "description": "Constant-time Implementations of RFC 4648 Encoding (Base-64, Base-32, Base-16)", "keywords": ["base16", "base32", "base32_decode", "base32_encode", "base64", "base64_decode", "base64_encode", "bin2hex", "encoding", "hex", "hex2bin", "rfc4648"], "support": {"email": "<EMAIL>", "issues": "https://github.com/paragonie/constant_time_encoding/issues", "source": "https://github.com/paragonie/constant_time_encoding"}, "time": "2022-06-14T06:56:20+00:00"}, {"name": "paragonie/random_compat", "version": "v9.99.99", "source": {"type": "git", "url": "https://github.com/paragonie/random_compat.git", "reference": "84b4dfb120c6f9b4ff7b3685f9b8f1aa365a0c95"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/paragonie/random_compat/zipball/84b4dfb120c6f9b4ff7b3685f9b8f1aa365a0c95", "reference": "84b4dfb120c6f9b4ff7b3685f9b8f1aa365a0c95", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": "^7"}, "require-dev": {"phpunit/phpunit": "4.*|5.*", "vimeo/psalm": "^1"}, "suggest": {"ext-libsodium": "Provides a modern crypto API that can be used to generate random bytes."}, "type": "library", "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Paragon Initiative Enterprises", "email": "<EMAIL>", "homepage": "https://paragonie.com"}], "description": "PHP 5.x polyfill for random_bytes() and random_int() from PHP 7", "keywords": ["csprng", "polyfill", "pseudorandom", "random"], "time": "2018-07-02T15:55:56+00:00"}, {"name": "pear/ole", "version": "v1.0.0RC9", "source": {"type": "git", "url": "https://github.com/pear/OLE.git", "reference": "6bb4365451e35b036fc804a53e0e2cc68be9dff0"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/pear/OLE/zipball/6bb4365451e35b036fc804a53e0e2cc68be9dff0", "reference": "6bb4365451e35b036fc804a53e0e2cc68be9dff0", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"pear/pear_exception": "^1.0", "php": ">=5.6"}, "require-dev": {"friendsofphp/php-cs-fixer": "^2", "pear/pear-core-minimal": "^1.10", "phpunit/phpunit": ">=5 <10", "sanmai/phpunit-legacy-adapter": "^6 || ^8"}, "type": "library", "autoload": {"psr-0": {"OLE": "./"}}, "notification-url": "https://packagist.org/downloads/", "include-path": ["./"], "license": ["PHP-3.01"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "Lead"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Lead"}], "description": "This package allows reading and writing of OLE (Object Linking and Embedding) compound documents. This format is used as container for Excel (.xls), Word (.doc) and other Microsoft file formats.", "support": {"issues": "http://pear.php.net/bugs/search.php?cmd=display&package_name[]=OLE", "source": "https://github.com/pear/OLE"}, "time": "2023-06-11T00:17:19+00:00"}, {"name": "pear/pear_exception", "version": "v1.0.2", "source": {"type": "git", "url": "https://github.com/pear/PEAR_Exception.git", "reference": "b14fbe2ddb0b9f94f5b24cf08783d599f776fff0"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/pear/PEAR_Exception/zipball/b14fbe2ddb0b9f94f5b24cf08783d599f776fff0", "reference": "b14fbe2ddb0b9f94f5b24cf08783d599f776fff0", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=5.2.0"}, "require-dev": {"phpunit/phpunit": "<9"}, "type": "class", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"classmap": ["PEAR/"]}, "notification-url": "https://packagist.org/downloads/", "include-path": ["."], "license": ["BSD-2-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The PEAR Exception base class.", "homepage": "https://github.com/pear/PEAR_Exception", "keywords": ["exception"], "support": {"issues": "http://pear.php.net/bugs/search.php?cmd=display&package_name[]=PEAR_Exception", "source": "https://github.com/pear/PEAR_Exception"}, "time": "2021-03-21T15:43:46+00:00"}, {"name": "php-di/invoker", "version": "2.3.4", "source": {"type": "git", "url": "https://github.com/PHP-DI/Invoker.git", "reference": "33234b32dafa8eb69202f950a1fc92055ed76a86"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/PHP-DI/Invoker/zipball/33234b32dafa8eb69202f950a1fc92055ed76a86", "reference": "33234b32dafa8eb69202f950a1fc92055ed76a86", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=7.3", "psr/container": "^1.0|^2.0"}, "require-dev": {"athletic/athletic": "~0.1.8", "mnapoli/hard-mode": "~0.3.0", "phpunit/phpunit": "^9.0"}, "type": "library", "autoload": {"psr-4": {"Invoker\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "Generic and extensible callable invoker", "homepage": "https://github.com/PHP-DI/Invoker", "keywords": ["callable", "dependency", "dependency-injection", "injection", "invoke", "invoker"], "support": {"issues": "https://github.com/PHP-DI/Invoker/issues", "source": "https://github.com/PHP-DI/Invoker/tree/2.3.4"}, "funding": [{"url": "https://github.com/mnapoli", "type": "github"}], "time": "2023-09-08T09:24:21+00:00"}, {"name": "phpoffice/phpspreadsheet", "version": "1.16.1", "source": {"type": "git", "url": "https://github.com/PHPOffice/PhpSpreadsheet.git", "reference": "76d4323b85129d0c368149c831a07a3e258b2b50"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/PHPOffice/PhpSpreadsheet/zipball/76d4323b85129d0c368149c831a07a3e258b2b50", "reference": "76d4323b85129d0c368149c831a07a3e258b2b50", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"ext-ctype": "*", "ext-dom": "*", "ext-fileinfo": "*", "ext-gd": "*", "ext-iconv": "*", "ext-libxml": "*", "ext-mbstring": "*", "ext-simplexml": "*", "ext-xml": "*", "ext-xmlreader": "*", "ext-xmlwriter": "*", "ext-zip": "*", "ext-zlib": "*", "ezyang/htmlpurifier": "^4.13", "maennchen/zipstream-php": "^2.1", "markbaker/complex": "^1.5||^2.0", "markbaker/matrix": "^1.2||^2.0", "php": "^7.2||^8.0", "psr/http-client": "^1.0", "psr/http-factory": "^1.0", "psr/simple-cache": "^1.0"}, "require-dev": {"dompdf/dompdf": "^0.8.5", "friendsofphp/php-cs-fixer": "^2.16", "jpgraph/jpgraph": "^4.0", "mpdf/mpdf": "^8.0", "phpcompatibility/php-compatibility": "^9.3", "phpunit/phpunit": "^8.5||^9.3", "squizlabs/php_codesniffer": "^3.5", "tecnickcom/tcpdf": "^6.3"}, "suggest": {"dompdf/dompdf": "Option for rendering PDF with PDF Writer (doesn't yet support PHP8)", "jpgraph/jpgraph": "Option for rendering charts, or including charts with PDF or HTML Writers", "mpdf/mpdf": "Option for rendering PDF with PDF Writer", "tecnickcom/tcpdf": "Option for rendering PDF with PDF Writer (doesn't yet support PHP8)"}, "type": "library", "autoload": {"psr-4": {"PhpOffice\\PhpSpreadsheet\\": "src/PhpSpreadsheet"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "homepage": "https://blog.maartenballiauw.be"}, {"name": "<PERSON>", "homepage": "https://markbakeruk.net"}, {"name": "<PERSON><PERSON><PERSON>", "homepage": "https://rootslabs.net"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}], "description": "PHPSpreadsheet - Read, Create and Write Spreadsheet documents in PHP - Spreadsheet engine", "homepage": "https://github.com/PHPOffice/PhpSpreadsheet", "keywords": ["OpenXML", "excel", "gnumeric", "ods", "php", "spreadsheet", "xls", "xlsx"], "time": "2020-12-31T18:03:49+00:00"}, {"name": "phpoption/phpoption", "version": "1.7.4", "source": {"type": "git", "url": "https://github.com/schmittjoh/php-option.git", "reference": "b2ada2ad5d8a32b89088b8adc31ecd2e3a13baf3"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/schmittjoh/php-option/zipball/b2ada2ad5d8a32b89088b8adc31ecd2e3a13baf3", "reference": "b2ada2ad5d8a32b89088b8adc31ecd2e3a13baf3", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": "^5.5.9 || ^7.0 || ^8.0"}, "require-dev": {"bamarni/composer-bin-plugin": "^1.3", "phpunit/phpunit": "^4.8.35 || ^5.0 || ^6.0 || ^7.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.7-dev"}}, "autoload": {"psr-4": {"PhpOption\\": "src/PhpOption/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Option Type for PHP", "keywords": ["language", "option", "php", "type"], "time": "2020-06-07T10:40:07+00:00"}, {"name": "pimple/pimple", "version": "v3.3.1", "source": {"type": "git", "url": "https://github.com/silexphp/Pimple.git", "reference": "21e45061c3429b1e06233475cc0e1f6fc774d5b0"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/silexphp/Pimple/zipball/21e45061c3429b1e06233475cc0e1f6fc774d5b0", "reference": "21e45061c3429b1e06233475cc0e1f6fc774d5b0", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=7.2.5", "psr/container": "^1.0"}, "require-dev": {"symfony/phpunit-bridge": "^5.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.3.x-dev"}}, "autoload": {"psr-0": {"Pimple": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "<PERSON><PERSON>, a simple Dependency Injection Container", "homepage": "https://pimple.symfony.com", "keywords": ["container", "dependency injection"], "support": {"source": "https://github.com/silexphp/Pimple/tree/v3.3.1"}, "time": "2020-11-24T20:35:42+00:00"}, {"name": "pragmarx/google2fa", "version": "v8.0.1", "source": {"type": "git", "url": "https://github.com/antonioribeiro/google2fa.git", "reference": "80c3d801b31fe165f8fe99ea085e0a37834e1be3"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/antonioribeiro/google2fa/zipball/80c3d801b31fe165f8fe99ea085e0a37834e1be3", "reference": "80c3d801b31fe165f8fe99ea085e0a37834e1be3", "shasum": ""}, "require": {"paragonie/constant_time_encoding": "^1.0|^2.0", "php": "^7.1|^8.0"}, "require-dev": {"phpstan/phpstan": "^0.12.18", "phpunit/phpunit": "^7.5.15|^8.5|^9.0"}, "type": "library", "autoload": {"psr-4": {"PragmaRX\\Google2FA\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "Creator & Designer"}], "description": "A One Time Password Authentication package, compatible with Google Authenticator.", "keywords": ["2fa", "Authentication", "Two Factor Authentication", "google2fa"], "support": {"issues": "https://github.com/antonioribeiro/google2fa/issues", "source": "https://github.com/antonioribeiro/google2fa/tree/v8.0.1"}, "time": "2022-06-13T21:57:56+00:00"}, {"name": "pragmarx/google2fa-laravel", "version": "v2.1.1", "source": {"type": "git", "url": "https://github.com/antonioribeiro/google2fa-laravel.git", "reference": "035b799d6ea080d07722012c926c15c9dde66fd7"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/antonioribeiro/google2fa-laravel/zipball/035b799d6ea080d07722012c926c15c9dde66fd7", "reference": "035b799d6ea080d07722012c926c15c9dde66fd7", "shasum": ""}, "require": {"laravel/framework": "^5.4.36|^6.0|^7.0|^8.0|^9.0|^10.0", "php": ">=7.0", "pragmarx/google2fa-qrcode": "^1.0|^2.0|^3.0"}, "require-dev": {"bacon/bacon-qr-code": "^2.0", "orchestra/testbench": "3.4.*|3.5.*|3.6.*|3.7.*|4.*|5.*|6.*|7.*|8.*", "phpunit/phpunit": "~5|~6|~7|~8|~9"}, "suggest": {"bacon/bacon-qr-code": "Required to generate inline QR Codes.", "pragmarx/recovery": "Generate recovery codes."}, "type": "library", "extra": {"component": "package", "frameworks": ["<PERSON><PERSON>"], "branch-alias": {"dev-master": "0.2-dev"}, "laravel": {"providers": ["PragmaRX\\Google2FALaravel\\ServiceProvider"], "aliases": {"Google2FA": "PragmaRX\\Google2FALaravel\\Facade"}}}, "autoload": {"psr-4": {"PragmaRX\\Google2FALaravel\\": "src/", "PragmaRX\\Google2FALaravel\\Tests\\": "tests/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "Creator & Designer"}], "description": "A One Time Password Authentication package, compatible with Google Authenticator.", "keywords": ["Authentication", "Two Factor Authentication", "google2fa", "laravel"], "support": {"issues": "https://github.com/antonioribeiro/google2fa-laravel/issues", "source": "https://github.com/antonioribeiro/google2fa-laravel/tree/v2.1.1"}, "time": "2023-02-26T09:41:06+00:00"}, {"name": "pragmarx/google2fa-qrcode", "version": "v3.0.0", "source": {"type": "git", "url": "https://github.com/antonioribeiro/google2fa-qrcode.git", "reference": "ce4d8a729b6c93741c607cfb2217acfffb5bf76b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/antonioribeiro/google2fa-qrcode/zipball/ce4d8a729b6c93741c607cfb2217acfffb5bf76b", "reference": "ce4d8a729b6c93741c607cfb2217acfffb5bf76b", "shasum": ""}, "require": {"php": ">=7.1", "pragmarx/google2fa": ">=4.0"}, "require-dev": {"bacon/bacon-qr-code": "^2.0", "chillerlan/php-qrcode": "^1.0|^2.0|^3.0|^4.0", "khanamiryan/qrcode-detector-decoder": "^1.0", "phpunit/phpunit": "~4|~5|~6|~7|~8|~9"}, "suggest": {"bacon/bacon-qr-code": "For QR Code generation, requires imagick", "chillerlan/php-qrcode": "For QR Code generation"}, "type": "library", "extra": {"component": "package", "branch-alias": {"dev-master": "1.0-dev"}}, "autoload": {"psr-4": {"PragmaRX\\Google2FAQRCode\\": "src/", "PragmaRX\\Google2FAQRCode\\Tests\\": "tests/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "Creator & Designer"}], "description": "QR Code package for Google2FA", "keywords": ["2fa", "Authentication", "Two Factor Authentication", "google2fa", "qr code", "qrcode"], "support": {"issues": "https://github.com/antonioribeiro/google2fa-qrcode/issues", "source": "https://github.com/antonioribeiro/google2fa-qrcode/tree/v3.0.0"}, "time": "2021-08-15T12:53:48+00:00"}, {"name": "predis/predis", "version": "v1.1.6", "source": {"type": "git", "url": "https://github.com/predis/predis.git", "reference": "9930e933c67446962997b05201c69c2319bf26de"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/predis/predis/zipball/9930e933c67446962997b05201c69c2319bf26de", "reference": "9930e933c67446962997b05201c69c2319bf26de", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=5.3.9"}, "require-dev": {"cweagans/composer-patches": "^1.6", "phpunit/phpunit": "~4.8"}, "suggest": {"ext-curl": "Allows access to Webdis when paired with phpiredis", "ext-phpiredis": "Allows faster serialization and deserialization of the Redis protocol"}, "type": "library", "extra": {"composer-exit-on-patch-failure": true, "patches": {"phpunit/phpunit-mock-objects": {"Fix PHP 7 and 8 compatibility": "./tests/phpunit_mock_objects.patch"}, "phpunit/phpunit": {"Fix PHP 7 compatibility": "./tests/phpunit_php7.patch", "Fix PHP 8 compatibility": "./tests/phpunit_php8.patch"}}}, "autoload": {"psr-4": {"Predis\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://clorophilla.net", "role": "Creator & Maintainer"}, {"name": "<PERSON>", "homepage": "https://till.im", "role": "Maintainer"}], "description": "Flexible and feature-complete Redis client for PHP and HHVM", "homepage": "http://github.com/predis/predis", "keywords": ["nosql", "predis", "redis"], "time": "2020-09-11T19:18:05+00:00"}, {"name": "psr/cache", "version": "1.0.1", "source": {"type": "git", "url": "https://github.com/php-fig/cache.git", "reference": "d11b50ad223250cf17b86e38383413f5a6764bf8"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/cache/zipball/d11b50ad223250cf17b86e38383413f5a6764bf8", "reference": "d11b50ad223250cf17b86e38383413f5a6764bf8", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=5.3.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Cache\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Common interface for caching libraries", "keywords": ["cache", "psr", "psr-6"], "support": {"source": "https://github.com/php-fig/cache/tree/master"}, "time": "2016-08-06T20:24:11+00:00"}, {"name": "psr/container", "version": "1.0.0", "source": {"type": "git", "url": "https://github.com/php-fig/container.git", "reference": "b7ce3b176482dbbc1245ebf52b181af44c2cf55f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/container/zipball/b7ce3b176482dbbc1245ebf52b181af44c2cf55f", "reference": "b7ce3b176482dbbc1245ebf52b181af44c2cf55f", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=5.3.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Container\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Common Container Interface (PHP FIG PSR-11)", "homepage": "https://github.com/php-fig/container", "keywords": ["PSR-11", "container", "container-interface", "container-interop", "psr"], "time": "2017-02-14T16:28:37+00:00"}, {"name": "psr/event-dispatcher", "version": "1.0.0", "source": {"type": "git", "url": "https://github.com/php-fig/event-dispatcher.git", "reference": "dbefd12671e8a14ec7f180cab83036ed26714bb0"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/event-dispatcher/zipball/dbefd12671e8a14ec7f180cab83036ed26714bb0", "reference": "dbefd12671e8a14ec7f180cab83036ed26714bb0", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=7.2.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\EventDispatcher\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Standard interfaces for event handling.", "keywords": ["events", "psr", "psr-14"], "time": "2019-01-08T18:20:26+00:00"}, {"name": "psr/http-client", "version": "1.0.1", "source": {"type": "git", "url": "https://github.com/php-fig/http-client.git", "reference": "2dfb5f6c5eff0e91e20e913f8c5452ed95b86621"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/http-client/zipball/2dfb5f6c5eff0e91e20e913f8c5452ed95b86621", "reference": "2dfb5f6c5eff0e91e20e913f8c5452ed95b86621", "shasum": ""}, "require": {"php": "^7.0 || ^8.0", "psr/http-message": "^1.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Http\\Client\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Common interface for HTTP clients", "homepage": "https://github.com/php-fig/http-client", "keywords": ["http", "http-client", "psr", "psr-18"], "support": {"source": "https://github.com/php-fig/http-client/tree/master"}, "time": "2020-06-29T06:28:15+00:00"}, {"name": "psr/http-factory", "version": "1.0.1", "source": {"type": "git", "url": "https://github.com/php-fig/http-factory.git", "reference": "12ac7fcd07e5b077433f5f2bee95b3a771bf61be"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/http-factory/zipball/12ac7fcd07e5b077433f5f2bee95b3a771bf61be", "reference": "12ac7fcd07e5b077433f5f2bee95b3a771bf61be", "shasum": ""}, "require": {"php": ">=7.0.0", "psr/http-message": "^1.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Http\\Message\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Common interfaces for PSR-7 HTTP message factories", "keywords": ["factory", "http", "message", "psr", "psr-17", "psr-7", "request", "response"], "support": {"source": "https://github.com/php-fig/http-factory/tree/master"}, "time": "2019-04-30T12:38:16+00:00"}, {"name": "psr/http-message", "version": "1.0.1", "source": {"type": "git", "url": "https://github.com/php-fig/http-message.git", "reference": "f6561bf28d520154e4b0ec72be95418abe6d9363"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/http-message/zipball/f6561bf28d520154e4b0ec72be95418abe6d9363", "reference": "f6561bf28d520154e4b0ec72be95418abe6d9363", "shasum": ""}, "require": {"php": ">=5.3.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Http\\Message\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Common interface for HTTP messages", "homepage": "https://github.com/php-fig/http-message", "keywords": ["http", "http-message", "psr", "psr-7", "request", "response"], "support": {"source": "https://github.com/php-fig/http-message/tree/master"}, "time": "2016-08-06T14:39:51+00:00"}, {"name": "psr/log", "version": "1.1.3", "source": {"type": "git", "url": "https://github.com/php-fig/log.git", "reference": "0f73288fd15629204f9d42b7055f72dacbe811fc"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/log/zipball/0f73288fd15629204f9d42b7055f72dacbe811fc", "reference": "0f73288fd15629204f9d42b7055f72dacbe811fc", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=5.3.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.1.x-dev"}}, "autoload": {"psr-4": {"Psr\\Log\\": "Psr/Log/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Common interface for logging libraries", "homepage": "https://github.com/php-fig/log", "keywords": ["log", "psr", "psr-3"], "time": "2020-03-23T09:12:05+00:00"}, {"name": "psr/simple-cache", "version": "1.0.1", "source": {"type": "git", "url": "https://github.com/php-fig/simple-cache.git", "reference": "408d5eafb83c57f6365a3ca330ff23aa4a5fa39b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/simple-cache/zipball/408d5eafb83c57f6365a3ca330ff23aa4a5fa39b", "reference": "408d5eafb83c57f6365a3ca330ff23aa4a5fa39b", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=5.3.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\SimpleCache\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Common interfaces for simple caching", "keywords": ["cache", "caching", "psr", "psr-16", "simple-cache"], "time": "2017-10-23T01:57:42+00:00"}, {"name": "psy/psysh", "version": "v0.10.4", "source": {"type": "git", "url": "https://github.com/bobthecow/psysh.git", "reference": "a8aec1b2981ab66882a01cce36a49b6317dc3560"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/bobthecow/psysh/zipball/a8aec1b2981ab66882a01cce36a49b6317dc3560", "reference": "a8aec1b2981ab66882a01cce36a49b6317dc3560", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"dnoegel/php-xdg-base-dir": "0.1.*", "ext-json": "*", "ext-tokenizer": "*", "nikic/php-parser": "~4.0|~3.0|~2.0|~1.3", "php": "^8.0 || ^7.0 || ^5.5.9", "symfony/console": "~5.0|~4.0|~3.0|^2.4.2|~2.3.10", "symfony/var-dumper": "~5.0|~4.0|~3.0|~2.7"}, "require-dev": {"bamarni/composer-bin-plugin": "^1.2", "hoa/console": "3.17.*"}, "suggest": {"ext-pcntl": "Enabling the PCNTL extension makes PsySH a lot happier :)", "ext-pdo-sqlite": "The doc command requires SQLite to work.", "ext-posix": "If you have PCNTL, you'll want the POSIX extension as well.", "ext-readline": "Enables support for arrow-key history navigation, and showing and manipulating command history.", "hoa/console": "A pure PHP readline implementation. You'll want this if your PHP install doesn't already support readline or libedit."}, "bin": ["bin/psysh"], "type": "library", "extra": {"branch-alias": {"dev-master": "0.10.x-dev"}}, "autoload": {"files": ["src/functions.php"], "psr-4": {"Psy\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://justinhileman.com"}], "description": "An interactive shell for modern PHP.", "homepage": "http://psysh.org", "keywords": ["REPL", "console", "interactive", "shell"], "time": "2020-05-03T19:32:03+00:00"}, {"name": "ralouphie/getallheaders", "version": "3.0.3", "source": {"type": "git", "url": "https://github.com/ralouphie/getallheaders.git", "reference": "120b605dfeb996808c31b6477290a714d356e822"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/ralouphie/getallheaders/zipball/120b605dfeb996808c31b6477290a714d356e822", "reference": "120b605dfeb996808c31b6477290a714d356e822", "shasum": ""}, "require": {"php": ">=5.6"}, "require-dev": {"php-coveralls/php-coveralls": "^2.1", "phpunit/phpunit": "^5 || ^6.5"}, "type": "library", "autoload": {"files": ["src/getallheaders.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "A polyfill for getallheaders.", "support": {"issues": "https://github.com/ralouphie/getallheaders/issues", "source": "https://github.com/ralouphie/getallheaders/tree/develop"}, "time": "2019-03-08T08:55:37+00:00"}, {"name": "ramsey/collection", "version": "1.0.1", "source": {"type": "git", "url": "https://github.com/ramsey/collection.git", "reference": "925ad8cf55ba7a3fc92e332c58fd0478ace3e1ca"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/ramsey/collection/zipball/925ad8cf55ba7a3fc92e332c58fd0478ace3e1ca", "reference": "925ad8cf55ba7a3fc92e332c58fd0478ace3e1ca", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": "^7.2"}, "require-dev": {"dealerdirect/phpcodesniffer-composer-installer": "^0.5.0", "fzaninotto/faker": "^1.5", "jakub-onderka/php-parallel-lint": "^1", "jangregor/phpstan-prophecy": "^0.6", "mockery/mockery": "^1.3", "phpstan/extension-installer": "^1", "phpstan/phpdoc-parser": "0.4.1", "phpstan/phpstan": "^0.12", "phpstan/phpstan-mockery": "^0.12", "phpstan/phpstan-phpunit": "^0.12", "phpunit/phpunit": "^8.5", "slevomat/coding-standard": "^6.0", "squizlabs/php_codesniffer": "^3.5"}, "type": "library", "autoload": {"psr-4": {"Ramsey\\Collection\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://benramsey.com"}], "description": "A PHP 7.2+ library for representing and manipulating collections.", "homepage": "https://github.com/ramsey/collection", "keywords": ["array", "collection", "hash", "map", "queue", "set"], "time": "2020-01-05T00:22:59+00:00"}, {"name": "ramsey/uuid", "version": "4.0.1", "source": {"type": "git", "url": "https://github.com/ramsey/uuid.git", "reference": "ba8fff1d3abb8bb4d35a135ed22a31c6ef3ede3d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/ramsey/uuid/zipball/ba8fff1d3abb8bb4d35a135ed22a31c6ef3ede3d", "reference": "ba8fff1d3abb8bb4d35a135ed22a31c6ef3ede3d", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"brick/math": "^0.8", "ext-json": "*", "php": "^7.2 || ^8", "ramsey/collection": "^1.0", "symfony/polyfill-ctype": "^1.8"}, "replace": {"rhumsaa/uuid": "self.version"}, "require-dev": {"codeception/aspect-mock": "^3", "dealerdirect/phpcodesniffer-composer-installer": "^0.6.2", "doctrine/annotations": "^1.8", "goaop/framework": "^2", "mockery/mockery": "^1.3", "moontoast/math": "^1.1", "paragonie/random-lib": "^2", "php-mock/php-mock-mockery": "^1.3", "php-mock/php-mock-phpunit": "^2.5", "php-parallel-lint/php-parallel-lint": "^1.1", "phpstan/extension-installer": "^1.0", "phpstan/phpdoc-parser": "0.4.3", "phpstan/phpstan": "^0.12", "phpstan/phpstan-mockery": "^0.12", "phpstan/phpstan-phpunit": "^0.12", "phpunit/phpunit": "^8.5", "psy/psysh": "^0.10.0", "slevomat/coding-standard": "^6.0", "squizlabs/php_codesniffer": "^3.5", "vimeo/psalm": "3.9.4"}, "suggest": {"ext-bcmath": "Enables faster math with arbitrary-precision integers using BCMath.", "ext-ctype": "Enables faster processing of character classification using ctype functions.", "ext-gmp": "Enables faster math with arbitrary-precision integers using GMP.", "ext-uuid": "Enables the use of PeclUuidTimeGenerator and PeclUuidRandomGenerator.", "paragonie/random-lib": "Provides RandomLib for use with the RandomLibAdapter", "ramsey/uuid-doctrine": "Allows the use of Ramsey\\Uuid\\Uuid as Doctrine field type."}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.x-dev"}}, "autoload": {"psr-4": {"Ramsey\\Uuid\\": "src/"}, "files": ["src/functions.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "A PHP library for generating and working with universally unique identifiers (UUIDs).", "homepage": "https://github.com/ramsey/uuid", "keywords": ["guid", "identifier", "uuid"], "time": "2020-03-29T20:13:32+00:00"}, {"name": "spatie/eloquent-sortable", "version": "3.11.0", "source": {"type": "git", "url": "https://github.com/spatie/eloquent-sortable.git", "reference": "b06fa886559f8d40e31c8a69fd32bd45401dc5da"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/spatie/eloquent-sortable/zipball/b06fa886559f8d40e31c8a69fd32bd45401dc5da", "reference": "b06fa886559f8d40e31c8a69fd32bd45401dc5da", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"illuminate/database": "^6.0|^7.0|^8.0", "illuminate/support": "^6.0|^7.0|^8.0", "php": "^7.3|^8.0"}, "require-dev": {"orchestra/testbench": "^4.0|^5.0|^6.0", "phpunit/phpunit": "^8.0|^9.0"}, "type": "library", "extra": {"laravel": {"providers": ["Spatie\\EloquentSortable\\EloquentSortableServiceProvider"]}}, "autoload": {"psr-4": {"Spatie\\EloquentSortable\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Sortable behaviour for eloquent models", "homepage": "https://github.com/spatie/eloquent-sortable", "keywords": ["behaviour", "eloquent", "laravel", "model", "sort", "sortable"], "support": {"issues": "https://github.com/spatie/eloquent-sortable/issues", "source": "https://github.com/spatie/eloquent-sortable/tree/3.11.0"}, "funding": [{"url": "https://github.com/spatie", "type": "github"}], "time": "2021-01-18T00:32:12+00:00"}, {"name": "spatie/laravel-schemaless-attributes", "version": "1.7.1", "source": {"type": "git", "url": "https://github.com/spatie/laravel-schemaless-attributes.git", "reference": "2ad106215d7d4403e50cac0ca9aaea0bf91506c2"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/spatie/laravel-schemaless-attributes/zipball/2ad106215d7d4403e50cac0ca9aaea0bf91506c2", "reference": "2ad106215d7d4403e50cac0ca9aaea0bf91506c2", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"illuminate/database": "^5.6|^6.0|^7.0", "illuminate/support": "^5.6|^6.0|^7.0", "php": "^7.2"}, "require-dev": {"larapack/dd": "^1.0", "orchestra/testbench": "^3.6|^4.0|^5.0", "phpunit/phpunit": "^7.0|^8.0|^9.0"}, "type": "library", "extra": {"laravel": {"providers": ["Spatie\\SchemalessAttributes\\SchemalessAttributesServiceProvider"]}}, "autoload": {"psr-4": {"Spatie\\SchemalessAttributes\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://spatie.be", "role": "Developer"}], "description": "Add schemaless attributes to Eloquent models", "homepage": "https://github.com/spatie/laravel-schemaless-attributes", "keywords": ["laravel-schemaless-attributes", "spatie"], "time": "2020-03-18T19:08:09+00:00"}, {"name": "swiftmailer/swiftmailer", "version": "v6.2.3", "source": {"type": "git", "url": "https://github.com/swiftmailer/swiftmailer.git", "reference": "149cfdf118b169f7840bbe3ef0d4bc795d1780c9"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/swiftmailer/swiftmailer/zipball/149cfdf118b169f7840bbe3ef0d4bc795d1780c9", "reference": "149cfdf118b169f7840bbe3ef0d4bc795d1780c9", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"egulias/email-validator": "~2.0", "php": ">=7.0.0", "symfony/polyfill-iconv": "^1.0", "symfony/polyfill-intl-idn": "^1.10", "symfony/polyfill-mbstring": "^1.0"}, "require-dev": {"mockery/mockery": "~0.9.1", "symfony/phpunit-bridge": "^3.4.19|^4.1.8"}, "suggest": {"ext-intl": "Needed to support internationalized email addresses", "true/punycode": "Needed to support internationalized email addresses, if ext-intl is not installed"}, "type": "library", "extra": {"branch-alias": {"dev-master": "6.2-dev"}}, "autoload": {"files": ["lib/swift_required.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Swiftmailer, free feature-rich PHP mailer", "homepage": "https://swiftmailer.symfony.com", "keywords": ["email", "mail", "mailer"], "time": "2019-11-12T09:31:26+00:00"}, {"name": "symfony/cache", "version": "v5.3.14", "source": {"type": "git", "url": "https://github.com/symfony/cache.git", "reference": "ea6de0cb6fef5e3d5c145bbd665ba35b43b4d11b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/cache/zipball/ea6de0cb6fef5e3d5c145bbd665ba35b43b4d11b", "reference": "ea6de0cb6fef5e3d5c145bbd665ba35b43b4d11b", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=7.2.5", "psr/cache": "^1.0|^2.0", "psr/log": "^1.1|^2|^3", "symfony/cache-contracts": "^1.1.7|^2", "symfony/deprecation-contracts": "^2.1", "symfony/polyfill-php73": "^1.9", "symfony/polyfill-php80": "^1.16", "symfony/service-contracts": "^1.1|^2", "symfony/var-exporter": "^4.4|^5.0"}, "conflict": {"doctrine/dbal": "<2.10", "symfony/dependency-injection": "<4.4", "symfony/http-kernel": "<4.4", "symfony/var-dumper": "<4.4"}, "provide": {"psr/cache-implementation": "1.0|2.0", "psr/simple-cache-implementation": "1.0|2.0", "symfony/cache-implementation": "1.0|2.0"}, "require-dev": {"cache/integration-tests": "dev-master", "doctrine/cache": "^1.6|^2.0", "doctrine/dbal": "^2.10|^3.0", "predis/predis": "^1.1", "psr/simple-cache": "^1.0|^2.0", "symfony/config": "^4.4|^5.0", "symfony/dependency-injection": "^4.4|^5.0", "symfony/filesystem": "^4.4|^5.0", "symfony/http-kernel": "^4.4|^5.0", "symfony/messenger": "^4.4|^5.0", "symfony/var-dumper": "^4.4|^5.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Cache\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides an extended PSR-6, PSR-16 (and tags) implementation", "homepage": "https://symfony.com", "keywords": ["caching", "psr6"], "support": {"source": "https://github.com/symfony/cache/tree/v5.3.14"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2022-01-26T16:05:39+00:00"}, {"name": "symfony/cache-contracts", "version": "v2.5.1", "source": {"type": "git", "url": "https://github.com/symfony/cache-contracts.git", "reference": "64be4a7acb83b6f2bf6de9a02cee6dad41277ebc"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/cache-contracts/zipball/64be4a7acb83b6f2bf6de9a02cee6dad41277ebc", "reference": "64be4a7acb83b6f2bf6de9a02cee6dad41277ebc", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=7.2.5", "psr/cache": "^1.0|^2.0|^3.0"}, "suggest": {"symfony/cache-implementation": ""}, "type": "library", "extra": {"branch-alias": {"dev-main": "2.5-dev"}, "thanks": {"name": "symfony/contracts", "url": "https://github.com/symfony/contracts"}}, "autoload": {"psr-4": {"Symfony\\Contracts\\Cache\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Generic abstractions related to caching", "homepage": "https://symfony.com", "keywords": ["abstractions", "contracts", "decoupling", "interfaces", "interoperability", "standards"], "support": {"source": "https://github.com/symfony/cache-contracts/tree/v2.5.1"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2022-01-02T09:53:40+00:00"}, {"name": "symfony/console", "version": "v5.1.2", "source": {"type": "git", "url": "https://github.com/symfony/console.git", "reference": "34ac555a3627e324b660e318daa07572e1140123"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/console/zipball/34ac555a3627e324b660e318daa07572e1140123", "reference": "34ac555a3627e324b660e318daa07572e1140123", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=7.2.5", "symfony/polyfill-mbstring": "~1.0", "symfony/polyfill-php73": "^1.8", "symfony/polyfill-php80": "^1.15", "symfony/service-contracts": "^1.1|^2", "symfony/string": "^5.1"}, "conflict": {"symfony/dependency-injection": "<4.4", "symfony/dotenv": "<5.1", "symfony/event-dispatcher": "<4.4", "symfony/lock": "<4.4", "symfony/process": "<4.4"}, "provide": {"psr/log-implementation": "1.0"}, "require-dev": {"psr/log": "~1.0", "symfony/config": "^4.4|^5.0", "symfony/dependency-injection": "^4.4|^5.0", "symfony/event-dispatcher": "^4.4|^5.0", "symfony/lock": "^4.4|^5.0", "symfony/process": "^4.4|^5.0", "symfony/var-dumper": "^4.4|^5.0"}, "suggest": {"psr/log": "For using the console logger", "symfony/event-dispatcher": "", "symfony/lock": "", "symfony/process": ""}, "type": "library", "extra": {"branch-alias": {"dev-master": "5.1-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\Console\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Console Component", "homepage": "https://symfony.com", "time": "2020-06-15T12:59:21+00:00"}, {"name": "symfony/css-selector", "version": "v5.1.2", "source": {"type": "git", "url": "https://github.com/symfony/css-selector.git", "reference": "e544e24472d4c97b2d11ade7caacd446727c6bf9"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/css-selector/zipball/e544e24472d4c97b2d11ade7caacd446727c6bf9", "reference": "e544e24472d4c97b2d11ade7caacd446727c6bf9", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=7.2.5"}, "type": "library", "extra": {"branch-alias": {"dev-master": "5.1-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\CssSelector\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony CssSelector Component", "homepage": "https://symfony.com", "time": "2020-05-20T17:43:50+00:00"}, {"name": "symfony/deprecation-contracts", "version": "v2.4.0", "source": {"type": "git", "url": "https://github.com/symfony/deprecation-contracts.git", "reference": "5f38c8804a9e97d23e0c8d63341088cd8a22d627"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/deprecation-contracts/zipball/5f38c8804a9e97d23e0c8d63341088cd8a22d627", "reference": "5f38c8804a9e97d23e0c8d63341088cd8a22d627", "shasum": ""}, "require": {"php": ">=7.1"}, "type": "library", "extra": {"branch-alias": {"dev-main": "2.4-dev"}, "thanks": {"name": "symfony/contracts", "url": "https://github.com/symfony/contracts"}}, "autoload": {"files": ["function.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "A generic function and convention to trigger deprecation notices", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/deprecation-contracts/tree/v2.4.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2021-03-23T23:28:01+00:00"}, {"name": "symfony/dom-crawler", "version": "v5.2.1", "source": {"type": "git", "url": "https://github.com/symfony/dom-crawler.git", "reference": "ee7cf316fb0de786cfe5ae32ee79502b290c81ea"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/dom-crawler/zipball/ee7cf316fb0de786cfe5ae32ee79502b290c81ea", "reference": "ee7cf316fb0de786cfe5ae32ee79502b290c81ea", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=7.2.5", "symfony/polyfill-ctype": "~1.8", "symfony/polyfill-mbstring": "~1.0", "symfony/polyfill-php80": "^1.15"}, "conflict": {"masterminds/html5": "<2.6"}, "require-dev": {"masterminds/html5": "^2.6", "symfony/css-selector": "^4.4|^5.0"}, "suggest": {"symfony/css-selector": ""}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\DomCrawler\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony DomCrawler Component", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/dom-crawler/tree/v5.2.1"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2020-12-18T08:02:46+00:00"}, {"name": "symfony/error-handler", "version": "v5.1.2", "source": {"type": "git", "url": "https://github.com/symfony/error-handler.git", "reference": "7d0b927b9d3dc41d7d46cda38cbfcd20cdcbb896"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/error-handler/zipball/7d0b927b9d3dc41d7d46cda38cbfcd20cdcbb896", "reference": "7d0b927b9d3dc41d7d46cda38cbfcd20cdcbb896", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=7.2.5", "psr/log": "^1.0", "symfony/polyfill-php80": "^1.15", "symfony/var-dumper": "^4.4|^5.0"}, "require-dev": {"symfony/deprecation-contracts": "^2.1", "symfony/http-kernel": "^4.4|^5.0", "symfony/serializer": "^4.4|^5.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "5.1-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\ErrorHandler\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony ErrorHandler Component", "homepage": "https://symfony.com", "time": "2020-05-30T20:35:19+00:00"}, {"name": "symfony/event-dispatcher", "version": "v5.1.2", "source": {"type": "git", "url": "https://github.com/symfony/event-dispatcher.git", "reference": "cc0d059e2e997e79ca34125a52f3e33de4424ac7"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/cc0d059e2e997e79ca34125a52f3e33de4424ac7", "reference": "cc0d059e2e997e79ca34125a52f3e33de4424ac7", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=7.2.5", "symfony/deprecation-contracts": "^2.1", "symfony/event-dispatcher-contracts": "^2", "symfony/polyfill-php80": "^1.15"}, "conflict": {"symfony/dependency-injection": "<4.4"}, "provide": {"psr/event-dispatcher-implementation": "1.0", "symfony/event-dispatcher-implementation": "2.0"}, "require-dev": {"psr/log": "~1.0", "symfony/config": "^4.4|^5.0", "symfony/dependency-injection": "^4.4|^5.0", "symfony/expression-language": "^4.4|^5.0", "symfony/http-foundation": "^4.4|^5.0", "symfony/service-contracts": "^1.1|^2", "symfony/stopwatch": "^4.4|^5.0"}, "suggest": {"symfony/dependency-injection": "", "symfony/http-kernel": ""}, "type": "library", "extra": {"branch-alias": {"dev-master": "5.1-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\EventDispatcher\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony EventDispatcher Component", "homepage": "https://symfony.com", "time": "2020-05-20T17:43:50+00:00"}, {"name": "symfony/event-dispatcher-contracts", "version": "v2.1.3", "source": {"type": "git", "url": "https://github.com/symfony/event-dispatcher-contracts.git", "reference": "f6f613d74cfc5a623fc36294d3451eb7fa5a042b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/event-dispatcher-contracts/zipball/f6f613d74cfc5a623fc36294d3451eb7fa5a042b", "reference": "f6f613d74cfc5a623fc36294d3451eb7fa5a042b", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=7.2.5", "psr/event-dispatcher": "^1"}, "suggest": {"symfony/event-dispatcher-implementation": ""}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.1-dev"}, "thanks": {"name": "symfony/contracts", "url": "https://github.com/symfony/contracts"}}, "autoload": {"psr-4": {"Symfony\\Contracts\\EventDispatcher\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Generic abstractions related to dispatching event", "homepage": "https://symfony.com", "keywords": ["abstractions", "contracts", "decoupling", "interfaces", "interoperability", "standards"], "time": "2020-07-06T13:23:11+00:00"}, {"name": "symfony/finder", "version": "v5.1.2", "source": {"type": "git", "url": "https://github.com/symfony/finder.git", "reference": "4298870062bfc667cb78d2b379be4bf5dec5f187"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/finder/zipball/4298870062bfc667cb78d2b379be4bf5dec5f187", "reference": "4298870062bfc667cb78d2b379be4bf5dec5f187", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=7.2.5"}, "type": "library", "extra": {"branch-alias": {"dev-master": "5.1-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\Finder\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Finder Component", "homepage": "https://symfony.com", "time": "2020-05-20T17:43:50+00:00"}, {"name": "symfony/http-foundation", "version": "v5.1.2", "source": {"type": "git", "url": "https://github.com/symfony/http-foundation.git", "reference": "f93055171b847915225bd5b0a5792888419d8d75"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/http-foundation/zipball/f93055171b847915225bd5b0a5792888419d8d75", "reference": "f93055171b847915225bd5b0a5792888419d8d75", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=7.2.5", "symfony/deprecation-contracts": "^2.1", "symfony/polyfill-mbstring": "~1.1", "symfony/polyfill-php80": "^1.15"}, "require-dev": {"predis/predis": "~1.0", "symfony/cache": "^4.4|^5.0", "symfony/expression-language": "^4.4|^5.0", "symfony/mime": "^4.4|^5.0"}, "suggest": {"symfony/mime": "To use the file extension guesser"}, "type": "library", "extra": {"branch-alias": {"dev-master": "5.1-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\HttpFoundation\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony HttpFoundation Component", "homepage": "https://symfony.com", "time": "2020-06-15T06:52:54+00:00"}, {"name": "symfony/http-kernel", "version": "v5.1.2", "source": {"type": "git", "url": "https://github.com/symfony/http-kernel.git", "reference": "a18c27ace1ef344ffcb129a5b089bad7643b387a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/http-kernel/zipball/a18c27ace1ef344ffcb129a5b089bad7643b387a", "reference": "a18c27ace1ef344ffcb129a5b089bad7643b387a", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=7.2.5", "psr/log": "~1.0", "symfony/deprecation-contracts": "^2.1", "symfony/error-handler": "^4.4|^5.0", "symfony/event-dispatcher": "^5.0", "symfony/http-foundation": "^4.4|^5.0", "symfony/polyfill-ctype": "^1.8", "symfony/polyfill-php73": "^1.9", "symfony/polyfill-php80": "^1.15"}, "conflict": {"symfony/browser-kit": "<4.4", "symfony/cache": "<5.0", "symfony/config": "<5.0", "symfony/console": "<4.4", "symfony/dependency-injection": "<4.4", "symfony/doctrine-bridge": "<5.0", "symfony/form": "<5.0", "symfony/http-client": "<5.0", "symfony/mailer": "<5.0", "symfony/messenger": "<5.0", "symfony/translation": "<5.0", "symfony/twig-bridge": "<5.0", "symfony/validator": "<5.0", "twig/twig": "<2.4"}, "provide": {"psr/log-implementation": "1.0"}, "require-dev": {"psr/cache": "~1.0", "symfony/browser-kit": "^4.4|^5.0", "symfony/config": "^5.0", "symfony/console": "^4.4|^5.0", "symfony/css-selector": "^4.4|^5.0", "symfony/dependency-injection": "^4.4|^5.0", "symfony/dom-crawler": "^4.4|^5.0", "symfony/expression-language": "^4.4|^5.0", "symfony/finder": "^4.4|^5.0", "symfony/process": "^4.4|^5.0", "symfony/routing": "^4.4|^5.0", "symfony/stopwatch": "^4.4|^5.0", "symfony/translation": "^4.4|^5.0", "symfony/translation-contracts": "^1.1|^2", "twig/twig": "^2.4|^3.0"}, "suggest": {"symfony/browser-kit": "", "symfony/config": "", "symfony/console": "", "symfony/dependency-injection": ""}, "type": "library", "extra": {"branch-alias": {"dev-master": "5.1-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\HttpKernel\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony HttpKernel Component", "homepage": "https://symfony.com", "time": "2020-06-15T13:51:38+00:00"}, {"name": "symfony/mime", "version": "v5.1.2", "source": {"type": "git", "url": "https://github.com/symfony/mime.git", "reference": "c0c418f05e727606e85b482a8591519c4712cf45"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/mime/zipball/c0c418f05e727606e85b482a8591519c4712cf45", "reference": "c0c418f05e727606e85b482a8591519c4712cf45", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=7.2.5", "symfony/polyfill-intl-idn": "^1.10", "symfony/polyfill-mbstring": "^1.0", "symfony/polyfill-php80": "^1.15"}, "conflict": {"symfony/mailer": "<4.4"}, "require-dev": {"egulias/email-validator": "^2.1.10", "symfony/dependency-injection": "^4.4|^5.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "5.1-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\Mime\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "A library to manipulate MIME messages", "homepage": "https://symfony.com", "keywords": ["mime", "mime-type"], "time": "2020-06-09T15:07:35+00:00"}, {"name": "symfony/polyfill-ctype", "version": "v1.18.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-ctype.git", "reference": "1c302646f6efc070cd46856e600e5e0684d6b454"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-ctype/zipball/1c302646f6efc070cd46856e600e5e0684d6b454", "reference": "1c302646f6efc070cd46856e600e5e0684d6b454", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=5.3.3"}, "suggest": {"ext-ctype": "For best performance"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.18-dev"}, "thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"psr-4": {"Symfony\\Polyfill\\Ctype\\": ""}, "files": ["bootstrap.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for ctype functions", "homepage": "https://symfony.com", "keywords": ["compatibility", "ctype", "polyfill", "portable"], "time": "2020-07-14T12:35:20+00:00"}, {"name": "symfony/polyfill-iconv", "version": "v1.18.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-iconv.git", "reference": "6c2f78eb8f5ab8eaea98f6d414a5915f2e0fce36"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-iconv/zipball/6c2f78eb8f5ab8eaea98f6d414a5915f2e0fce36", "reference": "6c2f78eb8f5ab8eaea98f6d414a5915f2e0fce36", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=5.3.3"}, "suggest": {"ext-iconv": "For best performance"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.18-dev"}, "thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"psr-4": {"Symfony\\Polyfill\\Iconv\\": ""}, "files": ["bootstrap.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for the Iconv extension", "homepage": "https://symfony.com", "keywords": ["compatibility", "iconv", "polyfill", "portable", "shim"], "time": "2020-07-14T12:35:20+00:00"}, {"name": "symfony/polyfill-intl-grapheme", "version": "v1.18.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-intl-grapheme.git", "reference": "b740103edbdcc39602239ee8860f0f45a8eb9aa5"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-intl-grapheme/zipball/b740103edbdcc39602239ee8860f0f45a8eb9aa5", "reference": "b740103edbdcc39602239ee8860f0f45a8eb9aa5", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=5.3.3"}, "suggest": {"ext-intl": "For best performance"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.18-dev"}, "thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"psr-4": {"Symfony\\Polyfill\\Intl\\Grapheme\\": ""}, "files": ["bootstrap.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for intl's grapheme_* functions", "homepage": "https://symfony.com", "keywords": ["compatibility", "grapheme", "intl", "polyfill", "portable", "shim"], "time": "2020-07-14T12:35:20+00:00"}, {"name": "symfony/polyfill-intl-idn", "version": "v1.18.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-intl-idn.git", "reference": "bc6549d068d0160e0f10f7a5a23c7d1406b95ebe"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-intl-idn/zipball/bc6549d068d0160e0f10f7a5a23c7d1406b95ebe", "reference": "bc6549d068d0160e0f10f7a5a23c7d1406b95ebe", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=5.3.3", "symfony/polyfill-intl-normalizer": "^1.10", "symfony/polyfill-php70": "^1.10", "symfony/polyfill-php72": "^1.10"}, "suggest": {"ext-intl": "For best performance"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.18-dev"}, "thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"psr-4": {"Symfony\\Polyfill\\Intl\\Idn\\": ""}, "files": ["bootstrap.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for intl's idn_to_ascii and idn_to_utf8 functions", "homepage": "https://symfony.com", "keywords": ["compatibility", "idn", "intl", "polyfill", "portable", "shim"], "time": "2020-07-14T12:35:20+00:00"}, {"name": "symfony/polyfill-intl-normalizer", "version": "v1.18.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-intl-normalizer.git", "reference": "37078a8dd4a2a1e9ab0231af7c6cb671b2ed5a7e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-intl-normalizer/zipball/37078a8dd4a2a1e9ab0231af7c6cb671b2ed5a7e", "reference": "37078a8dd4a2a1e9ab0231af7c6cb671b2ed5a7e", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=5.3.3"}, "suggest": {"ext-intl": "For best performance"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.18-dev"}, "thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"psr-4": {"Symfony\\Polyfill\\Intl\\Normalizer\\": ""}, "files": ["bootstrap.php"], "classmap": ["Resources/stubs"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for intl's Normalizer class and related functions", "homepage": "https://symfony.com", "keywords": ["compatibility", "intl", "normalizer", "polyfill", "portable", "shim"], "time": "2020-07-14T12:35:20+00:00"}, {"name": "symfony/polyfill-mbstring", "version": "v1.18.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-mbstring.git", "reference": "a6977d63bf9a0ad4c65cd352709e230876f9904a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-mbstring/zipball/a6977d63bf9a0ad4c65cd352709e230876f9904a", "reference": "a6977d63bf9a0ad4c65cd352709e230876f9904a", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=5.3.3"}, "suggest": {"ext-mbstring": "For best performance"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.18-dev"}, "thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"psr-4": {"Symfony\\Polyfill\\Mbstring\\": ""}, "files": ["bootstrap.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for the Mbstring extension", "homepage": "https://symfony.com", "keywords": ["compatibility", "mbstring", "polyfill", "portable", "shim"], "time": "2020-07-14T12:35:20+00:00"}, {"name": "symfony/polyfill-php56", "version": "v1.18.1", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-php56.git", "reference": "13df84e91cd168f247c2f2ec82cc0fa24901c011"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-php56/zipball/13df84e91cd168f247c2f2ec82cc0fa24901c011", "reference": "13df84e91cd168f247c2f2ec82cc0fa24901c011", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=5.3.3", "symfony/polyfill-util": "~1.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.18-dev"}, "thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"psr-4": {"Symfony\\Polyfill\\Php56\\": ""}, "files": ["bootstrap.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill backporting some PHP 5.6+ features to lower PHP versions", "homepage": "https://symfony.com", "keywords": ["compatibility", "polyfill", "portable", "shim"], "time": "2020-07-14T12:35:20+00:00"}, {"name": "symfony/polyfill-php70", "version": "v1.18.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-php70.git", "reference": "0dd93f2c578bdc9c72697eaa5f1dd25644e618d3"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-php70/zipball/0dd93f2c578bdc9c72697eaa5f1dd25644e618d3", "reference": "0dd93f2c578bdc9c72697eaa5f1dd25644e618d3", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"paragonie/random_compat": "~1.0|~2.0|~9.99", "php": ">=5.3.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.18-dev"}, "thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"psr-4": {"Symfony\\Polyfill\\Php70\\": ""}, "files": ["bootstrap.php"], "classmap": ["Resources/stubs"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill backporting some PHP 7.0+ features to lower PHP versions", "homepage": "https://symfony.com", "keywords": ["compatibility", "polyfill", "portable", "shim"], "time": "2020-07-14T12:35:20+00:00"}, {"name": "symfony/polyfill-php72", "version": "v1.18.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-php72.git", "reference": "639447d008615574653fb3bc60d1986d7172eaae"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-php72/zipball/639447d008615574653fb3bc60d1986d7172eaae", "reference": "639447d008615574653fb3bc60d1986d7172eaae", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=5.3.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.18-dev"}, "thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"psr-4": {"Symfony\\Polyfill\\Php72\\": ""}, "files": ["bootstrap.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill backporting some PHP 7.2+ features to lower PHP versions", "homepage": "https://symfony.com", "keywords": ["compatibility", "polyfill", "portable", "shim"], "time": "2020-07-14T12:35:20+00:00"}, {"name": "symfony/polyfill-php73", "version": "v1.18.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-php73.git", "reference": "fffa1a52a023e782cdcc221d781fe1ec8f87fcca"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-php73/zipball/fffa1a52a023e782cdcc221d781fe1ec8f87fcca", "reference": "fffa1a52a023e782cdcc221d781fe1ec8f87fcca", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=5.3.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.18-dev"}, "thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"psr-4": {"Symfony\\Polyfill\\Php73\\": ""}, "files": ["bootstrap.php"], "classmap": ["Resources/stubs"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill backporting some PHP 7.3+ features to lower PHP versions", "homepage": "https://symfony.com", "keywords": ["compatibility", "polyfill", "portable", "shim"], "time": "2020-07-14T12:35:20+00:00"}, {"name": "symfony/polyfill-php80", "version": "v1.18.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-php80.git", "reference": "d87d5766cbf48d72388a9f6b85f280c8ad51f981"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-php80/zipball/d87d5766cbf48d72388a9f6b85f280c8ad51f981", "reference": "d87d5766cbf48d72388a9f6b85f280c8ad51f981", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=7.0.8"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.18-dev"}, "thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"psr-4": {"Symfony\\Polyfill\\Php80\\": ""}, "files": ["bootstrap.php"], "classmap": ["Resources/stubs"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill backporting some PHP 8.0+ features to lower PHP versions", "homepage": "https://symfony.com", "keywords": ["compatibility", "polyfill", "portable", "shim"], "time": "2020-07-14T12:35:20+00:00"}, {"name": "symfony/polyfill-util", "version": "v1.18.1", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-util.git", "reference": "46b910c71e9828f8ec2aa7a0314de1130d9b295a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-util/zipball/46b910c71e9828f8ec2aa7a0314de1130d9b295a", "reference": "46b910c71e9828f8ec2aa7a0314de1130d9b295a", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=5.3.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.18-dev"}, "thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"psr-4": {"Symfony\\Polyfill\\Util\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony utilities for portability of PHP codes", "homepage": "https://symfony.com", "keywords": ["compat", "compatibility", "polyfill", "shim"], "time": "2020-07-14T12:35:20+00:00"}, {"name": "symfony/process", "version": "v5.1.2", "source": {"type": "git", "url": "https://github.com/symfony/process.git", "reference": "7f6378c1fa2147eeb1b4c385856ce9de0d46ebd1"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/process/zipball/7f6378c1fa2147eeb1b4c385856ce9de0d46ebd1", "reference": "7f6378c1fa2147eeb1b4c385856ce9de0d46ebd1", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=7.2.5", "symfony/polyfill-php80": "^1.15"}, "type": "library", "extra": {"branch-alias": {"dev-master": "5.1-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\Process\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Process Component", "homepage": "https://symfony.com", "time": "2020-05-30T20:35:19+00:00"}, {"name": "symfony/psr-http-message-bridge", "version": "v2.1.2", "source": {"type": "git", "url": "https://github.com/symfony/psr-http-message-bridge.git", "reference": "22b37c8a3f6b5d94e9cdbd88e1270d96e2f97b34"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/psr-http-message-bridge/zipball/22b37c8a3f6b5d94e9cdbd88e1270d96e2f97b34", "reference": "22b37c8a3f6b5d94e9cdbd88e1270d96e2f97b34", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=7.1", "psr/http-message": "^1.0", "symfony/http-foundation": "^4.4 || ^5.0 || ^6.0"}, "require-dev": {"nyholm/psr7": "^1.1", "psr/log": "^1.1 || ^2 || ^3", "symfony/browser-kit": "^4.4 || ^5.0 || ^6.0", "symfony/config": "^4.4 || ^5.0 || ^6.0", "symfony/event-dispatcher": "^4.4 || ^5.0 || ^6.0", "symfony/framework-bundle": "^4.4 || ^5.0 || ^6.0", "symfony/http-kernel": "^4.4 || ^5.0 || ^6.0", "symfony/phpunit-bridge": "^5.4@dev || ^6.0"}, "suggest": {"nyholm/psr7": "For a super lightweight PSR-7/17 implementation"}, "type": "symfony-bridge", "extra": {"branch-alias": {"dev-main": "2.1-dev"}}, "autoload": {"psr-4": {"Symfony\\Bridge\\PsrHttpMessage\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "http://symfony.com/contributors"}], "description": "PSR HTTP message bridge", "homepage": "http://symfony.com", "keywords": ["http", "http-message", "psr-17", "psr-7"], "support": {"issues": "https://github.com/symfony/psr-http-message-bridge/issues", "source": "https://github.com/symfony/psr-http-message-bridge/tree/v2.1.2"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2021-11-05T13:13:39+00:00"}, {"name": "symfony/routing", "version": "v5.1.2", "source": {"type": "git", "url": "https://github.com/symfony/routing.git", "reference": "bbd0ba121d623f66d165a55a108008968911f3eb"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/routing/zipball/bbd0ba121d623f66d165a55a108008968911f3eb", "reference": "bbd0ba121d623f66d165a55a108008968911f3eb", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=7.2.5", "symfony/deprecation-contracts": "^2.1", "symfony/polyfill-php80": "^1.15"}, "conflict": {"symfony/config": "<5.0", "symfony/dependency-injection": "<4.4", "symfony/yaml": "<4.4"}, "require-dev": {"doctrine/annotations": "~1.2", "psr/log": "~1.0", "symfony/config": "^5.0", "symfony/dependency-injection": "^4.4|^5.0", "symfony/expression-language": "^4.4|^5.0", "symfony/http-foundation": "^4.4|^5.0", "symfony/yaml": "^4.4|^5.0"}, "suggest": {"doctrine/annotations": "For using the annotation loader", "symfony/config": "For using the all-in-one router or any loader", "symfony/expression-language": "For using expression matching", "symfony/http-foundation": "For using a Symfony Request object", "symfony/yaml": "For using the YAML loader"}, "type": "library", "extra": {"branch-alias": {"dev-master": "5.1-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\Routing\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Routing Component", "homepage": "https://symfony.com", "keywords": ["router", "routing", "uri", "url"], "time": "2020-06-10T11:49:58+00:00"}, {"name": "symfony/service-contracts", "version": "v2.1.3", "source": {"type": "git", "url": "https://github.com/symfony/service-contracts.git", "reference": "58c7475e5457c5492c26cc740cc0ad7464be9442"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/service-contracts/zipball/58c7475e5457c5492c26cc740cc0ad7464be9442", "reference": "58c7475e5457c5492c26cc740cc0ad7464be9442", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=7.2.5", "psr/container": "^1.0"}, "suggest": {"symfony/service-implementation": ""}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.1-dev"}, "thanks": {"name": "symfony/contracts", "url": "https://github.com/symfony/contracts"}}, "autoload": {"psr-4": {"Symfony\\Contracts\\Service\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Generic abstractions related to writing services", "homepage": "https://symfony.com", "keywords": ["abstractions", "contracts", "decoupling", "interfaces", "interoperability", "standards"], "time": "2020-07-06T13:23:11+00:00"}, {"name": "symfony/string", "version": "v5.1.2", "source": {"type": "git", "url": "https://github.com/symfony/string.git", "reference": "ac70459db781108db7c6d8981dd31ce0e29e3298"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/string/zipball/ac70459db781108db7c6d8981dd31ce0e29e3298", "reference": "ac70459db781108db7c6d8981dd31ce0e29e3298", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=7.2.5", "symfony/polyfill-ctype": "~1.8", "symfony/polyfill-intl-grapheme": "~1.0", "symfony/polyfill-intl-normalizer": "~1.0", "symfony/polyfill-mbstring": "~1.0", "symfony/polyfill-php80": "~1.15"}, "require-dev": {"symfony/error-handler": "^4.4|^5.0", "symfony/http-client": "^4.4|^5.0", "symfony/translation-contracts": "^1.1|^2", "symfony/var-exporter": "^4.4|^5.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "5.1-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\String\\": ""}, "files": ["Resources/functions.php"], "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony String component", "homepage": "https://symfony.com", "keywords": ["grapheme", "i18n", "string", "unicode", "utf-8", "utf8"], "time": "2020-06-11T12:16:36+00:00"}, {"name": "symfony/translation", "version": "v5.1.2", "source": {"type": "git", "url": "https://github.com/symfony/translation.git", "reference": "d387f07d4c15f9c09439cf3f13ddbe0b2c5e8be2"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/translation/zipball/d387f07d4c15f9c09439cf3f13ddbe0b2c5e8be2", "reference": "d387f07d4c15f9c09439cf3f13ddbe0b2c5e8be2", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=7.2.5", "symfony/polyfill-mbstring": "~1.0", "symfony/polyfill-php80": "^1.15", "symfony/translation-contracts": "^2"}, "conflict": {"symfony/config": "<4.4", "symfony/dependency-injection": "<5.0", "symfony/http-kernel": "<5.0", "symfony/twig-bundle": "<5.0", "symfony/yaml": "<4.4"}, "provide": {"symfony/translation-implementation": "2.0"}, "require-dev": {"psr/log": "~1.0", "symfony/config": "^4.4|^5.0", "symfony/console": "^4.4|^5.0", "symfony/dependency-injection": "^5.0", "symfony/finder": "^4.4|^5.0", "symfony/http-kernel": "^5.0", "symfony/intl": "^4.4|^5.0", "symfony/service-contracts": "^1.1.2|^2", "symfony/yaml": "^4.4|^5.0"}, "suggest": {"psr/log-implementation": "To use logging capability in translator", "symfony/config": "", "symfony/yaml": ""}, "type": "library", "extra": {"branch-alias": {"dev-master": "5.1-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\Translation\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Translation Component", "homepage": "https://symfony.com", "time": "2020-05-30T20:35:19+00:00"}, {"name": "symfony/translation-contracts", "version": "v2.1.3", "source": {"type": "git", "url": "https://github.com/symfony/translation-contracts.git", "reference": "616a9773c853097607cf9dd6577d5b143ffdcd63"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/translation-contracts/zipball/616a9773c853097607cf9dd6577d5b143ffdcd63", "reference": "616a9773c853097607cf9dd6577d5b143ffdcd63", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=7.2.5"}, "suggest": {"symfony/translation-implementation": ""}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.1-dev"}, "thanks": {"name": "symfony/contracts", "url": "https://github.com/symfony/contracts"}}, "autoload": {"psr-4": {"Symfony\\Contracts\\Translation\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Generic abstractions related to translation", "homepage": "https://symfony.com", "keywords": ["abstractions", "contracts", "decoupling", "interfaces", "interoperability", "standards"], "time": "2020-07-06T13:23:11+00:00"}, {"name": "symfony/var-dumper", "version": "v5.1.2", "source": {"type": "git", "url": "https://github.com/symfony/var-dumper.git", "reference": "46a942903059b0b05e601f00eb64179e05578c0f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/var-dumper/zipball/46a942903059b0b05e601f00eb64179e05578c0f", "reference": "46a942903059b0b05e601f00eb64179e05578c0f", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=7.2.5", "symfony/polyfill-mbstring": "~1.0", "symfony/polyfill-php80": "^1.15"}, "conflict": {"phpunit/phpunit": "<5.4.3", "symfony/console": "<4.4"}, "require-dev": {"ext-iconv": "*", "symfony/console": "^4.4|^5.0", "symfony/process": "^4.4|^5.0", "twig/twig": "^2.4|^3.0"}, "suggest": {"ext-iconv": "To convert non-UTF-8 strings to UTF-8 (or symfony/polyfill-iconv in case ext-iconv cannot be used).", "ext-intl": "To show region name in time zone dump", "symfony/console": "To use the ServerDumpCommand and/or the bin/var-dump-server script"}, "bin": ["Resources/bin/var-dump-server"], "type": "library", "extra": {"branch-alias": {"dev-master": "5.1-dev"}}, "autoload": {"files": ["Resources/functions/dump.php"], "psr-4": {"Symfony\\Component\\VarDumper\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony mechanism for exploring and dumping PHP variables", "homepage": "https://symfony.com", "keywords": ["debug", "dump"], "time": "2020-05-30T20:35:19+00:00"}, {"name": "symfony/var-exporter", "version": "v5.4.7", "source": {"type": "git", "url": "https://github.com/symfony/var-exporter.git", "reference": "7eacaa588c9b27f2738575adb4a8457a80d9c807"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/var-exporter/zipball/7eacaa588c9b27f2738575adb4a8457a80d9c807", "reference": "7eacaa588c9b27f2738575adb4a8457a80d9c807", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=7.2.5", "symfony/polyfill-php80": "^1.16"}, "require-dev": {"symfony/var-dumper": "^4.4.9|^5.0.9|^6.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\VarExporter\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Allows exporting any serializable PHP data structure to plain PHP code", "homepage": "https://symfony.com", "keywords": ["clone", "construct", "export", "hydrate", "instantiate", "serialize"], "support": {"source": "https://github.com/symfony/var-exporter/tree/v5.4.7"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2022-03-31T17:09:19+00:00"}, {"name": "tijsverkoyen/css-to-inline-styles", "version": "2.2.3", "source": {"type": "git", "url": "https://github.com/tijsverkoyen/CssToInlineStyles.git", "reference": "b43b05cf43c1b6d849478965062b6ef73e223bb5"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/tijsverkoyen/CssToInlineStyles/zipball/b43b05cf43c1b6d849478965062b6ef73e223bb5", "reference": "b43b05cf43c1b6d849478965062b6ef73e223bb5", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"ext-dom": "*", "ext-libxml": "*", "php": "^5.5 || ^7.0 || ^8.0", "symfony/css-selector": "^2.7 || ^3.0 || ^4.0 || ^5.0"}, "require-dev": {"phpunit/phpunit": "^4.8.35 || ^5.7 || ^6.0 || ^7.5"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.2.x-dev"}}, "autoload": {"psr-4": {"TijsVerkoyen\\CssToInlineStyles\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "CssToInlineStyles is a class that enables you to convert HTML-pages/files into HTML-pages/files with inline styles. This is very useful when you're sending emails.", "homepage": "https://github.com/tijsverkoyen/CssToInlineStyles", "time": "2020-07-13T06:12:54+00:00"}, {"name": "vlucas/phpdotenv", "version": "v4.1.8", "source": {"type": "git", "url": "https://github.com/vlucas/phpdotenv.git", "reference": "572af79d913627a9d70374d27a6f5d689a35de32"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/vlucas/phpdotenv/zipball/572af79d913627a9d70374d27a6f5d689a35de32", "reference": "572af79d913627a9d70374d27a6f5d689a35de32", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": "^5.5.9 || ^7.0 || ^8.0", "phpoption/phpoption": "^1.7.3", "symfony/polyfill-ctype": "^1.17"}, "require-dev": {"bamarni/composer-bin-plugin": "^1.4.1", "ext-filter": "*", "ext-pcre": "*", "phpunit/phpunit": "^4.8.35 || ^5.7.27 || ^6.5.6 || ^7.0"}, "suggest": {"ext-filter": "Required to use the boolean validator.", "ext-pcre": "Required to use most of the library."}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.1-dev"}}, "autoload": {"psr-4": {"Dotenv\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://gjcampbell.co.uk/"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://vancelucas.com/"}], "description": "Loads environment variables from `.env` to `getenv()`, `$_ENV` and `$_SERVER` automagically.", "keywords": ["dotenv", "env", "environment"], "time": "2020-07-14T19:22:52+00:00"}, {"name": "voku/portable-ascii", "version": "1.5.2", "source": {"type": "git", "url": "https://github.com/voku/portable-ascii.git", "reference": "618631dc601d8eb6ea0a9fbf654ec82f066c4e97"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/voku/portable-ascii/zipball/618631dc601d8eb6ea0a9fbf654ec82f066c4e97", "reference": "618631dc601d8eb6ea0a9fbf654ec82f066c4e97", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=7.0.0"}, "require-dev": {"phpunit/phpunit": "~6.0 || ~7.0"}, "suggest": {"ext-intl": "Use Intl for transliterator_transliterate() support"}, "type": "library", "autoload": {"psr-4": {"voku\\": "src/voku/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "homepage": "http://www.moelleken.org/"}], "description": "Portable ASCII library - performance optimized (ascii) string functions for php.", "homepage": "https://github.com/voku/portable-ascii", "keywords": ["ascii", "clean", "php"], "time": "2020-06-15T23:49:30+00:00"}, {"name": "youthage/laravel-3des", "version": "4.0", "source": {"type": "git", "url": "https://github.com/heyhip/Laravel-3DES.git", "reference": "ff0d556106d353b335d1ae871091a189814fe2c1"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/heyhip/Laravel-3DES/zipball/ff0d556106d353b335d1ae871091a189814fe2c1", "reference": "ff0d556106d353b335d1ae871091a189814fe2c1", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=5.3.9"}, "type": "library", "extra": {"laravel": {"providers": ["laraveldes3\\Des3Provider"], "aliases": {"DES3": "laraveldes3\\Des3Facade"}}}, "autoload": {"psr-4": {"laraveldes3\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "YouthAge", "email": "<EMAIL>"}], "description": "Laravel Des3 encode", "time": "2019-03-14T15:52:27+00:00"}], "packages-dev": [{"name": "barryvdh/laravel-debugbar", "version": "v3.3.3", "source": {"type": "git", "url": "https://github.com/barryvdh/laravel-debugbar.git", "reference": "57f2219f6d9efe41ed1bc880d86701c52f261bf5"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/barryvdh/laravel-debugbar/zipball/57f2219f6d9efe41ed1bc880d86701c52f261bf5", "reference": "57f2219f6d9efe41ed1bc880d86701c52f261bf5", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"illuminate/routing": "^5.5|^6|^7", "illuminate/session": "^5.5|^6|^7", "illuminate/support": "^5.5|^6|^7", "maximebf/debugbar": "^1.15.1", "php": ">=7.0", "symfony/debug": "^3|^4|^5", "symfony/finder": "^3|^4|^5"}, "require-dev": {"laravel/framework": "5.5.x"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.2-dev"}, "laravel": {"providers": ["Barryvdh\\Debugbar\\ServiceProvider"], "aliases": {"Debugbar": "Barryvdh\\Debugbar\\Facade"}}}, "autoload": {"psr-4": {"Barryvdh\\Debugbar\\": "src/"}, "files": ["src/helpers.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Barry vd. Heuvel", "email": "<EMAIL>"}], "description": "PHP Debugbar integration for Laravel", "keywords": ["debug", "debugbar", "laravel", "profiler", "webprofiler"], "time": "2020-05-05T10:53:32+00:00"}, {"name": "doctrine/instantiator", "version": "1.3.1", "source": {"type": "git", "url": "https://github.com/doctrine/instantiator.git", "reference": "f350df0268e904597e3bd9c4685c53e0e333feea"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/instantiator/zipball/f350df0268e904597e3bd9c4685c53e0e333feea", "reference": "f350df0268e904597e3bd9c4685c53e0e333feea", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": "^7.1 || ^8.0"}, "require-dev": {"doctrine/coding-standard": "^6.0", "ext-pdo": "*", "ext-phar": "*", "phpbench/phpbench": "^0.13", "phpstan/phpstan-phpunit": "^0.11", "phpstan/phpstan-shim": "^0.11", "phpunit/phpunit": "^7.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.2.x-dev"}}, "autoload": {"psr-4": {"Doctrine\\Instantiator\\": "src/Doctrine/Instantiator/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://ocramius.github.com/"}], "description": "A small, lightweight utility to instantiate objects in PHP without invoking their constructors", "homepage": "https://www.doctrine-project.org/projects/instantiator.html", "keywords": ["constructor", "instantiate"], "time": "2020-05-29T17:27:14+00:00"}, {"name": "facade/flare-client-php", "version": "1.3.4", "source": {"type": "git", "url": "https://github.com/facade/flare-client-php.git", "reference": "0eeb0de4fc1078433f0915010bd8f41e998adcb4"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/facade/flare-client-php/zipball/0eeb0de4fc1078433f0915010bd8f41e998adcb4", "reference": "0eeb0de4fc1078433f0915010bd8f41e998adcb4", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"facade/ignition-contracts": "~1.0", "illuminate/pipeline": "^5.5|^6.0|^7.0", "php": "^7.1", "symfony/http-foundation": "^3.3|^4.1|^5.0", "symfony/mime": "^3.4|^4.0|^5.1", "symfony/var-dumper": "^3.4|^4.0|^5.0"}, "require-dev": {"friendsofphp/php-cs-fixer": "^2.14", "larapack/dd": "^1.1", "phpunit/phpunit": "^7.5.16", "spatie/phpunit-snapshot-assertions": "^2.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0-dev"}}, "autoload": {"psr-4": {"Facade\\FlareClient\\": "src"}, "files": ["src/helpers.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "Send PHP errors to <PERSON><PERSON><PERSON>", "homepage": "https://github.com/facade/flare-client-php", "keywords": ["exception", "facade", "flare", "reporting"], "time": "2020-07-13T23:25:57+00:00"}, {"name": "facade/ignition", "version": "2.3.3", "source": {"type": "git", "url": "https://github.com/facade/ignition.git", "reference": "cc7df15806aad8a9915148ea4daf7f0dd0be45b5"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/facade/ignition/zipball/cc7df15806aad8a9915148ea4daf7f0dd0be45b5", "reference": "cc7df15806aad8a9915148ea4daf7f0dd0be45b5", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"ext-json": "*", "ext-mbstring": "*", "facade/flare-client-php": "^1.0", "facade/ignition-contracts": "^1.0", "filp/whoops": "^2.4", "illuminate/support": "^7.0|^8.0", "monolog/monolog": "^2.0", "php": "^7.2.5", "scrivo/highlight.php": "^9.15", "symfony/console": "^5.0", "symfony/var-dumper": "^5.0"}, "require-dev": {"friendsofphp/php-cs-fixer": "^2.14", "mockery/mockery": "^1.3", "orchestra/testbench": "5.0", "psalm/plugin-laravel": "^1.2"}, "suggest": {"laravel/telescope": "^3.1"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.x-dev"}, "laravel": {"providers": ["Facade\\Ignition\\IgnitionServiceProvider"], "aliases": {"Flare": "Facade\\Ignition\\Facades\\Flare"}}}, "autoload": {"psr-4": {"Facade\\Ignition\\": "src"}, "files": ["src/helpers.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "A beautiful error page for Laravel applications.", "homepage": "https://github.com/facade/ignition", "keywords": ["error", "flare", "laravel", "page"], "time": "2020-07-14T11:34:42+00:00"}, {"name": "facade/ignition-contracts", "version": "1.0.1", "source": {"type": "git", "url": "https://github.com/facade/ignition-contracts.git", "reference": "aeab1ce8b68b188a43e81758e750151ad7da796b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/facade/ignition-contracts/zipball/aeab1ce8b68b188a43e81758e750151ad7da796b", "reference": "aeab1ce8b68b188a43e81758e750151ad7da796b", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": "^7.1"}, "require-dev": {"friendsofphp/php-cs-fixer": "^2.14", "phpunit/phpunit": "^7.5|^8.0", "vimeo/psalm": "^3.12"}, "type": "library", "autoload": {"psr-4": {"Facade\\IgnitionContracts\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://flareapp.io", "role": "Developer"}], "description": "Solution contracts for Ignition", "homepage": "https://github.com/facade/ignition-contracts", "keywords": ["contracts", "flare", "ignition"], "time": "2020-07-14T10:10:28+00:00"}, {"name": "filp/whoops", "version": "2.7.3", "source": {"type": "git", "url": "https://github.com/filp/whoops.git", "reference": "5d5fe9bb3d656b514d455645b3addc5f7ba7714d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/filp/whoops/zipball/5d5fe9bb3d656b514d455645b3addc5f7ba7714d", "reference": "5d5fe9bb3d656b514d455645b3addc5f7ba7714d", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": "^5.5.9 || ^7.0", "psr/log": "^1.0.1"}, "require-dev": {"mockery/mockery": "^0.9 || ^1.0", "phpunit/phpunit": "^4.8.35 || ^5.7 || ^6.0", "symfony/var-dumper": "^2.6 || ^3.0 || ^4.0 || ^5.0"}, "suggest": {"symfony/var-dumper": "Pretty print complex values better with var-dumper available", "whoops/soap": "Formats errors as SOAP responses"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.6-dev"}}, "autoload": {"psr-4": {"Whoops\\": "src/Whoops/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "homepage": "https://github.com/filp", "role": "Developer"}], "description": "php error handling for cool kids", "homepage": "https://filp.github.io/whoops/", "keywords": ["error", "exception", "handling", "library", "throwable", "whoops"], "time": "2020-06-14T09:00:00+00:00"}, {"name": "fzaninotto/faker", "version": "v1.9.1", "source": {"type": "git", "url": "https://github.com/fzaninotto/Faker.git", "reference": "fc10d778e4b84d5bd315dad194661e091d307c6f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/fzaninotto/Faker/zipball/fc10d778e4b84d5bd315dad194661e091d307c6f", "reference": "fc10d778e4b84d5bd315dad194661e091d307c6f", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": "^5.3.3 || ^7.0"}, "require-dev": {"ext-intl": "*", "phpunit/phpunit": "^4.8.35 || ^5.7", "squizlabs/php_codesniffer": "^2.9.2"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.9-dev"}}, "autoload": {"psr-4": {"Faker\\": "src/Faker/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>"}], "description": "Faker is a PHP library that generates fake data for you.", "keywords": ["data", "faker", "fixtures"], "abandoned": true, "time": "2019-12-12T13:22:17+00:00"}, {"name": "hamcrest/hamcrest-php", "version": "v2.0.1", "source": {"type": "git", "url": "https://github.com/hamcrest/hamcrest-php.git", "reference": "8c3d0a3f6af734494ad8f6fbbee0ba92422859f3"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/hamcrest/hamcrest-php/zipball/8c3d0a3f6af734494ad8f6fbbee0ba92422859f3", "reference": "8c3d0a3f6af734494ad8f6fbbee0ba92422859f3", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": "^5.3|^7.0|^8.0"}, "replace": {"cordoval/hamcrest-php": "*", "davedevelopment/hamcrest-php": "*", "kodova/hamcrest-php": "*"}, "require-dev": {"phpunit/php-file-iterator": "^1.4 || ^2.0", "phpunit/phpunit": "^4.8.36 || ^5.7 || ^6.5 || ^7.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.1-dev"}}, "autoload": {"classmap": ["hamcrest"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "description": "This is the PHP port of Hamcrest Matchers", "keywords": ["test"], "time": "2020-07-09T08:09:16+00:00"}, {"name": "maximebf/debugbar", "version": "v1.16.3", "source": {"type": "git", "url": "https://github.com/maximebf/php-debugbar.git", "reference": "1a1605b8e9bacb34cc0c6278206d699772e1d372"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/maximebf/php-debugbar/zipball/1a1605b8e9bacb34cc0c6278206d699772e1d372", "reference": "1a1605b8e9bacb34cc0c6278206d699772e1d372", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": "^7.1", "psr/log": "^1.0", "symfony/var-dumper": "^2.6|^3|^4|^5"}, "require-dev": {"phpunit/phpunit": "^5"}, "suggest": {"kriswallsmith/assetic": "The best way to manage assets", "monolog/monolog": "Log using Monolog", "predis/predis": "Redis storage"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.16-dev"}}, "autoload": {"psr-4": {"DebugBar\\": "src/DebugBar/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "maxime.bouroume<PERSON>@gmail.com", "homepage": "http://maximebf.com"}, {"name": "Barry vd. Heuvel", "email": "<EMAIL>"}], "description": "Debug bar in the browser for php application", "homepage": "https://github.com/maximebf/php-debugbar", "keywords": ["debug", "debugbar"], "time": "2020-05-06T07:06:27+00:00"}, {"name": "mockery/mockery", "version": "1.4.1", "source": {"type": "git", "url": "https://github.com/mockery/mockery.git", "reference": "1404386ca3410b04fe58b9517e85d702ab33b2c6"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/mockery/mockery/zipball/1404386ca3410b04fe58b9517e85d702ab33b2c6", "reference": "1404386ca3410b04fe58b9517e85d702ab33b2c6", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"hamcrest/hamcrest-php": "^2.0.1", "lib-pcre": ">=7.0", "php": "^7.3 || ^8.0"}, "conflict": {"phpunit/phpunit": "<8.0"}, "require-dev": {"phpunit/phpunit": "^8.5 || ^9.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.4.x-dev"}}, "autoload": {"psr-0": {"Mockery": "library/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://blog.astrumfutura.com"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://davedevelopment.co.uk"}], "description": "Mockery is a simple yet flexible PHP mock object framework", "homepage": "https://github.com/mockery/mockery", "keywords": ["BDD", "TDD", "library", "mock", "mock objects", "mockery", "stub", "test", "test double", "testing"], "time": "2020-07-09T08:31:54+00:00"}, {"name": "myclabs/deep-copy", "version": "1.10.1", "source": {"type": "git", "url": "https://github.com/myclabs/DeepCopy.git", "reference": "969b211f9a51aa1f6c01d1d2aef56d3bd91598e5"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/myclabs/DeepCopy/zipball/969b211f9a51aa1f6c01d1d2aef56d3bd91598e5", "reference": "969b211f9a51aa1f6c01d1d2aef56d3bd91598e5", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": "^7.1 || ^8.0"}, "replace": {"myclabs/deep-copy": "self.version"}, "require-dev": {"doctrine/collections": "^1.0", "doctrine/common": "^2.6", "phpunit/phpunit": "^7.1"}, "type": "library", "autoload": {"psr-4": {"DeepCopy\\": "src/DeepCopy/"}, "files": ["src/DeepCopy/deep_copy.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "Create deep copies (clones) of your objects", "keywords": ["clone", "copy", "duplicate", "object", "object graph"], "time": "2020-06-29T13:22:24+00:00"}, {"name": "nunomaduro/collision", "version": "v4.2.0", "source": {"type": "git", "url": "https://github.com/nunomaduro/collision.git", "reference": "d50490417eded97be300a92cd7df7badc37a9018"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nunomaduro/collision/zipball/d50490417eded97be300a92cd7df7badc37a9018", "reference": "d50490417eded97be300a92cd7df7badc37a9018", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"facade/ignition-contracts": "^1.0", "filp/whoops": "^2.4", "php": "^7.2.5", "symfony/console": "^5.0"}, "require-dev": {"facade/ignition": "^2.0", "fideloper/proxy": "^4.2", "friendsofphp/php-cs-fixer": "^2.16", "fruitcake/laravel-cors": "^1.0", "laravel/framework": "^7.0", "laravel/tinker": "^2.0", "nunomaduro/larastan": "^0.5", "orchestra/testbench": "^5.0", "phpstan/phpstan": "^0.12.3", "phpunit/phpunit": "^8.5.1 || ^9.0"}, "type": "library", "extra": {"laravel": {"providers": ["NunoMaduro\\Collision\\Adapters\\Laravel\\CollisionServiceProvider"]}}, "autoload": {"psr-4": {"NunoMaduro\\Collision\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Cli error handling for console/command-line PHP applications.", "keywords": ["artisan", "cli", "command-line", "console", "error", "handling", "laravel", "laravel-zero", "php", "symfony"], "time": "2020-04-04T19:56:08+00:00"}, {"name": "overtrue/laravel-query-logger", "version": "1.2.0", "source": {"type": "git", "url": "https://github.com/overtrue/laravel-query-logger.git", "reference": "9aef8bf37d0bc20130688cd0e4221bbd448f9e14"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/overtrue/laravel-query-logger/zipball/9aef8bf37d0bc20130688cd0e4221bbd448f9e14", "reference": "9aef8bf37d0bc20130688cd0e4221bbd448f9e14", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"laravel/framework": "~5.5||~6.0||~7.0"}, "type": "library", "extra": {"laravel": {"providers": ["Overtrue\\LaravelQueryLogger\\ServiceProvider"]}, "hooks": {"pre-commit": ["composer test", "composer fix-style"], "pre-push": ["composer test", "composer check-style"]}}, "autoload": {"psr-4": {"Overtrue\\LaravelQueryLogger\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "overtrue", "email": "<EMAIL>"}], "description": "A dev tool to log all queries for laravel application.", "time": "2020-07-22T03:55:01+00:00"}, {"name": "phar-io/manifest", "version": "1.0.3", "source": {"type": "git", "url": "https://github.com/phar-io/manifest.git", "reference": "7761fcacf03b4d4f16e7ccb606d4879ca431fcf4"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phar-io/manifest/zipball/7761fcacf03b4d4f16e7ccb606d4879ca431fcf4", "reference": "7761fcacf03b4d4f16e7ccb606d4879ca431fcf4", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"ext-dom": "*", "ext-phar": "*", "phar-io/version": "^2.0", "php": "^5.6 || ^7.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "Component for reading phar.io manifest information from a PHP Archive (PHAR)", "time": "2018-07-08T19:23:20+00:00"}, {"name": "phar-io/version", "version": "2.0.1", "source": {"type": "git", "url": "https://github.com/phar-io/version.git", "reference": "45a2ec53a73c70ce41d55cedef9063630abaf1b6"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phar-io/version/zipball/45a2ec53a73c70ce41d55cedef9063630abaf1b6", "reference": "45a2ec53a73c70ce41d55cedef9063630abaf1b6", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": "^5.6 || ^7.0"}, "type": "library", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "Library for handling version information and constraints", "time": "2018-07-08T19:19:57+00:00"}, {"name": "phpdocumentor/reflection-common", "version": "2.2.0", "source": {"type": "git", "url": "https://github.com/phpDocumentor/ReflectionCommon.git", "reference": "1d01c49d4ed62f25aa84a747ad35d5a16924662b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phpDocumentor/ReflectionCommon/zipball/1d01c49d4ed62f25aa84a747ad35d5a16924662b", "reference": "1d01c49d4ed62f25aa84a747ad35d5a16924662b", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": "^7.2 || ^8.0"}, "type": "library", "extra": {"branch-alias": {"dev-2.x": "2.x-dev"}}, "autoload": {"psr-4": {"phpDocumentor\\Reflection\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Common reflection classes used by phpdocumentor to reflect the code structure", "homepage": "http://www.phpdoc.org", "keywords": ["FQSEN", "phpDocumentor", "phpdoc", "reflection", "static analysis"], "time": "2020-06-27T09:03:43+00:00"}, {"name": "phpdocumentor/reflection-docblock", "version": "5.1.0", "source": {"type": "git", "url": "https://github.com/phpDocumentor/ReflectionDocBlock.git", "reference": "cd72d394ca794d3466a3b2fc09d5a6c1dc86b47e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phpDocumentor/ReflectionDocBlock/zipball/cd72d394ca794d3466a3b2fc09d5a6c1dc86b47e", "reference": "cd72d394ca794d3466a3b2fc09d5a6c1dc86b47e", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"ext-filter": "^7.1", "php": "^7.2", "phpdocumentor/reflection-common": "^2.0", "phpdocumentor/type-resolver": "^1.0", "webmozart/assert": "^1"}, "require-dev": {"doctrine/instantiator": "^1", "mockery/mockery": "^1"}, "type": "library", "extra": {"branch-alias": {"dev-master": "5.x-dev"}}, "autoload": {"psr-4": {"phpDocumentor\\Reflection\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "With this component, a library can provide support for annotations via DocBlocks or otherwise retrieve information that is embedded in a DocBlock.", "time": "2020-02-22T12:28:44+00:00"}, {"name": "phpdocumentor/type-resolver", "version": "1.3.0", "source": {"type": "git", "url": "https://github.com/phpDocumentor/TypeResolver.git", "reference": "e878a14a65245fbe78f8080eba03b47c3b705651"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phpDocumentor/TypeResolver/zipball/e878a14a65245fbe78f8080eba03b47c3b705651", "reference": "e878a14a65245fbe78f8080eba03b47c3b705651", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": "^7.2 || ^8.0", "phpdocumentor/reflection-common": "^2.0"}, "require-dev": {"ext-tokenizer": "*"}, "type": "library", "extra": {"branch-alias": {"dev-1.x": "1.x-dev"}}, "autoload": {"psr-4": {"phpDocumentor\\Reflection\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "A PSR-5 based resolver of Class names, Types and Structural Element Names", "time": "2020-06-27T10:12:23+00:00"}, {"name": "phpspec/prophecy", "version": "1.11.1", "source": {"type": "git", "url": "https://github.com/phpspec/prophecy.git", "reference": "b20034be5efcdab4fb60ca3a29cba2949aead160"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phpspec/prophecy/zipball/b20034be5efcdab4fb60ca3a29cba2949aead160", "reference": "b20034be5efcdab4fb60ca3a29cba2949aead160", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"doctrine/instantiator": "^1.2", "php": "^7.2", "phpdocumentor/reflection-docblock": "^5.0", "sebastian/comparator": "^3.0 || ^4.0", "sebastian/recursion-context": "^3.0 || ^4.0"}, "require-dev": {"phpspec/phpspec": "^6.0", "phpunit/phpunit": "^8.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.11.x-dev"}}, "autoload": {"psr-4": {"Prophecy\\": "src/Prophecy"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://everzet.com"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Highly opinionated mocking framework for PHP 5.3+", "homepage": "https://github.com/phpspec/prophecy", "keywords": ["Double", "Dummy", "fake", "mock", "spy", "stub"], "time": "2020-07-08T12:44:21+00:00"}, {"name": "phpunit/php-code-coverage", "version": "7.0.10", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage.git", "reference": "f1884187926fbb755a9aaf0b3836ad3165b478bf"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON>mann/php-code-coverage/zipball/f1884187926fbb755a9aaf0b3836ad3165b478bf", "reference": "f1884187926fbb755a9aaf0b3836ad3165b478bf", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"ext-dom": "*", "ext-xmlwriter": "*", "php": "^7.2", "phpunit/php-file-iterator": "^2.0.2", "phpunit/php-text-template": "^1.2.1", "phpunit/php-token-stream": "^3.1.1", "sebastian/code-unit-reverse-lookup": "^1.0.1", "sebastian/environment": "^4.2.2", "sebastian/version": "^2.0.1", "theseer/tokenizer": "^1.1.3"}, "require-dev": {"phpunit/phpunit": "^8.2.2"}, "suggest": {"ext-xdebug": "^2.7.2"}, "type": "library", "extra": {"branch-alias": {"dev-master": "7.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Library that provides collection, processing, and rendering functionality for PHP code coverage information.", "homepage": "https://github.com/sebastian<PERSON>mann/php-code-coverage", "keywords": ["coverage", "testing", "xunit"], "time": "2019-11-20T13:55:58+00:00"}, {"name": "phpunit/php-file-iterator", "version": "2.0.2", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON>mann/php-file-iterator.git", "reference": "050bedf145a257b1ff02746c31894800e5122946"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-file-iterator/zipball/050bedf145a257b1ff02746c31894800e5122946", "reference": "050bedf145a257b1ff02746c31894800e5122946", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": "^7.1"}, "require-dev": {"phpunit/phpunit": "^7.1"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "FilterIterator implementation that filters files based on a list of suffixes.", "homepage": "https://github.com/sebas<PERSON><PERSON>mann/php-file-iterator/", "keywords": ["filesystem", "iterator"], "time": "2018-09-13T20:33:42+00:00"}, {"name": "phpunit/php-text-template", "version": "1.2.1", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON>mann/php-text-template.git", "reference": "31f8b717e51d9a2afca6c9f046f5d69fc27c8686"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/se<PERSON><PERSON><PERSON><PERSON>/php-text-template/zipball/31f8b717e51d9a2afca6c9f046f5d69fc27c8686", "reference": "31f8b717e51d9a2afca6c9f046f5d69fc27c8686", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=5.3.3"}, "type": "library", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Simple template engine.", "homepage": "https://github.com/sebas<PERSON><PERSON>mann/php-text-template/", "keywords": ["template"], "time": "2015-06-21T13:50:34+00:00"}, {"name": "phpunit/php-timer", "version": "2.1.2", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/php-timer.git", "reference": "1038454804406b0b5f5f520358e78c1c2f71501e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebastian<PERSON>mann/php-timer/zipball/1038454804406b0b5f5f520358e78c1c2f71501e", "reference": "1038454804406b0b5f5f520358e78c1c2f71501e", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": "^7.1"}, "require-dev": {"phpunit/phpunit": "^7.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.1-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Utility class for timing", "homepage": "https://github.com/sebastian<PERSON>mann/php-timer/", "keywords": ["timer"], "time": "2019-06-07T04:22:29+00:00"}, {"name": "phpunit/php-token-stream", "version": "3.1.1", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/php-token-stream.git", "reference": "995192df77f63a59e47f025390d2d1fdf8f425ff"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON>mann/php-token-stream/zipball/995192df77f63a59e47f025390d2d1fdf8f425ff", "reference": "995192df77f63a59e47f025390d2d1fdf8f425ff", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"ext-tokenizer": "*", "php": "^7.1"}, "require-dev": {"phpunit/phpunit": "^7.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.1-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Wrapper around PHP's tokenizer extension.", "homepage": "https://github.com/sebastian<PERSON>mann/php-token-stream/", "keywords": ["tokenizer"], "abandoned": true, "time": "2019-09-17T06:23:10+00:00"}, {"name": "phpunit/phpunit", "version": "8.5.8", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/phpunit.git", "reference": "34c18baa6a44f1d1fbf0338907139e9dce95b997"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebastian<PERSON>mann/phpunit/zipball/34c18baa6a44f1d1fbf0338907139e9dce95b997", "reference": "34c18baa6a44f1d1fbf0338907139e9dce95b997", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"doctrine/instantiator": "^1.2.0", "ext-dom": "*", "ext-json": "*", "ext-libxml": "*", "ext-mbstring": "*", "ext-xml": "*", "ext-xmlwriter": "*", "myclabs/deep-copy": "^1.9.1", "phar-io/manifest": "^1.0.3", "phar-io/version": "^2.0.1", "php": "^7.2", "phpspec/prophecy": "^1.8.1", "phpunit/php-code-coverage": "^7.0.7", "phpunit/php-file-iterator": "^2.0.2", "phpunit/php-text-template": "^1.2.1", "phpunit/php-timer": "^2.1.2", "sebastian/comparator": "^3.0.2", "sebastian/diff": "^3.0.2", "sebastian/environment": "^4.2.2", "sebastian/exporter": "^3.1.1", "sebastian/global-state": "^3.0.0", "sebastian/object-enumerator": "^3.0.3", "sebastian/resource-operations": "^2.0.1", "sebastian/type": "^1.1.3", "sebastian/version": "^2.0.1"}, "require-dev": {"ext-pdo": "*"}, "suggest": {"ext-soap": "*", "ext-xdebug": "*", "phpunit/php-invoker": "^2.0.0"}, "bin": ["phpunit"], "type": "library", "extra": {"branch-alias": {"dev-master": "8.5-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "The PHP Unit Testing framework.", "homepage": "https://phpunit.de/", "keywords": ["phpunit", "testing", "xunit"], "time": "2020-06-22T07:06:58+00:00"}, {"name": "rap2hpoutre/laravel-log-viewer", "version": "v1.6.1", "source": {"type": "git", "url": "https://github.com/rap2hpoutre/laravel-log-viewer.git", "reference": "40a6a9e8343673bd34a83419335c7b2496e9a477"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/rap2hpoutre/laravel-log-viewer/zipball/40a6a9e8343673bd34a83419335c7b2496e9a477", "reference": "40a6a9e8343673bd34a83419335c7b2496e9a477", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"illuminate/support": "4.2.*|5.*|^6.0|^7.0", "php": ">=5.4.0"}, "require-dev": {"orchestra/testbench": "3.7.*", "phpunit/phpunit": "^7"}, "type": "laravel-package", "extra": {"laravel": {"providers": ["Rap2hpoutre\\LaravelLogViewer\\LaravelLogViewerServiceProvider"]}}, "autoload": {"classmap": ["src/controllers"], "psr-0": {"Rap2hpoutre\\LaravelLogViewer\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "rap2hpoutre", "email": "rap<PERSON><PERSON><PERSON>@gmail.com"}], "description": "A Laravel log reader", "keywords": ["laravel", "log", "log-reader", "log-viewer", "logging", "lumen"], "time": "2020-07-31T04:58:48+00:00"}, {"name": "scrivo/highlight.php", "version": "v9.18.1.1", "source": {"type": "git", "url": "https://github.com/scrivo/highlight.php.git", "reference": "52fc21c99fd888e33aed4879e55a3646f8d40558"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/scrivo/highlight.php/zipball/52fc21c99fd888e33aed4879e55a3646f8d40558", "reference": "52fc21c99fd888e33aed4879e55a3646f8d40558", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"ext-json": "*", "ext-mbstring": "*", "php": ">=5.4"}, "require-dev": {"phpunit/phpunit": "^4.8|^5.7", "sabberworm/php-css-parser": "^8.3", "symfony/finder": "^2.8|^3.4", "symfony/var-dumper": "^2.8|^3.4"}, "suggest": {"ext-dom": "Needed to make use of the features in the utilities namespace"}, "type": "library", "autoload": {"psr-0": {"Highlight\\": "", "HighlightUtilities\\": ""}, "files": ["HighlightUtilities/functions.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>", "homepage": "http://www.scrivo.org/", "role": "Project Author"}, {"name": "<PERSON>", "homepage": "https://allejo.io", "role": "Maintainer"}, {"name": "<PERSON>", "homepage": "https://twobrain.io", "role": "Contributor"}], "description": "Server side syntax highlighter that supports 185 languages. It's a PHP port of highlight.js", "keywords": ["code", "highlight", "highlight.js", "highlight.php", "syntax"], "time": "2020-03-02T05:59:21+00:00"}, {"name": "sebastian/code-unit-reverse-lookup", "version": "1.0.1", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON><PERSON>/code-unit-reverse-lookup.git", "reference": "4419fcdb5eabb9caa61a27c7a1db532a6b55dd18"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/se<PERSON><PERSON><PERSON><PERSON>/code-unit-reverse-lookup/zipball/4419fcdb5eabb9caa61a27c7a1db532a6b55dd18", "reference": "4419fcdb5eabb9caa61a27c7a1db532a6b55dd18", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": "^5.6 || ^7.0"}, "require-dev": {"phpunit/phpunit": "^5.7 || ^6.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Looks up which function or method a line of code belongs to", "homepage": "https://github.com/sebas<PERSON><PERSON>mann/code-unit-reverse-lookup/", "time": "2017-03-04T06:30:41+00:00"}, {"name": "sebastian/comparator", "version": "3.0.2", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/comparator.git", "reference": "5de4fc177adf9bce8df98d8d141a7559d7ccf6da"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/comparator/zipball/5de4fc177adf9bce8df98d8d141a7559d7ccf6da", "reference": "5de4fc177adf9bce8df98d8d141a7559d7ccf6da", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": "^7.1", "sebastian/diff": "^3.0", "sebastian/exporter": "^3.1"}, "require-dev": {"phpunit/phpunit": "^7.1"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Provides the functionality to compare PHP values for equality", "homepage": "https://github.com/sebas<PERSON><PERSON><PERSON>/comparator", "keywords": ["comparator", "compare", "equality"], "time": "2018-07-12T15:12:46+00:00"}, {"name": "sebastian/diff", "version": "3.0.2", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/diff.git", "reference": "720fcc7e9b5cf384ea68d9d930d480907a0c1a29"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/diff/zipball/720fcc7e9b5cf384ea68d9d930d480907a0c1a29", "reference": "720fcc7e9b5cf384ea68d9d930d480907a0c1a29", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": "^7.1"}, "require-dev": {"phpunit/phpunit": "^7.5 || ^8.0", "symfony/process": "^2 || ^3.3 || ^4"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Diff implementation", "homepage": "https://github.com/sebastian<PERSON>mann/diff", "keywords": ["diff", "udiff", "unidiff", "unified diff"], "time": "2019-02-04T06:01:07+00:00"}, {"name": "sebastian/environment", "version": "4.2.3", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/environment.git", "reference": "464c90d7bdf5ad4e8a6aea15c091fec0603d4368"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebastian<PERSON>mann/environment/zipball/464c90d7bdf5ad4e8a6aea15c091fec0603d4368", "reference": "464c90d7bdf5ad4e8a6aea15c091fec0603d4368", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": "^7.1"}, "require-dev": {"phpunit/phpunit": "^7.5"}, "suggest": {"ext-posix": "*"}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.2-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Provides functionality to handle HHVM/PHP environments", "homepage": "http://www.github.com/sebastianbergmann/environment", "keywords": ["Xdebug", "environment", "hhvm"], "time": "2019-11-20T08:46:58+00:00"}, {"name": "sebastian/exporter", "version": "3.1.2", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/exporter.git", "reference": "68609e1261d215ea5b21b7987539cbfbe156ec3e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebastian<PERSON>mann/exporter/zipball/68609e1261d215ea5b21b7987539cbfbe156ec3e", "reference": "68609e1261d215ea5b21b7987539cbfbe156ec3e", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": "^7.0", "sebastian/recursion-context": "^3.0"}, "require-dev": {"ext-mbstring": "*", "phpunit/phpunit": "^6.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.1.x-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "b<PERSON><PERSON><PERSON>@gmail.com"}], "description": "Provides the functionality to export PHP variables for visualization", "homepage": "http://www.github.com/sebastianbergmann/exporter", "keywords": ["export", "exporter"], "time": "2019-09-14T09:02:43+00:00"}, {"name": "sebastian/global-state", "version": "3.0.0", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/global-state.git", "reference": "edf8a461cf1d4005f19fb0b6b8b95a9f7fa0adc4"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON>mann/global-state/zipball/edf8a461cf1d4005f19fb0b6b8b95a9f7fa0adc4", "reference": "edf8a461cf1d4005f19fb0b6b8b95a9f7fa0adc4", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": "^7.2", "sebastian/object-reflector": "^1.1.1", "sebastian/recursion-context": "^3.0"}, "require-dev": {"ext-dom": "*", "phpunit/phpunit": "^8.0"}, "suggest": {"ext-uopz": "*"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Snapshotting of global state", "homepage": "http://www.github.com/sebastian<PERSON>mann/global-state", "keywords": ["global state"], "time": "2019-02-01T05:30:01+00:00"}, {"name": "sebastian/object-enumerator", "version": "3.0.3", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON><PERSON>/object-enumerator.git", "reference": "7cfd9e65d11ffb5af41198476395774d4c8a84c5"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/object-enumerator/zipball/7cfd9e65d11ffb5af41198476395774d4c8a84c5", "reference": "7cfd9e65d11ffb5af41198476395774d4c8a84c5", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": "^7.0", "sebastian/object-reflector": "^1.1.1", "sebastian/recursion-context": "^3.0"}, "require-dev": {"phpunit/phpunit": "^6.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.0.x-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Traverses array structures and object graphs to enumerate all referenced objects", "homepage": "https://github.com/sebas<PERSON><PERSON>mann/object-enumerator/", "time": "2017-08-03T12:35:26+00:00"}, {"name": "sebastian/object-reflector", "version": "1.1.1", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON>mann/object-reflector.git", "reference": "773f97c67f28de00d397be301821b06708fca0be"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/object-reflector/zipball/773f97c67f28de00d397be301821b06708fca0be", "reference": "773f97c67f28de00d397be301821b06708fca0be", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": "^7.0"}, "require-dev": {"phpunit/phpunit": "^6.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.1-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Allows reflection of object attributes, including inherited and non-public ones", "homepage": "https://github.com/sebastian<PERSON>mann/object-reflector/", "time": "2017-03-29T09:07:27+00:00"}, {"name": "sebastian/recursion-context", "version": "3.0.0", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON>mann/recursion-context.git", "reference": "5b0cd723502bac3b006cbf3dbf7a1e3fcefe4fa8"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/recursion-context/zipball/5b0cd723502bac3b006cbf3dbf7a1e3fcefe4fa8", "reference": "5b0cd723502bac3b006cbf3dbf7a1e3fcefe4fa8", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": "^7.0"}, "require-dev": {"phpunit/phpunit": "^6.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.0.x-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Provides functionality to recursively process PHP variables", "homepage": "http://www.github.com/sebastian<PERSON>mann/recursion-context", "time": "2017-03-03T06:23:57+00:00"}, {"name": "sebastian/resource-operations", "version": "2.0.1", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/resource-operations.git", "reference": "4d7a795d35b889bf80a0cc04e08d77cedfa917a9"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebastian<PERSON>mann/resource-operations/zipball/4d7a795d35b889bf80a0cc04e08d77cedfa917a9", "reference": "4d7a795d35b889bf80a0cc04e08d77cedfa917a9", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": "^7.1"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Provides a list of PHP built-in functions that operate on resources", "homepage": "https://www.github.com/sebastianbergmann/resource-operations", "time": "2018-10-04T04:07:39+00:00"}, {"name": "sebastian/type", "version": "1.1.3", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/type.git", "reference": "3aaaa15fa71d27650d62a948be022fe3b48541a3"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebastian<PERSON>mann/type/zipball/3aaaa15fa71d27650d62a948be022fe3b48541a3", "reference": "3aaaa15fa71d27650d62a948be022fe3b48541a3", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": "^7.2"}, "require-dev": {"phpunit/phpunit": "^8.2"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.1-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Collection of value objects that represent the types of the PHP type system", "homepage": "https://github.com/sebastian<PERSON>mann/type", "time": "2019-07-02T08:10:15+00:00"}, {"name": "sebastian/version", "version": "2.0.1", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/version.git", "reference": "99732be0ddb3361e16ad77b68ba41efc8e979019"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/version/zipball/99732be0ddb3361e16ad77b68ba41efc8e979019", "reference": "99732be0ddb3361e16ad77b68ba41efc8e979019", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=5.6"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Library that helps with managing the version number of Git-hosted PHP projects", "homepage": "https://github.com/sebastian<PERSON>mann/version", "time": "2016-10-03T07:35:21+00:00"}, {"name": "symfony/debug", "version": "v4.4.11", "source": {"type": "git", "url": "https://github.com/symfony/debug.git", "reference": "47aa9064d75db36389692dd4d39895a0820f00f2"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/debug/zipball/47aa9064d75db36389692dd4d39895a0820f00f2", "reference": "47aa9064d75db36389692dd4d39895a0820f00f2", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=7.1.3", "psr/log": "~1.0", "symfony/polyfill-php80": "^1.15"}, "conflict": {"symfony/http-kernel": "<3.4"}, "require-dev": {"symfony/http-kernel": "^3.4|^4.0|^5.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.4-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\Debug\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Debug Component", "homepage": "https://symfony.com", "time": "2020-07-23T08:31:43+00:00"}, {"name": "theseer/tokenizer", "version": "1.2.0", "source": {"type": "git", "url": "https://github.com/theseer/tokenizer.git", "reference": "75a63c33a8577608444246075ea0af0d052e452a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/theseer/tokenizer/zipball/75a63c33a8577608444246075ea0af0d052e452a", "reference": "75a63c33a8577608444246075ea0af0d052e452a", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"ext-dom": "*", "ext-tokenizer": "*", "ext-xmlwriter": "*", "php": "^7.2 || ^8.0"}, "type": "library", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "A small library for converting tokenized PHP source code into XML and potentially other formats", "time": "2020-07-12T23:59:07+00:00"}, {"name": "webmozart/assert", "version": "1.9.1", "source": {"type": "git", "url": "https://github.com/webmozart/assert.git", "reference": "bafc69caeb4d49c39fd0779086c03a3738cbb389"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/webmozart/assert/zipball/bafc69caeb4d49c39fd0779086c03a3738cbb389", "reference": "bafc69caeb4d49c39fd0779086c03a3738cbb389", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": "^5.3.3 || ^7.0 || ^8.0", "symfony/polyfill-ctype": "^1.8"}, "conflict": {"phpstan/phpstan": "<0.12.20", "vimeo/psalm": "<3.9.1"}, "require-dev": {"phpunit/phpunit": "^4.8.36 || ^7.5.13"}, "type": "library", "autoload": {"psr-4": {"Webmozart\\Assert\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "b<PERSON><PERSON><PERSON>@gmail.com"}], "description": "Assertions to validate method input/output with nice error messages.", "keywords": ["assert", "check", "validate"], "time": "2020-07-08T17:02:28+00:00"}], "aliases": [], "minimum-stability": "dev", "stability-flags": {"dcat/laravel-admin": 10}, "prefer-stable": true, "prefer-lowest": false, "platform": {"php": "^7.2.5"}, "platform-dev": {}, "plugin-api-version": "2.6.0"}