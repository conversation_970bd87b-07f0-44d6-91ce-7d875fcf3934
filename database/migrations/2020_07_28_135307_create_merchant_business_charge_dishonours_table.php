<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateMerchantBusinessChargeDishonoursTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('merchant_business_charge_dishonours', function (Blueprint $table) {
            $table->id();
            $table->char('business_id', 13)->comment('BID');
            $table->decimal('dishonour_rate', 15, 2)->comment('拒付率超标值(%)');
            $table->decimal('transaction_rate_plus', 15, 2)->comment('比例手续费加收值(%)');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('merchant_business_charge_dishonours');
    }
}
