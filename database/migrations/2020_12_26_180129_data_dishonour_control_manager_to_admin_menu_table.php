<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class DataDishonourControlManagerToAdminMenuTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
	    if (!is_local()) { // 非本地执行
		    // 拒付调控
		    $risk = Dcat\Admin\Models\Menu::all()->where('parent_id', '0')->where('title', '风险管理')->first();

		    Dcat\Admin\Models\Menu::insert(
			    [
				    [
					    "parent_id"  => $risk->id,
					    "order"      => 3,
					    "title"      => "拒付调控",
					    "icon"       => NULL,
					    "uri"        => "/risk/dishonour_control_tasks",
					    "created_at" => now(),
					    "updated_at" => now()
				    ]
			    ]
		    );
	    }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {

    }
}
