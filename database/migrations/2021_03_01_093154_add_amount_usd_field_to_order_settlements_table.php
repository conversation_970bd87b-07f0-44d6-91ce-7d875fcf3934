<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddAmountUsdFieldToOrderSettlementsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('order_settlements', function (Blueprint $table) {
            $table->decimal('amount_usd', 15, 2)->default(0.00)->comment('USD金额')->after('payment_amount');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('order_settlements', function (Blueprint $table) {
            $table->dropColumn('amount_usd');
        });
    }
}