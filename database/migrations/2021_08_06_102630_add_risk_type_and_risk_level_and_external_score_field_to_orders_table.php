<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddRiskTypeAndRiskLevelAndExternalScoreFieldToOrdersTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('orders', function (Blueprint $table) {
            $table->tinyInteger('risk_type')->comment('风险标志(0:风险模型拦截,1:风控系统拦截,2:不拦截)')->default(2)->after('access_type');
            $table->tinyInteger('risk_level')->comment('风险等级(0:高风险,1:低风险,2:中风险,3:收银台未提交，4:3D未验证)')->default(1)->after('risk_type');
            $table->string('external_score',3)->comment('外部返回评分')->default(0)->after('risk_level');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('orders', function (Blueprint $table) {
            $table->dropColumn('risk_type');
            $table->dropColumn('risk_level');
            $table->dropColumn('external_score');
        });
    }
}
