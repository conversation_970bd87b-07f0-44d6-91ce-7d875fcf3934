<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class DataDirectoryParitiesUprateToAdminMenuTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        if (!is_local()) { // 非本地执行
            // 参数管理>运输商信息
            $directory = Dcat\Admin\Models\Menu::firstWhere('title', '参数管理');

            Dcat\Admin\Models\Menu::insert(
                [
                    [
                        "parent_id"  => $directory->id,
                        "order"      => 15,
                        "title"      => "汇率加价方案",
                        "icon"       => NULL,
                        "uri"        => "/directory_parities_uprates",
                        "created_at" => now(),
                        "updated_at" => now()
                    ]
                ]
            );
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('admin_menu', function (Blueprint $table) {
            //
        });
    }
}
