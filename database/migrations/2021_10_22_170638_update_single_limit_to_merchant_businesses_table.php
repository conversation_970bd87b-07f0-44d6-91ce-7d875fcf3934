<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class UpdateSingleLimitToMerchantBusinessesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('merchant_businesses', function (Blueprint $table) {
            $table->decimal('single_limit',15,2)->default(0.00)->comment('单笔限额')->after('open_whitelist');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('merchant_businesses', function (Blueprint $table) {
            $table->dropColumn('single_limit');
        });
    }
}
