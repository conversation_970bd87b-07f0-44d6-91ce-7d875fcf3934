<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class UpdateDCardTypeIdToDishonourControlsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('dishonour_controls', function (Blueprint $table) {
            $table->string('d_card_type_id',64)->comment('信用卡类型(字典ID)')->change();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('dishonour_controls', function (Blueprint $table) {
            $table->bigInteger('d_card_type_id')->comment('信用卡类型(字典ID)')->change();
        });
    }
}
