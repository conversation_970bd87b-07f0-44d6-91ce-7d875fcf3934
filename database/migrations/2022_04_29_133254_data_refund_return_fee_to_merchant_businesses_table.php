<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class DataRefundReturnFeeToMerchantBusinessesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('merchant_businesses', function (Blueprint $table) {
            $table->string('refund_return_fee', 10)->default(null)->nullable()->comment('退款退费类型')->change();
        });

        \App\Models\MerchantBusiness::whereIn('refund_return_fee', ["0", "1"])->update(['refund_return_fee' => '']);
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('merchant_businesses', function (Blueprint $table) {
            $table->dropColumn('refund_return_fee');
        });
    }
}
