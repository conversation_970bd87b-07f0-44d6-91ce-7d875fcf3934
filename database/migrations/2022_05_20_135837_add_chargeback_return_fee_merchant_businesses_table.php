<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddChargebackReturnFeeMerchantBusinessesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('merchant_businesses', function (Blueprint $table) {
            $table->string('chargeback_return_fee', 12)->nullable()->comment('拒付退费类型(10:单笔处理费;11:比例手续费)')->after('refund_return_fee');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('merchant_businesses', function (Blueprint $table) {
            $table->dropColumn('chargeback_return_fee');
        });
    }
}
