<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddDepositReturnAtOrderSettlementsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('order_settlements', function (Blueprint $table) {
            $table->date('deposit_return_at')->nullable()->comment('保证金退还时间')->after('settle_at');
            $table->date('deposit_expect_return_at')->nullable()->comment('保证金预计退还时间')->after('deposit_return_at');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('order_settlements', function (Blueprint $table) {
            $table->dropColumn('deposit_return_at');
            $table->dropColumn('deposit_expect_return_at');
        });
    }
}
