<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class UpdateDepositReturnAtIndexToOrderSettlementsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('order_settlements', function (Blueprint $table) {
            $table->index('deposit_return_at','order_settlements_deposit_return_at_index');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('order_settlements', function (Blueprint $table) {
            $table->dropIndex('order_settlements_deposit_return_at_index');
        });
    }
}
