<?php

use Illuminate\Database\Migrations\Migration;
use App\Models\DirectoryDictionary;

class DataSettlementAdjustmentRemarksToDirectoryDictionary extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        $typeMap = [
            '开户费' => 'Onboarding Fee',
            '固定保证金' => 'Fixed Reserve',
            '开户费返还' => 'Onboarding Fee Refund',
            '拒付罚金' => 'Chargeback Penalty',
            '固定保证金' => 'Fixed Reserve',
            '年费' => 'Annual Fee',
            '商户处置' => 'Merchant Management',
            '比例手续费' => 'Merchant Discount Rate',
            '单笔处理费' => 'Transaction Fee',
            '退款处理费' => 'Refund Fee',
            '拒付处理费' => 'Chargeback Fee',
            '提现手续费' => 'Wire fee',
            '预拒付处理费' => 'Chargeback Alert Fee',
            '其他' => 'Other',
        ];

        $dictionaries = DirectoryDictionary::where('type', '结算调整类型')->get();

        foreach ($dictionaries as $dictionary) {
            if (isset($typeMap[$dictionary->name])) {
                $dictionary->remarks = $typeMap[$dictionary->name];
                $dictionary->save();
            }
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        DirectoryDictionary::where('type', '结算调整类型')->update(['remarks' => '']);
    }
}
