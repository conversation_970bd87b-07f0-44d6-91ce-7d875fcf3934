<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;
use \Dcat\Admin\Models\Permission;
use \Dcat\Admin\Models\Menu;

class DataDetailMerchantsChargebackRefundCardToAdminPermissionMenu extends Migration
{
    /**
     * 误匹配权限修正.
     *
     * @return void
     */
    public function up()
    {

        // MID结算明细权限
        $detailMerchantPermission = Permission::firstWhere('slug', '/settlement/detail_merchants');

        if (isset($detailMerchantPermission->id)) {
            $detailMerchantPermission->http_path = "/settlement/particular_merchants*";
            $detailMerchantPermission->save();
        }
        //MID结算明细菜单
        $detailMerchantMenu = Menu::firstWhere('title', 'MID结算明细');
        if (isset($detailMerchantMenu->id)) {
            $detailMerchantMenu->uri = "/settlement/particular_merchants";
            $detailMerchantMenu->save();
        }

        //拒付预警
        $chargebackPermission = Permission::firstWhere('slug', '/chargeback_cases');

        if (isset($chargebackPermission->id)) {
            $chargebackPermission->http_path = "/alert_chargeback*";
            $chargebackPermission->save();
        }
        //拒付预警菜单
        $chargebackMenu = Menu::firstWhere('title', '拒付预警');
        if (isset($chargebackMenu->id)) {
            $chargebackMenu->uri = "/alert_chargeback";
            $chargebackMenu->save();
        }

        //退款信息
        $refundsInfoPermission = Permission::firstWhere('slug', 'refunds_info');

        if (isset($refundsInfoPermission->id)) {
            $refundsInfoPermission->http_path = "/info_refunds*";
            $refundsInfoPermission->save();
        }
        //退款信息菜单
        $refundsInfoMenu = Menu::firstWhere('title', '退款信息');
        if (isset($refundsInfoMenu->id)) {
            $refundsInfoMenu->uri = "/info_refunds";
            $refundsInfoMenu->save();
        }

        //卡渠道类
        $cardSupplierPermission = Permission::firstWhere('slug', 'card/supplier_class');

        if (isset($cardSupplierPermission->id)) {
            $cardSupplierPermission->http_path = "/card/bin_supplier_class*";
            $cardSupplierPermission->save();
        }
        //卡渠道类菜单
        $cardSupplierMenu = Menu::firstWhere('title', '卡渠道类');
        if (isset($cardSupplierMenu->id)) {
            $cardSupplierMenu->uri = "/card/bin_supplier_class";
            $cardSupplierMenu->save();
        }

        //卡批次
        $cardVirtualPermission = Permission::firstWhere('slug', 'card_virtual_batch');

        if (isset($cardVirtualPermission->id)) {
            $cardVirtualPermission->http_path = "/card_batch_virtual*";
            $cardVirtualPermission->save();
        }
        //卡批次菜单
        $cardVirtualMenu = Menu::firstWhere('title', '卡批次');
        if (isset($cardVirtualMenu->id)) {
            $cardVirtualMenu->uri = "/card_batch_virtual";
            $cardVirtualMenu->save();
        }

        $permissions = DB::table('admin_permissions')->select('id', 'http_path')->get();
        $menus       = DB::table('admin_menu')->select('id', 'uri', 'parent_id')->get()->keyBy('uri');
        $sonMenuData  = [];
        $maiMenuData  = [];

        foreach ($permissions as $permission) {
            if (!empty($permission->http_path)) {
                $menusUri = rtrim($permission->http_path, '*');
                $oneUri   = trim($menusUri, '/');
                $twoUri   = '/' . $menusUri;

                if (isset($menus[$menusUri])) {
                    $this->insertData($permission, $menus[$menusUri], $maiMenuData, $sonMenuData);
                }
                if (isset($menus[$oneUri])) {
                    $this->insertData($permission, $menus[$oneUri], $maiMenuData, $sonMenuData);
                }
                if (isset($menus[$twoUri])) {
                    $this->insertData($permission, $menus[$twoUri], $maiMenuData, $sonMenuData);
                }
            }
        }

        if (count($sonMenuData)) {
            DB::table('admin_permission_menu')->insertOrIgnore($sonMenuData);
        }

        if (count($maiMenuData)) {
            DB::table('admin_permission_menu')->insertOrIgnore($maiMenuData);
        }

        //清空角色绑定菜单
        DB::table('admin_role_menu')->truncate();
    }

    private function insertData($permission, $menus, &$maiMenuData, &$sonMenuData): void
    {
        if ($menus->parent_id !== 0) {
            $maiMenuData[] = [
                'permission_id' => $permission->id,
                'menu_id'       => $menus->parent_id,
            ];
        }
        $sonMenuData[] = [
            'permission_id' => $permission->id,
            'menu_id'       => $menus->id,
        ];
    }
}
