<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use App\Models\MerchantPermission;
use App\Models\MerchantMenu;

class DataChargebackRefundCardToMerchantPermissionMenu extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        //拒付预警
        $chargebackPermission = MerchantPermission::firstWhere('slug', 'chargeback_cases');

        if (isset($chargebackPermission->id)) {
            $chargebackPermission->http_path = "/alert_chargeback*";
            $chargebackPermission->save();
        }
        //拒付预警菜单
        $chargebackMenu = MerchantMenu::firstWhere('title', '拒付预警');
        if (isset($chargebackMenu->id)) {
            $chargebackMenu->uri = "/alert_chargeback";
            $chargebackMenu->save();
        }

        //退款信息
        $refundsInfoPermission = MerchantPermission::firstWhere('slug', 'refunds_info');

        if (isset($refundsInfoPermission->id)) {
            $refundsInfoPermission->http_path = "/info_refunds*";
            $refundsInfoPermission->save();
        }
        //退款信息菜单
        $refundsInfoMenu = MerchantMenu::firstWhere('title', '退款信息');
        if (isset($refundsInfoMenu->id)) {
            $refundsInfoMenu->uri = "/info_refunds";
            $refundsInfoMenu->save();
        }

        //卡申请管理菜单
        $cardVirtualMenu = MerchantMenu::firstWhere('title', '卡申请管理');
        if (isset($cardVirtualMenu->id)) {
            $cardVirtualMenu->uri = "/card_batch_virtual";
            $cardVirtualMenu->save();
        }
    }
}
