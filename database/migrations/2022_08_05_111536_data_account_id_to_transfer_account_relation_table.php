<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;
use App\Models\TransferTicket;

class DataAccountIdToTransferAccountRelationTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        //提现信息表
        TransferTicket::query()->select('id', 'transfer_account_id')->chunkById(1000, static function ($transfers) {
            $data = [];
            foreach ($transfers as $transfer) {
                $data[] = [
                    'ticket_id' => $transfer->id,
                    'transfer_account_id' => $transfer->transfer_account_id,
                ];
            }
            DB::table('transfer_account_relation')->insertOrIgnore($data);
        });
    }
}
