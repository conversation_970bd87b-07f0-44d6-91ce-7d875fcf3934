<?php

use App\Models\DirectoryCc;
use App\Models\MerchantBusiness;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class UpdateWhitelistCardTypeToMerchantBusinessesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        //本地不执行
        if (!is_local()) {
            //开启白名单验证whitelist_card_type更新为V,M 
            MerchantBusiness::where('open_whitelist', MerchantBusiness::WHITE_LIST_OPEN)->update(['whitelist_card_type' => 'V,M']);
            
            //信用卡卡种配置初始化
            DirectoryCc::whereIn('cc_type', ['V', 'M', 'J', 'A', 'D'])->update(['is_risk_control' => DirectoryCc::IS_RISK_CONTROL_OPEN]);
        }
    }
}
