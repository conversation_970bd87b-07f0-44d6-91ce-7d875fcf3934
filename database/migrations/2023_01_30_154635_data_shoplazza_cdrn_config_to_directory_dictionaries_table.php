<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\DB;

class DataShoplazzaCdrnConfigToDirectoryDictionariesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
       //本地不执行
       if (!is_local()) {
            DB::table('directory_dictionaries')->insertOrIgnore([
                ['type' => '店匠配置', 'name' => 'client_id', 'remarks' => '4cohV6Ve0PnKw4BNX5tghYYcDlww1TkxVltftPK6Eko'],
                ['type' => '店匠配置', 'name' => 'client_secret', 'remarks' => '5JWtI_rlaMmKXzz7tiIn6WDCpWKvPFtsaCgbhpjfzuQ'],
                ['type' => '店匠配置', 'name' => 'redirect_uri', 'remarks' => 'redirect'],
                ['type' => '店匠配置', 'name' => 'scope', 'remarks' => 'read_product write_product read_order write_order read_customer write_customer write_payment_info read_payment_info'],
                ['type' => '店匠配置', 'name' => 'auth_jump_uri', 'remarks' => '/admin/smart_apps/coral/payment/providers/51121'],
                ['type' => 'CDRN配置', 'name' => 'partnerId', 'remarks' => '10717'],
                ['type' => 'CDRN配置', 'name' => 'secretKey', 'remarks' => 'wXq6x1B8bZLZK5zs6lnP5flwTTtEO15V'],
                ['type' => 'CDRN配置', 'name' => 'version', 'remarks' => '1.5'],
                ['type' => 'CDRN配置', 'name' => 'url', 'remarks' => 'https://verifiapi.visa.com'],
            ]);
       } 
    }
    
    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        DB::table('directory_dictionaries')->where('type', '店匠配置')->delete();
        DB::table('directory_dictionaries')->where('type', 'CDRN配置')->delete();
    }
}
