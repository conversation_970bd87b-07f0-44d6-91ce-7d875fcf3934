<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddIsNotDeliveryToMerchantBusinessHistoriesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('merchant_business_histories', function (Blueprint $table) {
            $table->tinyInteger('is_not_delivery')->default(0)->comment('是否无运单结算(0:有运单结算;1:无运单结算)')->after('chargeback_return_fee');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('merchant_business_histories', function (Blueprint $table) {
            $table->dropColumn('is_not_delivery');
        });
    }
}
