<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\DB;

class DataWhiteListConfigToDirectoryDictionariesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        //本地不执行
        if (!is_local()) {
            DB::table('directory_dictionaries')->insertOrIgnore([
                ['type' => '白名单接口配置', 'name' => 'API_ID', 'remarks' => '0352bf201b8b726f'],
                ['type' => '白名单接口配置', 'name' => 'API_KEY', 'remarks' => '01c935aed2b8f28101b97b84b729966f'],
                ['type' => '白名单接口配置', 'name' => 'PASS_IP', 'remarks' => '']
            ]);
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        DB::table('directory_dictionaries')->where('type', '白名单接口配置')->delete();
    }
}
