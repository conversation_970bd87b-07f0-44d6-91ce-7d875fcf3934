<?php

use App\Models\MerchantMenu;
use App\Models\MerchantPermission;
use Illuminate\Database\Migrations\Migration;

class DataOrderRiskTransactionsToMerchantMenusTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        //本地不执行
        if (!is_local()) {
            $menu_parent_id = MerchantMenu::where('title', '交易管理')->value('id');

            MerchantMenu::insertGetId([
                "parent_id"  => $menu_parent_id,
                "order"      => 9,
                "title"      => "风险交易",
                "icon"       => NULL,
                "uri"        => "/order_risk_transaction",
                "created_at" => now(),
                "updated_at" => now()
            ]);

            $permission_parent_id = MerchantPermission::where('slug', 'jygl')->value('id');

            MerchantPermission::insertGetId(
                [
                    "name"        => "风险交易",
                    "slug"        => "order_risk_transaction",
                    "http_method" => "",
                    "http_path"   => "/order_risk_transaction*",
                    "order"       => 16,
                    "parent_id"   => $permission_parent_id,
                    "created_at"  => now(),
                    "updated_at"  => now()
                ]
            );
        }
    }
}
