<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\DB;

class DataShellStandApiConfigToDirectoryDictionariesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        //本地不执行
        if (!is_local()) {
            DB::table('directory_dictionaries')->insertOrIgnore([
                ['type' => '壳站接口配置', 'name' => 'API_ID', 'remarks' => 'wxa9vjjcxyjkw7ro'],
                ['type' => '壳站接口配置', 'name' => 'API_KEY', 'remarks' => 'j4h5r2ct5ucaki17ym7x4h4yhzhcsbvk'],
            ]);
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        DB::table('directory_dictionaries')->where('type', '壳站接口配置')->delete();
    }
    
}
