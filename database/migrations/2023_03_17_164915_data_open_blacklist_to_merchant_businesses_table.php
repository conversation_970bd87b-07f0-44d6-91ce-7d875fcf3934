<?php

use App\Models\MerchantBusiness;
use Illuminate\Database\Migrations\Migration;

class DataOpenBlacklistToMerchantBusinessesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        //本地不执行
        if (!is_local()) {
            MerchantBusiness::where('open_blacklist', MerchantBusiness::BLACKLIST_LIST_CLOSE)
                ->update(['open_blacklist' => MerchantBusiness::BLACKLIST_LIST_OPEN]);
        }
    }
}
