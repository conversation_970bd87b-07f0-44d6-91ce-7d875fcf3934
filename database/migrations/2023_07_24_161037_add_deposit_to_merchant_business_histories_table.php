<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddDepositToMerchantBusinessHistoriesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('merchant_business_histories', function (Blueprint $table) {
            $table->json('deposit')->after('deposit_cycle')->nullable()->comment('保证金配置');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('merchant_business_histories', function (Blueprint $table) {
            $table->dropColumn('deposit');
        });
    }
}
