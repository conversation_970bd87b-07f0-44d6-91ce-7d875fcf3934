<?php

use App\Models\MerchantBusiness;
use App\Models\MerchantBusinessHistory;
use Illuminate\Database\Migrations\Migration;

class DataDepositToMerchantBusinessesTable extends Migration
{
    public function up()
    {
        //本地不执行
        if (!is_local()) {
            $merchantBusinessArr = MerchantBusiness::whereNotNull('deposit_type')->get();
            if (!empty($merchantBusinessArr)) {
                foreach ($merchantBusinessArr as $merchantBusiness) {
                    $update = [
                        'deposit' => [
                            [
                                'deposit_type'  => $merchantBusiness->deposit_type,
                                'deposit_card'  => '*',
                            ]
                        ]
                    ];
                    switch ($merchantBusiness->deposit_type) {
                        case MerchantBusiness::DEPOSIT_TYPE_FIXTION:
                            $update['deposit'][0]['deposit_value'] = $merchantBusiness->deposit_amount;
                            $update['deposit'][0]['deposit_cycle'] = '0';
                            break;
                        case MerchantBusiness::DEPOSIT_TYPE_ACTIVITY:
                            $update['deposit'][0]['deposit_value'] = $merchantBusiness->deposit_rate;
                            $update['deposit'][0]['deposit_cycle'] = $merchantBusiness->deposit_cycle;
                            break;
                    }
                    
                    MerchantBusiness::where('business_id', $merchantBusiness->business_id)->update($update);
                }
            }
            
            $merchantBusinessHistoriesArr = MerchantBusinessHistory::whereNotNull('deposit_type')->get();
            if (!empty($merchantBusinessHistoriesArr)) {
                foreach ($merchantBusinessHistoriesArr as $merchantBusinessHistories) {
                    $update = [
                        'deposit' => [
                            [
                                'deposit_type'  => $merchantBusinessHistories->deposit_type,
                                'deposit_card'  => '*',
                            ] 
                        ]
                    ];
                    switch ($merchantBusinessHistories->deposit_type) {
                        case MerchantBusinessHistory::DEPOSIT_TYPE_FIXTION:
                            $update['deposit'][0]['deposit_value'] = $merchantBusinessHistories->deposit_amount;
                            $update['deposit'][0]['deposit_cycle'] = '0';
                            break;
                        case MerchantBusinessHistory::DEPOSIT_TYPE_ACTIVITY:
                            $update['deposit'][0]['deposit_value'] = $merchantBusinessHistories->deposit_rate;
                            $update['deposit'][0]['deposit_cycle'] = $merchantBusinessHistories->deposit_cycle;
                            break;
                    }
                    
                    MerchantBusinessHistory::where('id', $merchantBusinessHistories->id)->update($update);
                }
            }
        }
    }
}
