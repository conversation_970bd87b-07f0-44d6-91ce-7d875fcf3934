<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddStatOrdersMerchantIdDateIndexToStatOrdersTable extends Migration
{
	/**
	 * Run the migrations.
	 *
	 * @return void
	 */
	public function up()
	{
		// 运行迁移时会被调用 => 新增一个组合索引
		Schema::table('stat_orders', function (Blueprint $table) {
			$table->index(['merchant_id', 'date_stat', 'date_stat_hour'], "stat_orders_merchant_id_date_index");
			$table->dropIndex("stat_orders_merchant_id_index");
		});
	}

	/**
	 * Reverse the migrations.
	 *
	 * @return void
	 */
	public function down()
	{
		// 回滚迁移时会被调用
		Schema::table('stat_orders', function (Blueprint $table) {
			$table->dropIndex('stat_orders_merchant_id_date_index');
			$table->index('merchant_id');
		});
	}
}
