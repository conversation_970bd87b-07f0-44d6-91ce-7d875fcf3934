<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddOrdersStatusTypeCompleteIndexToOrdersTable extends Migration
{
	/**
	 * Run the migrations.
	 *
	 * @return void
	 */
	public function up()
	{
		// 运行迁移时会被调用 => 新增一个组合索引
		Schema::table('orders', function (Blueprint $table) {
			$table->index(['status', 'type', 'completed_at'], "orders_status_type_completed_at_index");
		});
	}

	/**
	 * Reverse the migrations.
	 *
	 * @return void
	 */
	public function down()
	{
		// 回滚迁移时会被调用
		Schema::table('orders', function (Blueprint $table) {
			$table->dropIndex('orders_status_type_completed_at_index');
		});
	}
}
