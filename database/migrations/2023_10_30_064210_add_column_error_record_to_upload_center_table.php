<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddColumnErrorRecordToUploadCenterTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('upload_centers', function (Blueprint $table) {
			$table->text('error_record')->nullable()->after('import_identity')->comment('错误记录');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('upload_centers', function (Blueprint $table) {
			$table->dropColumn('error_record');
        });
    }
}
