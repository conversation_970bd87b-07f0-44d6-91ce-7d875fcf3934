<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\DB;

class DataRiskReplyToDirectoryDictionariesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        if (!is_local()) {
            $insertData = [
                ['type' => '预警平台处理码', 'name' => 'a010', 'remarks' => '成功处理并退款'],
                ['type' => '预警平台处理码', 'name' => 'a011', 'remarks' => '在收到预警前已经退款'],
                ['type' => '预警平台处理码', 'name' => 'a012', 'remarks' => '原始交易状态为失败'],
                ['type' => '预警平台处理码', 'name' => 'a013', 'remarks' => '在收到预警前已拒付'],
                ['type' => '预警平台处理码', 'name' => 'a014', 'remarks' => '没有找到对应的交易'],
                ['type' => '预警平台处理码', 'name' => 'a015', 'remarks' => '已匹配，不处理'],
                ['type' => '预警平台处理码', 'name' => 'a016', 'remarks' => '成功处理并退款'],
                ['type' => '预警平台处理码', 'name' => 'a017', 'remarks' => '在收到预警前已经退款'],
                ['type' => '预警平台处理码', 'name' => 'a018', 'remarks' => '原始交易状态为失败'],
                ['type' => '预警平台处理码', 'name' => 'a019', 'remarks' => '已匹配，不处理'],
                ['type' => '预警平台处理码', 'name' => 'a020', 'remarks' => '其他'],
                ['type' => '预警平台处理码', 'name' => 'a021', 'remarks' => '重复警报']
            ];

            DB::table('directory_dictionaries')->insert($insertData);
        }
    }
}
