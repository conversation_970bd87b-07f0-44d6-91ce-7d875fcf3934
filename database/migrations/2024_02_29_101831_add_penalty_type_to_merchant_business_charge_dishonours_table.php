<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddPenaltyTypeToMerchantBusinessChargeDishonoursTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('merchant_business_charge_dishonours', function (Blueprint $table) {
            $table->tinyInteger('type')->default(0)->comment('罚金种类：0-拒付罚金 1-拒付罚金*')->after('dishonour_rate');
            $table->tinyInteger('penalty_type')->default(0)->comment('罚金类型：0-固定 1-比例')->after('type');
            $table->decimal('penalty_value', 15, 2)->comment('固定/比例罚金')->after('penalty_type');
            $table->tinyInteger('penalty_status')->default(1)->comment('启用状态：0-不启用 1-启用')->after('penalty_value');
            $table->dropColumn('transaction_rate_plus');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('merchant_business_charge_dishonours', function (Blueprint $table) {
            $table->dropColumn(['penalty_type', 'penalty_value', 'penalty_status']);
            $table->decimal('transaction_rate_plus', 15, 2)->comment('比例手续费加收值(%)');
        });
    }
}
