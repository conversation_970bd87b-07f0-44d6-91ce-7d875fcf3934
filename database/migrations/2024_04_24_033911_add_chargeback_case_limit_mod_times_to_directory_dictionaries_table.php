<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

class AddChargebackCaseLimitModTimesToDirectoryDictionariesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
		if (!is_local()) {
			DB::table('directory_dictionaries')->insert([
				'type'    => '单笔人工预警修改次数限制',
				'name'    => 'chargeback_case_mod_limit',
				'status'  => 1,
				'sort'    => 41,
				'remarks' => '2'
			]);
		}
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
		if (!is_local()) {
			DB::table('directory_dictionaries')->where('type', '单笔人工预警修改次数限制')->where('name', 'chargeback_case_mod_limit')->delete();
		}
    }
}
