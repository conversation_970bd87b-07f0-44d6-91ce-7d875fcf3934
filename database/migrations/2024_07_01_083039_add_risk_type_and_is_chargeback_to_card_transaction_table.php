<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddRiskTypeAndIsChargebackToCardTransactionTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('card_transactions', function (Blueprint $table) {
            $table->tinyInteger('risk_type')->comment('风控类型：0：正常交易 1: 欺诈交易 2：黑名单交易')->default(0)->after('settle_currency');
            $table->tinyInteger('is_chargeback')->comment('是否拒付：0：未拒付 1: 已拒付')->default(0)->after('risk_type');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('card_transactions', function (Blueprint $table) {
            $table->dropColumn('risk_type');
            $table->dropColumn('is_chargeback');
        });
    }
}
