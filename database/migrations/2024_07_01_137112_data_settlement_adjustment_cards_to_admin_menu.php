<?php

use Dcat\Admin\Models\Menu;
use Dcat\Admin\Models\Permission;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\DB;

class DataSettlementAdjustmentCardsToAdminMenu extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        //本地不执行
        if (!is_local()) {
            $menu_parent_id = Menu::where('title', '虚拟卡管理')->value('id');

            $menu_id = Menu::insertGetId([
                'parent_id'  => $menu_parent_id,
                'order'      => 5,
                'title'      => 'CID结算调整',
                'icon'       => NULL,
                'uri'        => '/settle_adjustment_cards',
                'created_at' => now(),
                'updated_at' => now()
            ]);

            $permission_parent_id = Permission::where('slug', 'xnkgl')->value('id');

            $permission_id = Permission::insertGetId(
                [
                    'name'        => 'CID结算调整',
                    'slug'        => 'settle_adjustment_cards',
                    'http_method' => '',
                    'http_path'   => '/settle_adjustment_cards*',
                    'order'       => 5,
                    'parent_id'   => $permission_parent_id,
                    'created_at'  => now(),
                    'updated_at'  => now()
                ]
            );

            //权限和菜单绑定
            DB::table('admin_permission_menu')->insertOrIgnore([
                [
                    'permission_id' => $permission_id,
                    'menu_id'       => $menu_id,
                ],
                [
                    'permission_id' => $permission_id,
                    'menu_id'       => $menu_parent_id,
                ]
            ]);
        }
    }
}
