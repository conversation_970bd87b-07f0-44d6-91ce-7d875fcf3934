<?php

use App\Models\MerchantPermission;
use Illuminate\Database\Migrations\Migration;

class AddReplenishPermissionToMerchantPermissionTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        if (!is_local()) {
            $settlePermissionId = MerchantPermission::where('name', '结算管理')->value('id');
            MerchantPermission::insert(
                [
                    "name"        => "账户充值",
                    "slug"        => "/settlement/refill",
                    "http_method" => "",
                    "http_path"   => "/settlement/refill*",
                    "order"       => 26,
                    "parent_id"   => $settlePermissionId,
                    "created_at"  => now(),
                    "updated_at"  => now()
                ]
            );

            $systemId = MerchantPermission::where('name', '系统设定')->value('id');
            MerchantPermission::insert(
                [
                    "name"        => "API信息",
                    "slug"        => "/apis",
                    "http_method" => "",
                    "http_path"   => "/apis*",
                    "order"       => 26,
                    "parent_id"   => $systemId,
                    "created_at"  => now(),
                    "updated_at"  => now()
                ]
            );


            $cardsId = MerchantPermission::insertGetId(
                [
                    "name"        => "CID管理",
                    "slug"        => "cidgl",
                    "http_method" => "",
                    "http_path"   => "",
                    "order"       => 10,
                    "parent_id"   => 0,
                    "created_at"  => now(),
                    "updated_at"  => now()
                ]
            );
            MerchantPermission::insert(
                [
                    "name"        => "CID信息",
                    "slug"        => "/cards/cards",
                    "http_method" => "",
                    "http_path"   => "/cards/cards*",
                    "order"       => 26,
                    "parent_id"   => $cardsId,
                    "created_at"  => now(),
                    "updated_at"  => now()
                ]
            );
            MerchantPermission::insert(
                [
                    "name"        => "CID充值工单",
                    "slug"        => "/cards/tickets",
                    "http_method" => "",
                    "http_path"   => "/cards/tickets*",
                    "order"       => 26,
                    "parent_id"   => $cardsId,
                    "created_at"  => now(),
                    "updated_at"  => now()
                ]
            );
            MerchantPermission::insert(
                [
                    "name"        => "CID结算明细",
                    "slug"        => "/cards/settle_details",
                    "http_method" => "",
                    "http_path"   => "/cards/settle_details*",
                    "order"       => 26,
                    "parent_id"   => $cardsId,
                    "created_at"  => now(),
                    "updated_at"  => now()
                ]
            );
            MerchantPermission::insert(
                [
                    "name"        => "CID交易统计",
                    "slug"        => "/cards/trade_stat",
                    "http_method" => "",
                    "http_path"   => "/cards/trade_stat*",
                    "order"       => 26,
                    "parent_id"   => $cardsId,
                    "created_at"  => now(),
                    "updated_at"  => now()
                ]
            );
        }
    }
}
