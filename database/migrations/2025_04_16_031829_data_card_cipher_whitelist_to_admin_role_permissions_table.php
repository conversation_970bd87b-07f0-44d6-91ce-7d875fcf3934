<?php

use Dcat\Admin\Models\Permission;
use Dcat\Admin\Models\Role;
use Illuminate\Database\Migrations\Migration;

class DataCardCipherWhitelistToAdminRolePermissionsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        if (!is_local()){
            $cidResourcePerm = Role::where('name', 'Risk Control Operation')->value('id');
            $permissionId = Permission::query()->where('slug', '/card/card_cipher_whitelist')->value('id');

            DB::table('admin_role_permissions')->insert(
                [
                    [
                        'role_id'       => $cidResourcePerm,
                        'permission_id' => $permissionId,
                        'created_at'    => now(),
                        'updated_at'    => now()
                    ],
                ]);
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        // 删除权限和菜单绑定
        $cidResourcePerm = Role::where('name', 'Risk Control Operation')->value('id');
        $permissionId = Permission::query()->where('slug', '/card/card_cipher_whitelist')->value('id');

        DB::table('admin_role_permissions')->where('role_id', $cidResourcePerm)->where('permission_id', $permissionId)->delete();
    }
}
