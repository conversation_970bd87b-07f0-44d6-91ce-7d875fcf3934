<?php

use Illuminate\Database\Seeder;

class DirectoryCarriersTableSeeder extends Seeder
{

    /**
     * Auto generated seed file
     *
     * @return void
     */
    public function run()
    {
        

        \DB::table('directory_carriers')->delete();
        
        \DB::table('directory_carriers')->insert(array (
            0 => 
            array (
                'id' => 1,
                'name' => 'DHL',
                'code' => 'dhl',
                'code_num' => '100001',
                'country' => '',
                'country_code' => '0000',
                'api_type' => 0,
                'sort' => 0,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            1 => 
            array (
                'id' => 2,
                'name' => 'UPS',
                'code' => 'ups',
                'code_num' => '100002',
                'country' => '',
                'country_code' => '0000',
                'api_type' => 0,
                'sort' => 10,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            2 => 
            array (
                'id' => 3,
                'name' => 'Fedex',
                'code' => 'fedex',
                'code_num' => '100003',
                'country' => '',
                'country_code' => '0000',
                'api_type' => 0,
                'sort' => 20,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            3 => 
            array (
                'id' => 4,
                'name' => 'TNT',
                'code' => 'tnt',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 30,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            4 => 
            array (
                'id' => 5,
                'name' => 'China EMS',
                'code' => 'china-ems',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 40,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            5 => 
            array (
                'id' => 6,
                'name' => 'China Post',
                'code' => 'china-post',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 50,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            6 => 
            array (
                'id' => 7,
                'name' => 'Singapore Post',
                'code' => 'singapore-post',
                'code_num' => '19131',
                'country' => 'Singapore',
                'country_code' => '1913',
                'api_type' => 0,
                'sort' => 60,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            7 => 
            array (
                'id' => 8,
                'name' => 'Singapore Speedpost',
                'code' => 'singapore-speedpost',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 70,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            8 => 
            array (
                'id' => 9,
                'name' => 'Hong Kong Post',
                'code' => 'hong-kong-post',
                'code_num' => '08011',
                'country' => 'Hong Kong [CN]',
                'country_code' => '0801',
                'api_type' => 0,
                'sort' => 80,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            9 => 
            array (
                'id' => 10,
                'name' => 'Swiss Post',
                'code' => 'swiss-post',
                'code_num' => '19251',
                'country' => 'Switzerland',
                'country_code' => '1925',
                'api_type' => 0,
                'sort' => 90,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            10 => 
            array (
                'id' => 11,
                'name' => 'USPS',
                'code' => 'usps',
                'code_num' => '21051',
                'country' => 'United States',
                'country_code' => '2105',
                'api_type' => 0,
                'sort' => 100,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            11 => 
            array (
                'id' => 12,
                'name' => 'United Kingdom Royal Mail',
                'code' => 'royal-mail',
                'code_num' => '11031',
                'country' => 'United Kingdom',
                'country_code' => '1103',
                'api_type' => 1,
                'sort' => 110,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            12 => 
            array (
                'id' => 13,
                'name' => 'Parcel Force',
                'code' => 'parcel-force',
                'code_num' => '11033',
                'country' => 'United Kingdom',
                'country_code' => '1103',
                'api_type' => 0,
                'sort' => 110,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            13 => 
            array (
                'id' => 14,
                'name' => 'Netherlands Post - PostNL',
                'code' => 'postnl-parcels',
                'code_num' => '14044',
                'country' => 'Netherlands',
                'country_code' => '1404',
                'api_type' => 0,
                'sort' => 120,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            14 => 
            array (
                'id' => 15,
                'name' => 'Netherlands Post',
                'code' => 'netherlands-post',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 130,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            15 => 
            array (
                'id' => 16,
                'name' => 'Australia Post',
                'code' => 'australia-post',
                'code_num' => '01151',
                'country' => 'Australia',
                'country_code' => '0115',
                'api_type' => 0,
                'sort' => 140,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            16 => 
            array (
                'id' => 17,
                'name' => 'Australia EMS',
                'code' => 'australia-ems',
                'code_num' => '01151',
                'country' => 'Australia',
                'country_code' => '0115',
                'api_type' => 0,
                'sort' => 150,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            17 => 
            array (
                'id' => 18,
                'name' => 'Canada Post',
                'code' => 'canada-post',
                'code_num' => '03041',
                'country' => 'Canada',
                'country_code' => '0304',
                'api_type' => 0,
                'sort' => 160,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            18 => 
            array (
                'id' => 19,
                'name' => 'New Zealand Post',
                'code' => 'new-zealand-post',
                'code_num' => '14061',
                'country' => 'New Zealand',
                'country_code' => '1406',
                'api_type' => 0,
                'sort' => 170,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            19 => 
            array (
                'id' => 20,
                'name' => 'Bpost',
                'code' => 'belgium-post',
                'code_num' => '02063',
                'country' => 'Belgium',
                'country_code' => '0206',
                'api_type' => 0,
                'sort' => 180,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            20 => 
            array (
                'id' => 21,
                'name' => 'Brazil Correios',
                'code' => 'brazil-correios',
                'code_num' => '02151',
                'country' => 'Brazil',
                'country_code' => '0215',
                'api_type' => 0,
                'sort' => 190,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            21 => 
            array (
                'id' => 22,
                'name' => 'Russian Post',
                'code' => 'russian-post',
                'code_num' => '18031',
                'country' => 'Russian Federation',
                'country_code' => '1803',
                'api_type' => 0,
                'sort' => 200,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            22 => 
            array (
                'id' => 23,
                'name' => 'Sweden Posten',
                'code' => 'sweden-posten',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 210,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            23 => 
            array (
                'id' => 24,
                'name' => 'La Poste',
                'code' => 'laposte',
                'code_num' => '06051',
                'country' => 'France',
                'country_code' => '0605',
                'api_type' => 0,
                'sort' => 220,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            24 => 
            array (
                'id' => 25,
                'name' => 'France EMS - Chronopost',
                'code' => 'chronopost',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 230,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            25 => 
            array (
                'id' => 26,
                'name' => 'Colissimo',
                'code' => 'colissimo',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 240,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            26 => 
            array (
                'id' => 27,
                'name' => 'Poste Italiane',
                'code' => 'poste-italiane',
                'code_num' => '09071',
                'country' => 'Italy',
                'country_code' => '0907',
                'api_type' => 0,
                'sort' => 250,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            27 => 
            array (
                'id' => 28,
                'name' => 'India Post',
                'code' => 'india-post',
                'code_num' => '09021',
                'country' => 'India',
                'country_code' => '0902',
                'api_type' => 0,
                'sort' => 260,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            28 => 
            array (
                'id' => 29,
                'name' => 'Magyar Posta',
                'code' => 'magyar-posta',
                'code_num' => '08051',
                'country' => 'Hungary',
                'country_code' => '0805',
                'api_type' => 0,
                'sort' => 270,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            29 => 
            array (
                'id' => 30,
                'name' => 'YANWEN',
                'code' => 'yanwen',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 280,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            30 => 
            array (
                'id' => 31,
                'name' => 'Deutsche Post DHL',
                'code' => 'dhl-germany',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 290,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            31 => 
            array (
                'id' => 32,
                'name' => 'An Post',
                'code' => 'an-post',
                'code_num' => '09051',
                'country' => 'Ireland',
                'country_code' => '0905',
                'api_type' => 0,
                'sort' => 300,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            32 => 
            array (
                'id' => 33,
                'name' => 'DHL Parcel Netherlands',
                'code' => 'dhlparcel-nl',
                'code_num' => '100047',
                'country' => '',
                'country_code' => '0000',
                'api_type' => 0,
                'sort' => 310,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            33 => 
            array (
                'id' => 34,
                'name' => 'DHL Poland Domestic',
                'code' => 'dhl-poland',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 320,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            34 => 
            array (
                'id' => 35,
                'name' => 'DHL Spain Domestic',
                'code' => 'dhl-es',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 330,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            35 => 
            array (
                'id' => 36,
                'name' => 'Mexico Post',
                'code' => 'correos-mexico',
                'code_num' => '13141',
                'country' => 'Mexico',
                'country_code' => '1314',
                'api_type' => 0,
                'sort' => 340,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            36 => 
            array (
                'id' => 37,
                'name' => 'Posten Norge',
                'code' => 'posten-norge',
                'code_num' => '14081',
                'country' => 'Norway',
                'country_code' => '1408',
                'api_type' => 0,
                'sort' => 350,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            37 => 
            array (
                'id' => 38,
                'name' => 'TNT Italy',
                'code' => 'tnt-it',
                'code_num' => '100065',
                'country' => '',
                'country_code' => '0000',
                'api_type' => 0,
                'sort' => 360,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            38 => 
            array (
                'id' => 39,
                'name' => 'TNT France',
                'code' => 'tnt-fr',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 370,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            39 => 
            array (
                'id' => 40,
                'name' => 'Portugal Post - CTT',
                'code' => 'ctt',
                'code_num' => '16101',
                'country' => 'Portugal',
                'country_code' => '1610',
                'api_type' => 0,
                'sort' => 380,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            40 => 
            array (
                'id' => 41,
                'name' => 'South African Post Office',
                'code' => 'south-africa-post',
                'code_num' => '19171',
                'country' => 'South Africa',
                'country_code' => '1917',
                'api_type' => 0,
                'sort' => 390,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            41 => 
            array (
                'id' => 42,
                'name' => 'Correos',
                'code' => 'correos-spain',
                'code_num' => '19181',
                'country' => 'Spain',
                'country_code' => '1918',
                'api_type' => 0,
                'sort' => 400,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            42 => 
            array (
                'id' => 43,
                'name' => 'Chunghwa POST',
                'code' => 'taiwan-post',
                'code_num' => '20011',
                'country' => 'Taiwan [CN]',
                'country_code' => '2001',
                'api_type' => 1,
                'sort' => 410,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            43 => 
            array (
                'id' => 44,
                'name' => 'Ukraine Post',
                'code' => 'ukraine-post',
                'code_num' => '21021',
                'country' => 'Ukraine',
                'country_code' => '2102',
                'api_type' => 0,
                'sort' => 420,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            44 => 
            array (
                'id' => 45,
                'name' => 'Ukraine EMS',
                'code' => 'ukraine-ems',
                'code_num' => '21023',
                'country' => 'Ukraine',
                'country_code' => '2102',
                'api_type' => 0,
                'sort' => 430,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            45 => 
            array (
                'id' => 46,
                'name' => 'Emirates Post',
                'code' => 'emirates-post',
                'code_num' => '05031',
                'country' => 'United Arab Emirates',
                'country_code' => '0503',
                'api_type' => 0,
                'sort' => 440,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            46 => 
            array (
                'id' => 47,
                'name' => 'Uruguay Post',
                'code' => 'uruguay-post',
                'code_num' => '21041',
                'country' => 'Uruguay',
                'country_code' => '2104',
                'api_type' => 0,
                'sort' => 450,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            47 => 
            array (
                'id' => 48,
                'name' => 'Japan Post',
                'code' => 'japan-post',
                'code_num' => '10021',
                'country' => 'Japan',
                'country_code' => '1002',
                'api_type' => 0,
                'sort' => 460,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            48 => 
            array (
                'id' => 49,
                'name' => 'Poșta Română',
                'code' => 'posta-romana',
                'code_num' => '18021',
                'country' => 'Romania',
                'country_code' => '1802',
                'api_type' => 0,
                'sort' => 470,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            49 => 
            array (
                'id' => 50,
                'name' => 'Korea Post',
                'code' => 'korea-post',
                'code_num' => '11051',
                'country' => 'Korea, South',
                'country_code' => '1105',
                'api_type' => 0,
                'sort' => 480,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            50 => 
            array (
                'id' => 51,
                'name' => 'ELTA Hellenic Post',
                'code' => 'greece-post',
                'code_num' => '07071',
                'country' => 'Greece',
                'country_code' => '0707',
                'api_type' => 0,
                'sort' => 490,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            51 => 
            array (
                'id' => 52,
                'name' => 'Deutsche Post',
                'code' => 'deutsche-post',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 500,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            52 => 
            array (
                'id' => 53,
                'name' => 'Česká Pošta',
                'code' => 'czech-post',
                'code_num' => '03221',
                'country' => 'Czech Republic',
                'country_code' => '0322',
                'api_type' => 0,
                'sort' => 510,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            53 => 
            array (
                'id' => 54,
                'name' => 'Correos Chile',
                'code' => 'correos-chile',
                'code_num' => '03101',
                'country' => 'Chile',
                'country_code' => '0310',
                'api_type' => 0,
                'sort' => 520,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            54 => 
            array (
                'id' => 55,
                'name' => 'Åland Post',
                'code' => 'aland-post',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 530,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            55 => 
            array (
                'id' => 56,
                'name' => 'Macao Post',
                'code' => 'macao-post',
                'code_num' => '13011',
                'country' => 'Macao [CN]',
                'country_code' => '1301',
                'api_type' => 0,
                'sort' => 540,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            56 => 
            array (
                'id' => 57,
                'name' => 'WishPost',
                'code' => 'wishpost',
                'code_num' => '190165',
                'country' => '',
                'country_code' => '0000',
                'api_type' => 0,
                'sort' => 550,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            57 => 
            array (
                'id' => 58,
                'name' => 'PFC Express',
                'code' => 'pfcexpress',
                'code_num' => '190282',
                'country' => '',
                'country_code' => '0000',
                'api_type' => 0,
                'sort' => 560,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            58 => 
            array (
                'id' => 59,
                'name' => 'Yun Express',
                'code' => 'yunexpress',
                'code_num' => '190008',
                'country' => '',
                'country_code' => '0000',
                'api_type' => 0,
                'sort' => 570,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            59 => 
            array (
                'id' => 60,
                'name' => 'CNE Express',
                'code' => 'cnexps',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 580,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            60 => 
            array (
                'id' => 61,
                'name' => 'Buylogic',
                'code' => 'buylogic',
                'code_num' => '190018',
                'country' => '',
                'country_code' => '0000',
                'api_type' => 0,
                'sort' => 590,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            61 => 
            array (
                'id' => 62,
                'name' => '4PX',
                'code' => '4px',
                'code_num' => '190094',
                'country' => '',
                'country_code' => '0000',
                'api_type' => 0,
                'sort' => 600,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            62 => 
            array (
                'id' => 63,
                'name' => 'Anjun Logistics',
                'code' => 'anjun',
                'code_num' => '190047',
                'country' => '',
                'country_code' => '0000',
                'api_type' => 0,
                'sort' => 610,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            63 => 
            array (
                'id' => 64,
                'name' => 'J-NET Express',
                'code' => 'j-net',
                'code_num' => '190082',
                'country' => '',
                'country_code' => '0000',
                'api_type' => 0,
                'sort' => 620,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            64 => 
            array (
                'id' => 65,
                'name' => 'Miuson Express',
                'code' => 'miuson-international',
                'code_num' => '100005',
                'country' => '',
                'country_code' => '0000',
                'api_type' => 0,
                'sort' => 630,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            65 => 
            array (
                'id' => 66,
                'name' => 'SF International',
                'code' => 'sfb2c',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 640,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            66 => 
            array (
                'id' => 67,
                'name' => 'S.F Express',
                'code' => 'sf-express',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 650,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            67 => 
            array (
                'id' => 68,
                'name' => 'STO Express',
                'code' => 'sto',
                'code_num' => '190324',
                'country' => '',
                'country_code' => '0000',
                'api_type' => 0,
                'sort' => 660,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            68 => 
            array (
                'id' => 69,
                'name' => 'YTO Express',
                'code' => 'yto',
                'code_num' => '190157',
                'country' => '',
                'country_code' => '0000',
                'api_type' => 0,
                'sort' => 670,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            69 => 
            array (
                'id' => 70,
                'name' => 'TTKD Express',
                'code' => 'ttkd',
                'code_num' => '190077',
                'country' => '',
                'country_code' => '0000',
                'api_type' => 0,
                'sort' => 680,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            70 => 
            array (
                'id' => 71,
                'name' => 'JD Express',
                'code' => 'jd',
                'code_num' => '190302',
                'country' => '',
                'country_code' => '0000',
                'api_type' => 0,
                'sort' => 690,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            71 => 
            array (
                'id' => 72,
                'name' => 'ZTO Express',
                'code' => 'zto',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 700,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            72 => 
            array (
                'id' => 73,
                'name' => 'ZJS International',
                'code' => 'zjs-express',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 710,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            73 => 
            array (
                'id' => 74,
                'name' => 'Yunda Express',
                'code' => 'yunda',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 720,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            74 => 
            array (
                'id' => 75,
                'name' => 'DEPPON',
                'code' => 'deppon',
                'code_num' => '190174',
                'country' => '',
                'country_code' => '0000',
                'api_type' => 0,
                'sort' => 730,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            75 => 
            array (
                'id' => 76,
                'name' => 'XQ Express',
                'code' => 'xqwl',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 740,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            76 => 
            array (
                'id' => 77,
                'name' => 'Chukou1 Logistics',
                'code' => 'chukou1',
                'code_num' => '190111',
                'country' => '',
                'country_code' => '0000',
                'api_type' => 0,
                'sort' => 750,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            77 => 
            array (
                'id' => 78,
                'name' => 'XRU',
                'code' => 'xru',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 760,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            78 => 
            array (
                'id' => 79,
                'name' => 'Ruston',
                'code' => 'ruston',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 770,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            79 => 
            array (
                'id' => 80,
                'name' => 'QFKD Express',
                'code' => 'qfkd',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 780,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            80 => 
            array (
                'id' => 81,
                'name' => 'Nanjing Woyuan',
                'code' => 'nanjingwoyuan',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 790,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            81 => 
            array (
                'id' => 82,
                'name' => 'Hua Han Logistics',
                'code' => 'hhexp',
                'code_num' => '190003',
                'country' => '',
                'country_code' => '0000',
                'api_type' => 0,
                'sort' => 800,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            82 => 
            array (
                'id' => 83,
                'name' => 'Flyt Express',
                'code' => 'flytexpress',
                'code_num' => '190002',
                'country' => '',
                'country_code' => '0000',
                'api_type' => 0,
                'sort' => 810,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            83 => 
            array (
                'id' => 84,
                'name' => 'Ali Business Logistics',
                'code' => 'al8856',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 820,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            84 => 
            array (
                'id' => 85,
                'name' => 'JCEX',
                'code' => 'jcex',
                'code_num' => '190120',
                'country' => '',
                'country_code' => '0000',
                'api_type' => 0,
                'sort' => 830,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            85 => 
            array (
                'id' => 86,
                'name' => 'DPE Express',
                'code' => 'dpe-express',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 840,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            86 => 
            array (
                'id' => 87,
                'name' => 'LWE',
                'code' => 'lwehk',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 850,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            87 => 
            array (
                'id' => 88,
                'name' => 'Equick China',
                'code' => 'equick-cn',
                'code_num' => '190136',
                'country' => '',
                'country_code' => '0000',
                'api_type' => 0,
                'sort' => 860,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            88 => 
            array (
                'id' => 89,
                'name' => 'Cuckoo Express',
                'code' => 'cuckooexpress',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 870,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            89 => 
            array (
                'id' => 90,
                'name' => 'DWZ Express',
                'code' => 'dwz',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 880,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            90 => 
            array (
                'id' => 91,
                'name' => 'Takesend Logistics',
                'code' => 'takesend',
                'code_num' => '190139',
                'country' => '',
                'country_code' => '0000',
                'api_type' => 0,
                'sort' => 890,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            91 => 
            array (
                'id' => 92,
                'name' => 'MC Express',
                'code' => 'md-express',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 900,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            92 => 
            array (
                'id' => 93,
                'name' => 'Aliexpress Standard Shipping',
                'code' => 'cainiao',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 910,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            93 => 
            array (
                'id' => 94,
                'name' => 'TGX',
                'code' => 'tgx',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 920,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            94 => 
            array (
                'id' => 95,
                'name' => 'Spee-Dee Delivery',
                'code' => 'speedee',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 930,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            95 => 
            array (
                'id' => 96,
                'name' => 'Ausworld Express',
                'code' => 'aus',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 940,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            96 => 
            array (
                'id' => 97,
                'name' => 'SX-Express',
                'code' => 'sxexpress',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 950,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            97 => 
            array (
                'id' => 98,
                'name' => 'WSE Logistics',
                'code' => 'gdwse',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 960,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            98 => 
            array (
                'id' => 99,
                'name' => 'LP Express',
                'code' => 'lpexpress',
                'code_num' => '100005',
                'country' => '',
                'country_code' => '0000',
                'api_type' => 0,
                'sort' => 970,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            99 => 
            array (
                'id' => 100,
                'name' => 'Sri Lanka Post',
                'code' => 'sri-lanka-post',
                'code_num' => '19191',
                'country' => 'Sri Lanka',
                'country_code' => '1919',
                'api_type' => 0,
                'sort' => 980,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            100 => 
            array (
                'id' => 101,
                'name' => 'EWE global express',
                'code' => 'ewe',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 990,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            101 => 
            array (
                'id' => 102,
                'name' => 'Jdpplus',
                'code' => 'jdpplus',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 1000,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            102 => 
            array (
                'id' => 103,
                'name' => 'KINGRUNS',
                'code' => 'kingruns',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 1010,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            103 => 
            array (
                'id' => 104,
                'name' => 'Sudan Post',
                'code' => 'sudan-post',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 1020,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            104 => 
            array (
                'id' => 105,
                'name' => 'Venipak',
                'code' => 'venipak',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 1030,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            105 => 
            array (
                'id' => 106,
                'name' => 'Showl',
                'code' => 'showl',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 1040,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            106 => 
            array (
                'id' => 107,
                'name' => 'YingNuo Supply Chain',
                'code' => 'szyn',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 1050,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            107 => 
            array (
                'id' => 108,
                'name' => 'BDM Corriere espresso',
                'code' => 'bdm',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 1060,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            108 => 
            array (
                'id' => 109,
                'name' => 'Redpack Mexico',
                'code' => 'redpack-mexico',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 1070,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            109 => 
            array (
                'id' => 110,
                'name' => 'Syrian Post',
                'code' => 'syrian-post',
                'code_num' => '19261',
                'country' => 'Syrian Arab Republic',
                'country_code' => '1926',
                'api_type' => 0,
                'sort' => 1080,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            110 => 
            array (
                'id' => 111,
                'name' => 'RaidereX',
                'code' => 'raiderex',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 1090,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            111 => 
            array (
                'id' => 112,
                'name' => 'allekurier',
                'code' => 'allekurier',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 1100,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            112 => 
            array (
                'id' => 113,
                'name' => 'Smartcat',
                'code' => 'tcat-cn',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 1110,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            113 => 
            array (
                'id' => 114,
                'name' => 'Sendle',
                'code' => 'sendle',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 1120,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            114 => 
            array (
                'id' => 115,
                'name' => 'RZY Express',
                'code' => 'rzyexpress',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 1130,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            115 => 
            array (
                'id' => 116,
                'name' => 'Transrush',
                'code' => 'transrush',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 1140,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            116 => 
            array (
                'id' => 117,
                'name' => 'Pinjun Express',
                'code' => 'pjbest',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 1150,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            117 => 
            array (
                'id' => 118,
                'name' => 'Pitney Bowes',
                'code' => 'pitneybowes',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 1160,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            118 => 
            array (
                'id' => 119,
                'name' => 'Tanzania Post',
                'code' => 'tanzania-post',
                'code_num' => '20031',
                'country' => 'Tanzania',
                'country_code' => '2003',
                'api_type' => 0,
                'sort' => 1170,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            119 => 
            array (
                'id' => 120,
                'name' => 'Suteng Logistics',
                'code' => 'ste56',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 1180,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            120 => 
            array (
                'id' => 121,
                'name' => 'Bombino Express',
                'code' => 'bombino-express',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 1190,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            121 => 
            array (
                'id' => 122,
                'name' => 'Thailand Post',
                'code' => 'thailand-post',
                'code_num' => '20041',
                'country' => 'Thailand',
                'country_code' => '2004',
                'api_type' => 0,
                'sort' => 1200,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            122 => 
            array (
                'id' => 123,
                'name' => 'Airpak Express',
                'code' => 'airpak-express',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 1210,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            123 => 
            array (
                'id' => 124,
                'name' => 'Winit',
                'code' => 'winit',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 1220,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            124 => 
            array (
                'id' => 125,
                'name' => 'Beebird Logistics',
                'code' => 'beebird',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 1230,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            125 => 
            array (
                'id' => 126,
                'name' => 'DPD Belgium',
                'code' => 'dpd-be',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 1240,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            126 => 
            array (
                'id' => 127,
                'name' => 'Togo Post',
                'code' => 'togo-post',
                'code_num' => '20051',
                'country' => 'Togo',
                'country_code' => '2005',
                'api_type' => 0,
                'sort' => 1250,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            127 => 
            array (
                'id' => 128,
                'name' => 'QEXPRESS',
                'code' => 'qexpress',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 1260,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            128 => 
            array (
                'id' => 129,
                'name' => 'Royal Shipments',
                'code' => 'royal-shipments',
                'code_num' => '100033',
                'country' => '',
                'country_code' => '0000',
                'api_type' => 0,
                'sort' => 1270,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            129 => 
            array (
                'id' => 130,
                'name' => 'Tonga Post',
                'code' => 'tonga-post',
                'code_num' => '20061',
                'country' => 'Tonga',
                'country_code' => '2006',
                'api_type' => 0,
                'sort' => 1280,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            130 => 
            array (
                'id' => 131,
                'name' => 'LBC Express',
                'code' => 'lbcexpress',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 1290,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            131 => 
            array (
                'id' => 132,
                'name' => '360zebra',
                'code' => '360zebra',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 1300,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            132 => 
            array (
                'id' => 133,
                'name' => 'CHINZ LOGISTICS',
                'code' => 'chinz56',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 1310,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            133 => 
            array (
                'id' => 134,
                'name' => 'Tunisia Post',
                'code' => 'tunisia-post',
                'code_num' => '20101',
                'country' => 'Tunisia',
                'country_code' => '2010',
                'api_type' => 0,
                'sort' => 1320,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            134 => 
            array (
                'id' => 135,
                'name' => 'Pandu Logistics',
                'code' => 'pandulogistics',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 1330,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            135 => 
            array (
                'id' => 136,
                'name' => 'Auexpress',
                'code' => 'auexpress',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 1340,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            136 => 
            array (
                'id' => 137,
                'name' => 'iepost',
                'code' => 'iepost',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 1350,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            137 => 
            array (
                'id' => 138,
                'name' => 'Top Ideal Express',
                'code' => 'zhuozhi',
                'code_num' => '190195',
                'country' => '',
                'country_code' => '0000',
                'api_type' => 1,
                'sort' => 1360,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            138 => 
            array (
                'id' => 139,
                'name' => 'Turkey Post',
                'code' => 'turkey-post',
                'code_num' => '20111',
                'country' => 'Turkey',
                'country_code' => '2011',
                'api_type' => 0,
                'sort' => 1370,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            139 => 
            array (
                'id' => 140,
            'name' => 'DPD(HK)',
                'code' => 'dpd-hk',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 1380,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            140 => 
            array (
                'id' => 141,
                'name' => 'FedEx UK',
                'code' => 'fedex-uk',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 1390,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            141 => 
            array (
                'id' => 142,
                'name' => 'MyHermes UK',
                'code' => 'hermes-uk',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 1400,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            142 => 
            array (
                'id' => 143,
                'name' => 'DHL Parcel UK',
                'code' => 'dhl-uk',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 1410,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            143 => 
            array (
                'id' => 144,
                'name' => 'Uganda Post',
                'code' => 'uganda-post',
                'code_num' => '21011',
                'country' => 'Uganda',
                'country_code' => '2101',
                'api_type' => 0,
                'sort' => 1420,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            144 => 
            array (
                'id' => 145,
                'name' => 'Collect+',
                'code' => 'collectplus',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 1430,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            145 => 
            array (
                'id' => 146,
                'name' => 'Zhongtie logistic',
                'code' => 'ztky',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 1440,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            146 => 
            array (
                'id' => 147,
                'name' => 'SAP Express',
                'code' => 'sap-express',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 1460,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            147 => 
            array (
                'id' => 148,
                'name' => 'TNT Click',
                'code' => 'tnt-click',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 1460,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            148 => 
            array (
                'id' => 149,
                'name' => 'Skynet Worldwide Express UK',
                'code' => 'skynetworldwide-uk',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 1470,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            149 => 
            array (
                'id' => 150,
                'name' => 'Dada logistic',
                'code' => 'idada56',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 1480,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            150 => 
            array (
                'id' => 151,
                'name' => 'TONGDA Global',
                'code' => 'tarrive',
                'code_num' => '190194',
                'country' => '',
                'country_code' => '0000',
                'api_type' => 0,
                'sort' => 1490,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            151 => 
            array (
                'id' => 152,
                'name' => 'Rufengda',
                'code' => 'rufengda',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 1500,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            152 => 
            array (
                'id' => 153,
                'name' => 'Whistl',
                'code' => 'whistl',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 1510,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            153 => 
            array (
                'id' => 154,
                'name' => 'Hermesworld',
                'code' => 'hermes',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 1520,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            154 => 
            array (
                'id' => 155,
                'name' => 'Jersey Post',
                'code' => 'jersey-post',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 1530,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            155 => 
            array (
                'id' => 156,
                'name' => 'OrangeDS',
                'code' => 'organeds',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 1540,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            156 => 
            array (
                'id' => 157,
                'name' => 'DX Delivery',
                'code' => 'dxdelivery',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 1550,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            157 => 
            array (
                'id' => 158,
                'name' => 'Nightline',
                'code' => 'nightline',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 1560,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            158 => 
            array (
                'id' => 159,
                'name' => 'Ninja Van Malaysia',
                'code' => 'ninjavan-my',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 1570,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            159 => 
            array (
                'id' => 160,
                'name' => 'dellin',
                'code' => 'dellin',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 1580,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            160 => 
            array (
                'id' => 161,
                'name' => 'MailAmericas',
                'code' => 'mailamericas',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 1590,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            161 => 
            array (
                'id' => 162,
                'name' => 'Hui Logistics',
                'code' => 'huilogistics',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 1600,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            162 => 
            array (
                'id' => 163,
                'name' => 'Uzbekistan Post',
                'code' => 'uzbekistan-post',
                'code_num' => '21031',
                'country' => 'Uzbekistan',
                'country_code' => '2103',
                'api_type' => 0,
                'sort' => 1610,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            163 => 
            array (
                'id' => 164,
                'name' => 'APC Postal Logistics',
                'code' => 'apc',
                'code_num' => '100121',
                'country' => '',
                'country_code' => '0000',
                'api_type' => 0,
                'sort' => 1620,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            164 => 
            array (
                'id' => 165,
                'name' => 'Ninja Van Indonesia',
                'code' => 'ninjaxpress',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 1630,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            165 => 
            array (
                'id' => 166,
                'name' => 'changjiangexpress',
                'code' => 'changjiangexpress',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 1640,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            166 => 
            array (
                'id' => 167,
                'name' => 'Far International Logistics',
                'code' => 'far800',
                'code_num' => '190152',
                'country' => '',
                'country_code' => '0000',
                'api_type' => 0,
                'sort' => 1650,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            167 => 
            array (
                'id' => 168,
                'name' => 'SuperOZ Logistics',
                'code' => 'superoz',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 1660,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            168 => 
            array (
                'id' => 169,
                'name' => 'Vanuatu Post',
                'code' => 'vanuatu-post',
                'code_num' => '22021',
                'country' => 'Vanuatu',
                'country_code' => '2202',
                'api_type' => 0,
                'sort' => 1670,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            169 => 
            array (
                'id' => 170,
                'name' => 'Newgistics',
                'code' => 'newgistics',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 1680,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            170 => 
            array (
                'id' => 171,
                'name' => 'DHL Netherlands',
                'code' => 'dhl-parcel-nl',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 1690,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            171 => 
            array (
                'id' => 172,
                'name' => 'Ninja Van Philippines',
                'code' => 'ninjavan-ph',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 1700,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            172 => 
            array (
                'id' => 173,
                'name' => 'Leopards Express',
                'code' => 'leopardschina',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 1710,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            173 => 
            array (
                'id' => 174,
                'name' => 'Old Dominion Freight Line',
                'code' => 'old-dominion',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 1720,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            174 => 
            array (
                'id' => 175,
                'name' => 'Roadbull Logistics',
                'code' => 'roadbull',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 1730,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            175 => 
            array (
                'id' => 176,
                'name' => 'Ninja Van Thailand',
                'code' => 'ninjavan-th',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 1740,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            176 => 
            array (
                'id' => 177,
                'name' => 'speed-post',
                'code' => 'speed-post',
                'code_num' => '09021',
                'country' => 'India',
                'country_code' => '0902',
                'api_type' => 0,
                'sort' => 1750,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            177 => 
            array (
                'id' => 178,
                'name' => 'cse',
                'code' => 'cse',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 1760,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            178 => 
            array (
                'id' => 179,
                'name' => 'Vietnam Post',
                'code' => 'vietnam-post',
                'code_num' => '22041',
                'country' => 'Vietnam',
                'country_code' => '2204',
                'api_type' => 0,
                'sort' => 1770,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            179 => 
            array (
                'id' => 180,
                'name' => 'DHL Benelux',
                'code' => 'dhl-benelux',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 1780,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            180 => 
            array (
                'id' => 181,
                'name' => 'Estes',
                'code' => 'estes',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 1790,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            181 => 
            array (
                'id' => 182,
                'name' => 'Sai Cheng Logistics',
                'code' => 'saicheng',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 1800,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            182 => 
            array (
                'id' => 183,
                'name' => 'Better Express',
                'code' => 'cbtsd',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 1810,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            183 => 
            array (
                'id' => 184,
                'name' => 'FirstMile',
                'code' => 'firstmile',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 1820,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            184 => 
            array (
                'id' => 185,
                'name' => 'Yemen Post',
                'code' => 'yemen-post',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 1830,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            185 => 
            array (
                'id' => 186,
                'name' => 'Zambia Post',
                'code' => 'zambia-post',
                'code_num' => '26011',
                'country' => 'Zambia',
                'country_code' => '2601',
                'api_type' => 0,
                'sort' => 1840,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            186 => 
            array (
                'id' => 187,
                'name' => '8europe',
                'code' => '8europe',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 1850,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            187 => 
            array (
                'id' => 188,
                'name' => 'Overseas Logistics',
                'code' => 'overseas-logistics',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 1860,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            188 => 
            array (
                'id' => 189,
                'name' => 'Bluecare Express',
                'code' => 'bluecare',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 1870,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            189 => 
            array (
                'id' => 190,
                'name' => 'Zimbabwe Post',
                'code' => 'zimbabwe-post',
                'code_num' => '26021',
                'country' => 'Zimbabwe',
                'country_code' => '2602',
                'api_type' => 0,
                'sort' => 1880,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            190 => 
            array (
                'id' => 191,
                'name' => 'Greyhound',
                'code' => 'greyhound',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 1890,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            191 => 
            array (
                'id' => 192,
                'name' => 'Linex',
                'code' => 'linexsolutions',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 1900,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            192 => 
            array (
                'id' => 193,
                'name' => 'Globegistics Inc',
                'code' => 'globegistics',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 1910,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            193 => 
            array (
                'id' => 194,
                'name' => 'J&T Express TH',
                'code' => 'jt-express-th',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 1920,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            194 => 
            array (
                'id' => 195,
                'name' => 'SYD Express',
                'code' => 'suyd56',
                'code_num' => '190221',
                'country' => '',
                'country_code' => '0000',
                'api_type' => 0,
                'sort' => 1930,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            195 => 
            array (
                'id' => 196,
                'name' => 'ESNAD Express',
                'code' => 'esnad',
                'code_num' => '100103',
                'country' => '',
                'country_code' => '0000',
                'api_type' => 0,
                'sort' => 1940,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            196 => 
            array (
                'id' => 197,
                'name' => 'Nexive',
                'code' => 'nexive',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 1950,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            197 => 
            array (
                'id' => 198,
                'name' => 'Tk Kit',
                'code' => 'tk-kit',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 1960,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            198 => 
            array (
                'id' => 199,
                'name' => 'Asendia HK',
                'code' => 'asendia-hk',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 1970,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            199 => 
            array (
                'id' => 200,
                'name' => 'ASM',
                'code' => 'asmred',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 1980,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            200 => 
            array (
                'id' => 201,
                'name' => 'Overnite Express',
                'code' => 'overnitenet',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 1990,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            201 => 
            array (
                'id' => 202,
                'name' => 'Airwings Courier Express India',
                'code' => 'airwings-india',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 2010,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            202 => 
            array (
                'id' => 203,
                'name' => 'Hermes Germany',
                'code' => 'hermes-de',
                'code_num' => '100005',
                'country' => '',
                'country_code' => '0000',
                'api_type' => 0,
                'sort' => 2020,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            203 => 
            array (
                'id' => 204,
            'name' => 'The Professional Couriers (TPC)',
                'code' => 'professional-couriers',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 2030,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            204 => 
            array (
                'id' => 205,
                'name' => 'International Seur',
                'code' => 'international-seur',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 2040,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            205 => 
            array (
                'id' => 206,
                'name' => 'ABF Freight',
                'code' => 'abf',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 2050,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            206 => 
            array (
                'id' => 207,
                'name' => 'TrakPak',
                'code' => 'trakpak',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 2060,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            207 => 
            array (
                'id' => 208,
                'name' => 'Mondial Relay',
                'code' => 'mondialrelay',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 2070,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            208 => 
            array (
                'id' => 209,
                'name' => 'Matkahuolto',
                'code' => 'matkahuolto',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 2080,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            209 => 
            array (
                'id' => 210,
                'name' => 'LJS',
                'code' => 'bt-exp',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 2090,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            210 => 
            array (
                'id' => 211,
                'name' => 'Couriers Please',
                'code' => 'couriers-please',
                'code_num' => '100122',
                'country' => '',
                'country_code' => '0000',
                'api_type' => 0,
                'sort' => 2110,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            211 => 
            array (
                'id' => 212,
                'name' => 'ACS Courier',
                'code' => 'acscourier',
                'code_num' => '100005',
                'country' => '',
                'country_code' => '0000',
                'api_type' => 0,
                'sort' => 2120,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            212 => 
            array (
                'id' => 213,
                'name' => 'AsiaFly',
                'code' => 'asiafly',
                'code_num' => '190303',
                'country' => '',
                'country_code' => '0000',
                'api_type' => 0,
                'sort' => 2130,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            213 => 
            array (
                'id' => 214,
                'name' => 'RL Carriers',
                'code' => 'rl-carriers',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 2150,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            214 => 
            array (
                'id' => 215,
                'name' => 'Afghan Post',
                'code' => 'afghan-post',
                'code_num' => '01021',
                'country' => 'Afghanistan',
                'country_code' => '0102',
                'api_type' => 0,
                'sort' => 2160,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            215 => 
            array (
                'id' => 216,
                'name' => 'DPD Poland',
                'code' => 'dpd-poland',
                'code_num' => '100111',
                'country' => '',
                'country_code' => '0000',
                'api_type' => 0,
                'sort' => 2170,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            216 => 
            array (
                'id' => 217,
                'name' => 'Hanjin Shipping',
                'code' => 'hanjin',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 2180,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            217 => 
            array (
                'id' => 218,
                'name' => 'Albania Post',
                'code' => 'posta-shqiptare',
                'code_num' => '01031',
                'country' => 'Albania',
                'country_code' => '0103',
                'api_type' => 0,
                'sort' => 2190,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            218 => 
            array (
                'id' => 219,
                'name' => 'Geniki Taxydromiki',
                'code' => 'taxydromiki',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 2200,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            219 => 
            array (
                'id' => 220,
                'name' => 'Cess',
                'code' => 'cess',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 2210,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            220 => 
            array (
                'id' => 221,
                'name' => 'Flying Leopards Express',
                'code' => 'njfeibao',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 2220,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            221 => 
            array (
                'id' => 222,
                'name' => 'Andorra Post',
                'code' => 'andorra-post',
                'code_num' => '06051',
                'country' => 'France',
                'country_code' => '0605',
                'api_type' => 0,
                'sort' => 2230,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            222 => 
            array (
                'id' => 223,
                'name' => 'Envialia',
                'code' => 'envialia',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 2240,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            223 => 
            array (
                'id' => 224,
                'name' => 'Best Express',
                'code' => 'bestex',
                'code_num' => '190259',
                'country' => '',
                'country_code' => '0000',
                'api_type' => 0,
                'sort' => 2250,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            224 => 
            array (
                'id' => 225,
                'name' => 'First Flight Couriers',
                'code' => 'firstflightme',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 2260,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            225 => 
            array (
                'id' => 226,
                'name' => 'Canpar Courier',
                'code' => 'canpar',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 2270,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            226 => 
            array (
                'id' => 227,
                'name' => 'Adicional Logistics',
                'code' => 'adicional',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 2280,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            227 => 
            array (
                'id' => 228,
                'name' => '17 Post Service',
                'code' => '17postservice',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 2300,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            228 => 
            array (
                'id' => 229,
                'name' => 'CBL Logistics',
                'code' => 'cbl-logistica',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 2310,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            229 => 
            array (
                'id' => 230,
                'name' => 'JET',
                'code' => 'jet',
                'code_num' => '100074',
                'country' => '',
                'country_code' => '0000',
                'api_type' => 0,
                'sort' => 2320,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            230 => 
            array (
                'id' => 231,
                'name' => 'Redur Spain',
                'code' => 'redur-es',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 2330,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            231 => 
            array (
                'id' => 232,
                'name' => '360lion Express',
                'code' => '360lion',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 2340,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            232 => 
            array (
                'id' => 233,
                'name' => 'Antilles Post',
                'code' => 'antilles-post',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 2350,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            233 => 
            array (
                'id' => 234,
                'name' => 'Argentina Post',
                'code' => 'correo-argentino',
                'code_num' => '01121',
                'country' => 'Argentina',
                'country_code' => '0112',
                'api_type' => 0,
                'sort' => 2360,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            234 => 
            array (
                'id' => 235,
                'name' => 'Siodemka',
                'code' => 'siodemka',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 2370,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            235 => 
            array (
                'id' => 236,
                'name' => 'Armenia Post',
                'code' => 'armenia-post',
                'code_num' => '01131',
                'country' => 'Armenia',
                'country_code' => '0113',
                'api_type' => 0,
                'sort' => 2380,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            236 => 
            array (
                'id' => 237,
                'name' => 'Aruba Post',
                'code' => 'aruba-post',
                'code_num' => '92031',
                'country' => 'Aruba [NL]',
                'country_code' => '9203',
                'api_type' => 0,
                'sort' => 2390,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            237 => 
            array (
                'id' => 238,
                'name' => 'Exapaq',
                'code' => 'exapaq',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 2400,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            238 => 
            array (
                'id' => 239,
                'name' => 'HCT Express',
                'code' => 'hct',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 2410,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            239 => 
            array (
                'id' => 240,
                'name' => 'LDXpress',
                'code' => 'ldxpress',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 2420,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            240 => 
            array (
                'id' => 241,
                'name' => 'Austrian Post',
                'code' => 'austria-post',
                'code_num' => '01161',
                'country' => 'Austria',
                'country_code' => '0116',
                'api_type' => 0,
                'sort' => 2430,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            241 => 
            array (
                'id' => 242,
                'name' => 'Azerbaijan Post',
                'code' => 'azerbaijan-post',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 2440,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            242 => 
            array (
                'id' => 243,
                'name' => 'CJ Logistics',
                'code' => 'doortodoor',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 2450,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            243 => 
            array (
                'id' => 244,
                'name' => 'Bahrain Post',
                'code' => 'bahrain-post',
                'code_num' => '02021',
                'country' => 'Bahrain',
                'country_code' => '0202',
                'api_type' => 0,
                'sort' => 2460,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            244 => 
            array (
                'id' => 245,
                'name' => 'K1 Express',
                'code' => 'kuajingyihao',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 2470,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            245 => 
            array (
                'id' => 246,
                'name' => 'Bangladesh EMS',
                'code' => 'bangladesh-ems',
                'code_num' => '02031',
                'country' => 'Bangladesh',
                'country_code' => '0203',
                'api_type' => 0,
                'sort' => 2480,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            246 => 
            array (
                'id' => 247,
                'name' => '7-ELEVEN',
                'code' => 'qi-eleven',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 2490,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            247 => 
            array (
                'id' => 248,
                'name' => 'Barbados Post',
                'code' => 'barbados-post',
                'code_num' => '02041',
                'country' => 'Barbados',
                'country_code' => '0204',
                'api_type' => 0,
                'sort' => 2500,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            248 => 
            array (
                'id' => 249,
                'name' => 'Belarus Post',
                'code' => 'belpochta',
                'code_num' => '02051',
                'country' => 'Belarus',
                'country_code' => '0205',
                'api_type' => 0,
                'sort' => 2510,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            249 => 
            array (
                'id' => 250,
                'name' => 'ORANGE CONNEX',
                'code' => 'orangeconnex',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 2520,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            250 => 
            array (
                'id' => 251,
                'name' => 'Belize Post',
                'code' => 'belize-post',
                'code_num' => '02071',
                'country' => 'Belize',
                'country_code' => '0207',
                'api_type' => 0,
                'sort' => 2530,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            251 => 
            array (
                'id' => 252,
                'name' => '007EX',
                'code' => '007ex',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 2540,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            252 => 
            array (
                'id' => 253,
                'name' => 'Benin Post',
                'code' => 'benin-post',
                'code_num' => '02081',
                'country' => 'Benin',
                'country_code' => '0208',
                'api_type' => 0,
                'sort' => 2550,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            253 => 
            array (
                'id' => 254,
                'name' => 'Bermuda Post',
                'code' => 'bermuda-post',
                'code_num' => '90041',
                'country' => 'Bermuda [GB]',
                'country_code' => '9004',
                'api_type' => 0,
                'sort' => 2560,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            254 => 
            array (
                'id' => 255,
                'name' => 'JS EXPRESS',
                'code' => 'js-exp',
                'code_num' => '190199',
                'country' => '',
                'country_code' => '0000',
                'api_type' => 0,
                'sort' => 2570,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            255 => 
            array (
                'id' => 256,
                'name' => 'Bhutan Post',
                'code' => 'bhutan-post',
                'code_num' => '02101',
                'country' => 'Bhutan',
                'country_code' => '0210',
                'api_type' => 0,
                'sort' => 2580,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            256 => 
            array (
                'id' => 257,
                'name' => 'Bolivia Post',
                'code' => 'correos-bolivia',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 2590,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            257 => 
            array (
                'id' => 258,
                'name' => 'Gofly',
                'code' => 'gofly',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 2600,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            258 => 
            array (
                'id' => 259,
                'name' => 'Gaopost',
                'code' => 'gaopost',
                'code_num' => '190183',
                'country' => '',
                'country_code' => '0000',
                'api_type' => 0,
                'sort' => 2610,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            259 => 
            array (
                'id' => 260,
                'name' => 'Bosnia And Herzegovina Post',
                'code' => 'bosnia-and-herzegovina-post',
                'code_num' => '02121',
                'country' => 'Bosnia and Herzegovina',
                'country_code' => '0212',
                'api_type' => 0,
                'sort' => 2620,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            260 => 
            array (
                'id' => 261,
                'name' => 'Botswana Post',
                'code' => 'botswana-post',
                'code_num' => '02131',
                'country' => 'Botswana',
                'country_code' => '0213',
                'api_type' => 0,
                'sort' => 2630,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            261 => 
            array (
                'id' => 262,
                'name' => 'Airfex',
                'code' => 'airfex',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 2640,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            262 => 
            array (
                'id' => 263,
                'name' => 'Brunei Post',
                'code' => 'brunei-post',
                'code_num' => '02161',
                'country' => 'Brunei',
                'country_code' => '0216',
                'api_type' => 0,
                'sort' => 2650,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            263 => 
            array (
                'id' => 264,
                'name' => 'Bulgaria Post',
                'code' => 'bulgaria-post',
                'code_num' => '02171',
                'country' => 'Bulgaria',
                'country_code' => '0217',
                'api_type' => 0,
                'sort' => 2660,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            264 => 
            array (
                'id' => 265,
                'name' => 'DB Schenker',
                'code' => 'dbschenker',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 2670,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            265 => 
            array (
                'id' => 266,
                'name' => 'Burkina Faso Post',
                'code' => 'sonapost',
                'code_num' => '02181',
                'country' => 'Burkina Faso',
                'country_code' => '0218',
                'api_type' => 0,
                'sort' => 2680,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            266 => 
            array (
                'id' => 267,
                'name' => 'Burundi Post',
                'code' => 'burundi-post',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 2690,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            267 => 
            array (
                'id' => 268,
                'name' => 'SINOAIR',
                'code' => 'sinoair',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 2700,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            268 => 
            array (
                'id' => 269,
                'name' => 'UK Mail',
                'code' => 'ukmail',
                'code_num' => '100050',
                'country' => '',
                'country_code' => '0000',
                'api_type' => 0,
                'sort' => 2710,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            269 => 
            array (
                'id' => 270,
                'name' => 'Cambodia Post',
                'code' => 'cambodia-post',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 2720,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            270 => 
            array (
                'id' => 271,
                'name' => 'Cameroon Post',
                'code' => 'campost',
                'code_num' => '03031',
                'country' => 'Cameroon',
                'country_code' => '0303',
                'api_type' => 0,
                'sort' => 2730,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            271 => 
            array (
                'id' => 272,
                'name' => 'Italy SDA',
                'code' => 'italy-sda',
                'code_num' => '100019',
                'country' => '',
                'country_code' => '0000',
                'api_type' => 0,
                'sort' => 2740,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            272 => 
            array (
                'id' => 273,
                'name' => 'Correios Cabo Verde',
                'code' => 'correios-cabo-verde',
                'code_num' => '03061',
                'country' => 'Cape Verde',
                'country_code' => '0306',
                'api_type' => 0,
                'sort' => 2750,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            273 => 
            array (
                'id' => 274,
                'name' => 'T Cat',
                'code' => 't-cat',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 2760,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            274 => 
            array (
                'id' => 275,
                'name' => 'Eshipping',
                'code' => 'yht',
                'code_num' => '190114',
                'country' => '',
                'country_code' => '0000',
                'api_type' => 0,
                'sort' => 2770,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            275 => 
            array (
                'id' => 276,
                'name' => 'Fastgo',
                'code' => 'fastgo',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 2780,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            276 => 
            array (
                'id' => 277,
                'name' => 'Colombia Post',
                'code' => 'colombia-post',
                'code_num' => '03131',
                'country' => 'Colombia',
                'country_code' => '0313',
                'api_type' => 0,
                'sort' => 2790,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            277 => 
            array (
                'id' => 278,
                'name' => 'Delcart',
                'code' => 'delcart-in',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 2800,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            278 => 
            array (
                'id' => 279,
                'name' => 'MyIB',
                'code' => 'myib',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 2810,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            279 => 
            array (
                'id' => 280,
                'name' => 'PCA',
                'code' => 'pca',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 2820,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            280 => 
            array (
                'id' => 281,
                'name' => 'City-Link Express',
                'code' => 'citylinkexpress',
                'code_num' => '100081',
                'country' => '',
                'country_code' => '0000',
                'api_type' => 0,
                'sort' => 2830,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            281 => 
            array (
                'id' => 282,
                'name' => 'Fulfillmen',
                'code' => 'fulfillmen',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 2840,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            282 => 
            array (
                'id' => 283,
                'name' => 'FTD Express',
                'code' => 'ftd',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 2850,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            283 => 
            array (
                'id' => 284,
                'name' => '2GO',
                'code' => '2go',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 2860,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            284 => 
            array (
                'id' => 285,
                'name' => 'tuffnells',
                'code' => 'tuffnells',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 2870,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            285 => 
            array (
                'id' => 286,
                'name' => 'Costa Rica Post',
                'code' => 'correos-de-costa-rica',
                'code_num' => '03181',
                'country' => 'Costa Rica',
                'country_code' => '0318',
                'api_type' => 0,
                'sort' => 2880,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            286 => 
            array (
                'id' => 287,
                'name' => 'Shipgce Express',
                'code' => 'shipgce',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 2890,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            287 => 
            array (
                'id' => 288,
                'name' => 'freaky quick logistics',
                'code' => 'freakyquick',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 2900,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            288 => 
            array (
                'id' => 289,
                'name' => 'Croatia Post',
                'code' => 'hrvatska-posta',
                'code_num' => '03191',
                'country' => 'Croatia',
                'country_code' => '0319',
                'api_type' => 0,
                'sort' => 2910,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            289 => 
            array (
                'id' => 290,
                'name' => 'Xend Express',
                'code' => 'xend',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 2920,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            290 => 
            array (
                'id' => 291,
                'name' => 'Turtle express',
                'code' => 'turtle-express',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 2930,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            291 => 
            array (
                'id' => 292,
                'name' => 'Cuba Post',
                'code' => 'cuba-post',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 2940,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            292 => 
            array (
                'id' => 293,
                'name' => 'Wise Express',
                'code' => 'wise-express',
                'code_num' => '190085',
                'country' => '',
                'country_code' => '0000',
                'api_type' => 0,
                'sort' => 2950,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            293 => 
            array (
                'id' => 294,
                'name' => 'Sure56',
                'code' => 'sure56',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 2960,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            294 => 
            array (
                'id' => 295,
                'name' => 'Cyprus Post',
                'code' => 'cyprus-post',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 2970,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            295 => 
            array (
                'id' => 296,
                'name' => 'AIR21',
                'code' => 'air21',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 2980,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            296 => 
            array (
                'id' => 297,
                'name' => 'GATI Courier',
                'code' => 'gaticn',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 2990,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            297 => 
            array (
                'id' => 298,
                'name' => 'CEVA Logistics',
                'code' => 'ceva-logistics',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 3000,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            298 => 
            array (
                'id' => 299,
                'name' => 'Cnpex',
                'code' => 'cnpex',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 3010,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            299 => 
            array (
                'id' => 300,
                'name' => 'Denmark post',
                'code' => 'denmark-post',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 3020,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            300 => 
            array (
                'id' => 301,
                'name' => 'Airspeed International Corporati',
                'code' => 'airspeed',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 3030,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            301 => 
            array (
                'id' => 302,
                'name' => 'DPEX China',
                'code' => 'szdpex',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 3040,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            302 => 
            array (
                'id' => 303,
                'name' => 'UBX Express',
                'code' => 'ubx-uk',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 3050,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            303 => 
            array (
                'id' => 304,
                'name' => '1hcang',
                'code' => '1hcang',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 3060,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            304 => 
            array (
                'id' => 305,
                'name' => 'RAF Philippines',
                'code' => 'raf',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 3070,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            305 => 
            array (
                'id' => 306,
                'name' => 'Shree Mahabali Express',
                'code' => 'shree-mahabali-express',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 3080,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            306 => 
            array (
                'id' => 307,
                'name' => 'Tea post',
                'code' => 'tea-post',
                'code_num' => '190046',
                'country' => '',
                'country_code' => '0000',
                'api_type' => 0,
                'sort' => 3090,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            307 => 
            array (
                'id' => 308,
                'name' => 'YDH',
                'code' => 'ydhex',
                'code_num' => '190200',
                'country' => '',
                'country_code' => '0000',
                'api_type' => 0,
                'sort' => 3100,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            308 => 
            array (
                'id' => 309,
                'name' => 'Tiki',
                'code' => 'tiki',
                'code_num' => '100005',
                'country' => '',
                'country_code' => '0000',
                'api_type' => 0,
                'sort' => 3110,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            309 => 
            array (
                'id' => 310,
                'name' => 'Ecuador Post',
                'code' => 'correos-del-ecuador',
                'code_num' => '05011',
                'country' => 'Ecuador',
                'country_code' => '0501',
                'api_type' => 0,
                'sort' => 3120,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            310 => 
            array (
                'id' => 311,
                'name' => 'Sunyou',
                'code' => 'sunyou',
                'code_num' => '190072',
                'country' => '',
                'country_code' => '0000',
                'api_type' => 0,
                'sort' => 3130,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            311 => 
            array (
                'id' => 312,
                'name' => 'Quickway',
                'code' => 'quickway',
                'code_num' => '190201',
                'country' => '',
                'country_code' => '0000',
                'api_type' => 0,
                'sort' => 3140,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            312 => 
            array (
                'id' => 313,
                'name' => 'Egypt Post',
                'code' => 'egypt-post',
                'code_num' => '05021',
                'country' => 'Egypt',
                'country_code' => '0502',
                'api_type' => 0,
                'sort' => 3150,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            313 => 
            array (
                'id' => 314,
                'name' => 'Wahana',
                'code' => 'wahana',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 3160,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            314 => 
            array (
                'id' => 315,
                'name' => 'El Salvador Post',
                'code' => 'el-salvador-post',
                'code_num' => '19031',
                'country' => 'El Salvador',
                'country_code' => '1903',
                'api_type' => 0,
                'sort' => 3170,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            315 => 
            array (
                'id' => 316,
                'name' => 'DHL Global Mail Asia',
                'code' => 'dhlecommerce-asia',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 3180,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            316 => 
            array (
                'id' => 317,
                'name' => 'Giao Hàng Nhanh',
                'code' => 'ghn',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 3190,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            317 => 
            array (
                'id' => 318,
                'name' => 'Eritrea Post',
                'code' => 'eritrea-post',
                'code_num' => '05061',
                'country' => 'Eritrea',
                'country_code' => '0506',
                'api_type' => 0,
                'sort' => 3200,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            318 => 
            array (
                'id' => 319,
                'name' => 'DHL Active Tracing',
                'code' => 'dhl-active',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 3210,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            319 => 
            array (
                'id' => 320,
                'name' => 'Estonia Post',
                'code' => 'omniva',
                'code_num' => '05041',
                'country' => 'Estonia',
                'country_code' => '0504',
                'api_type' => 0,
                'sort' => 3220,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            320 => 
            array (
                'id' => 321,
                'name' => 'Viettel Post',
                'code' => 'viettelpost',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 3230,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            321 => 
            array (
                'id' => 322,
                'name' => 'Ethiopia Post',
                'code' => 'ethiopia-post',
                'code_num' => '05051',
                'country' => 'Ethiopia',
                'country_code' => '0505',
                'api_type' => 0,
                'sort' => 3240,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            322 => 
            array (
                'id' => 323,
                'name' => 'TNT Reference',
                'code' => 'tnt-reference',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 3250,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            323 => 
            array (
                'id' => 324,
                'name' => 'Faroe Islands Post',
                'code' => 'faroe-islands-post',
                'code_num' => '96021',
                'country' => 'Faroe Islands [DK]',
                'country_code' => '9602',
                'api_type' => 0,
                'sort' => 3260,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            324 => 
            array (
                'id' => 325,
                'name' => 'Dotzot',
                'code' => 'dotzot',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 3270,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            325 => 
            array (
                'id' => 326,
                'name' => 'YL express',
                'code' => 'yunlu',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 3280,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            326 => 
            array (
                'id' => 327,
                'name' => 'Fiji Post',
                'code' => 'fiji-post',
                'code_num' => '06031',
                'country' => 'Fiji',
                'country_code' => '0603',
                'api_type' => 0,
                'sort' => 3290,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            327 => 
            array (
                'id' => 328,
                'name' => 'Kangaroo Worldwide Express',
                'code' => 'kangaroo-my',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 3300,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            328 => 
            array (
                'id' => 329,
                'name' => 'Finland Post - Posti',
                'code' => 'finland-posti',
                'code_num' => '06041',
                'country' => 'Finland',
                'country_code' => '0604',
                'api_type' => 0,
                'sort' => 3310,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            329 => 
            array (
                'id' => 330,
                'name' => 'Jiayi Express',
                'code' => 'jiayi56',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 3320,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            330 => 
            array (
                'id' => 331,
                'name' => 'Kerry Express',
                'code' => 'kerryexpress',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 3330,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            331 => 
            array (
                'id' => 332,
                'name' => 'Deltec Courier',
                'code' => 'deltec-courier',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 3340,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            332 => 
            array (
                'id' => 333,
                'name' => 'Maxcellents Pte Ltd',
                'code' => 'maxcellents',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 3350,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            333 => 
            array (
                'id' => 334,
                'name' => 'GEL Express',
                'code' => 'gel-express',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 3360,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            334 => 
            array (
                'id' => 335,
                'name' => 'Nationwide Express',
                'code' => 'nationwide-my',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 3370,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            335 => 
            array (
                'id' => 336,
                'name' => 'Georgia Post',
                'code' => 'georgian-post',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 3380,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            336 => 
            array (
                'id' => 337,
                'name' => 'RPX Online',
                'code' => 'rpxonline',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 3390,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            337 => 
            array (
                'id' => 338,
                'name' => 'Ghana Post',
                'code' => 'ghana-post',
                'code_num' => '07051',
                'country' => 'Ghana',
                'country_code' => '0705',
                'api_type' => 0,
                'sort' => 3400,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            338 => 
            array (
                'id' => 339,
                'name' => 'Scorejp',
                'code' => 'scorejp',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 3410,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            339 => 
            array (
                'id' => 340,
                'name' => 'Gibraltar  Post',
                'code' => 'gibraltar-post',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 3420,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            340 => 
            array (
                'id' => 341,
                'name' => 'Nhans Solutions',
                'code' => 'nhans-solutions',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 3430,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            341 => 
            array (
                'id' => 342,
                'name' => 'Espeedpost',
                'code' => 'espeedpost',
                'code_num' => '190061',
                'country' => '',
                'country_code' => '0000',
                'api_type' => 0,
                'sort' => 3440,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            342 => 
            array (
                'id' => 343,
                'name' => 'LD Logistics',
                'code' => 'ldlog',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 3450,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            343 => 
            array (
                'id' => 344,
                'name' => 'Greenland Post',
                'code' => 'tele-post',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 3460,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            344 => 
            array (
                'id' => 345,
                'name' => 'Jet-Ship Worldwide',
                'code' => 'jet-ship',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 3470,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            345 => 
            array (
                'id' => 346,
                'name' => 'XPOST',
                'code' => 'xpost',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 3480,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            346 => 
            array (
                'id' => 347,
                'name' => 'Guatemala Post',
                'code' => 'elcorreo',
                'code_num' => '07121',
                'country' => 'Guatemala',
                'country_code' => '0712',
                'api_type' => 0,
                'sort' => 3490,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            347 => 
            array (
                'id' => 348,
                'name' => 'Ecargo',
                'code' => 'ecargo-asia',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 3500,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            348 => 
            array (
                'id' => 349,
                'name' => 'GmbH',
                'code' => 'myaustrianpost',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 3510,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            349 => 
            array (
                'id' => 350,
                'name' => 'Guernsey Post',
                'code' => 'guernsey-post',
                'code_num' => '90071',
                'country' => 'Guernsey [GB]',
                'country_code' => '9007',
                'api_type' => 0,
                'sort' => 3520,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            350 => 
            array (
                'id' => 351,
                'name' => 'Delhivery',
                'code' => 'delhivery',
                'code_num' => '100060',
                'country' => '',
                'country_code' => '0000',
                'api_type' => 0,
                'sort' => 3530,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            351 => 
            array (
                'id' => 352,
                'name' => 'GLS',
                'code' => 'gls',
                'code_num' => '100005',
                'country' => '',
                'country_code' => '0000',
                'api_type' => 0,
                'sort' => 3540,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            352 => 
            array (
                'id' => 353,
                'name' => 'Cosex',
                'code' => 'cosex',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 3550,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            353 => 
            array (
                'id' => 354,
                'name' => 'BRT Bartolini',
                'code' => 'bartolini',
                'code_num' => '100026',
                'country' => '',
                'country_code' => '0000',
                'api_type' => 0,
                'sort' => 3560,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            354 => 
            array (
                'id' => 355,
                'name' => 'NuvoEx',
                'code' => 'nuvoex',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 3570,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            355 => 
            array (
                'id' => 356,
                'name' => 'ETS Express',
                'code' => 'ets-express',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 3580,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            356 => 
            array (
                'id' => 357,
                'name' => 'DPD',
                'code' => 'dpd',
                'code_num' => '100007',
                'country' => '',
                'country_code' => '0000',
                'api_type' => 0,
                'sort' => 3590,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            357 => 
            array (
                'id' => 358,
                'name' => 'Parcelled.in',
                'code' => 'parcelled-in',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 3600,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            358 => 
            array (
                'id' => 359,
                'name' => 'Hong Tai',
                'code' => 'ht56',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 3610,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            359 => 
            array (
                'id' => 360,
                'name' => 'Aramex',
                'code' => 'aramex',
                'code_num' => '100006',
                'country' => '',
                'country_code' => '0000',
                'api_type' => 0,
                'sort' => 3620,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            360 => 
            array (
                'id' => 361,
                'name' => 'TOLL',
                'code' => 'toll',
                'code_num' => '100009',
                'country' => '',
                'country_code' => '0000',
                'api_type' => 0,
                'sort' => 3630,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            361 => 
            array (
                'id' => 362,
                'name' => 'Ecom Express',
                'code' => 'ecom-express',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 3640,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            362 => 
            array (
                'id' => 363,
                'name' => 'Quantium',
                'code' => 'quantium',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 3650,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            363 => 
            array (
                'id' => 364,
                'name' => 'E-lian',
                'code' => 'elianpost',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 3660,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            364 => 
            array (
                'id' => 365,
                'name' => 'Iceland Post',
                'code' => 'iceland-post',
                'code_num' => '09011',
                'country' => 'Iceland',
                'country_code' => '0901',
                'api_type' => 0,
                'sort' => 3670,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            365 => 
            array (
                'id' => 366,
                'name' => 'GDEX',
                'code' => 'gdex',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 3680,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            366 => 
            array (
                'id' => 367,
                'name' => 'Alpha Fast',
                'code' => 'alpha-fast',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 3690,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            367 => 
            array (
                'id' => 368,
                'name' => 'Bao Tongda Freight Forwarding',
                'code' => 'btd56',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 3700,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            368 => 
            array (
                'id' => 369,
                'name' => 'Omni Parcel',
                'code' => 'omniparcel',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 3710,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            369 => 
            array (
                'id' => 370,
                'name' => 'Hi Life',
                'code' => 'hi-life',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 3720,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            370 => 
            array (
                'id' => 371,
                'name' => 'Indonesia Post',
                'code' => 'indonesia-post',
                'code_num' => '09031',
                'country' => 'Indonesia',
                'country_code' => '0903',
                'api_type' => 0,
                'sort' => 3730,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            371 => 
            array (
                'id' => 372,
                'name' => 'SkyNet Malaysia',
                'code' => 'skynet',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 3740,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            372 => 
            array (
                'id' => 373,
                'name' => 'CDEK Express',
                'code' => 'cdek',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 3750,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            373 => 
            array (
                'id' => 374,
                'name' => 'Shree Maruti Courier',
                'code' => 'shreemaruticourier',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 3760,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            374 => 
            array (
                'id' => 375,
                'name' => 'Iran Post',
                'code' => 'iran-post',
                'code_num' => '09041',
                'country' => 'Iran',
                'country_code' => '0904',
                'api_type' => 0,
                'sort' => 3770,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            375 => 
            array (
                'id' => 376,
                'name' => 'Trackon Courier',
                'code' => 'trackon',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 3780,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            376 => 
            array (
                'id' => 377,
                'name' => 'iMile',
                'code' => 'imile',
                'code_num' => '100005',
                'country' => '',
                'country_code' => '0000',
                'api_type' => 0,
                'sort' => 3790,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            377 => 
            array (
                'id' => 378,
                'name' => 'BQC',
                'code' => 'bqc',
                'code_num' => '190011',
                'country' => '',
                'country_code' => '0000',
                'api_type' => 0,
                'sort' => 3800,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            378 => 
            array (
                'id' => 379,
                'name' => 'Ltexp',
                'code' => 'ltexp',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 3780,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            379 => 
            array (
                'id' => 380,
                'name' => 'Israel Post',
                'code' => 'israel-post',
                'code_num' => '09061',
                'country' => 'Israel',
                'country_code' => '0906',
                'api_type' => 0,
                'sort' => 3810,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            380 => 
            array (
                'id' => 381,
                'name' => 'PostNL International 3S',
                'code' => 'postnl-3s',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 3820,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            381 => 
            array (
                'id' => 382,
                'name' => 'OCS Express',
                'code' => 'ocschina',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 3830,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            382 => 
            array (
                'id' => 383,
                'name' => 'Fang Yuan',
                'code' => 'hnfywl',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 3840,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            383 => 
            array (
                'id' => 384,
                'name' => 'Ivory Coast EMS',
                'code' => 'ivory-coast-ems',
                'code_num' => '03121',
                'country' => 'Ivory Coast',
                'country_code' => '0312',
                'api_type' => 0,
                'sort' => 3850,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            384 => 
            array (
                'id' => 385,
                'name' => 'Naqel',
                'code' => 'naqel',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 3860,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            385 => 
            array (
                'id' => 386,
                'name' => 'Kerry Tec',
                'code' => 'kerry-tec',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 3870,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            386 => 
            array (
                'id' => 387,
                'name' => 'Jamaica Post',
                'code' => 'jamaica-post',
                'code_num' => '10011',
                'country' => 'Jamaica',
                'country_code' => '1001',
                'api_type' => 0,
                'sort' => 3880,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            387 => 
            array (
                'id' => 388,
                'name' => 'Pitney Bowes',
                'code' => 'parcel',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 3890,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            388 => 
            array (
                'id' => 389,
                'name' => 'BEL',
                'code' => '8256ru',
                'code_num' => '190108',
                'country' => '',
                'country_code' => '0000',
                'api_type' => 0,
                'sort' => 3900,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            389 => 
            array (
                'id' => 390,
                'name' => 'Jordan Post',
                'code' => 'jordan-post',
                'code_num' => '10031',
                'country' => 'Jordan',
                'country_code' => '1003',
                'api_type' => 0,
                'sort' => 3910,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            390 => 
            array (
                'id' => 391,
                'name' => 'One World Express',
                'code' => 'oneworldexpress',
                'code_num' => '100011',
                'country' => '',
                'country_code' => '0000',
                'api_type' => 0,
                'sort' => 3920,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            391 => 
            array (
                'id' => 392,
                'name' => 'Kazakhstan Post',
                'code' => 'kazpost',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 3930,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            392 => 
            array (
                'id' => 393,
                'name' => 'ADSOne',
                'code' => 'adsone',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 3940,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            393 => 
            array (
                'id' => 394,
                'name' => 'BSI express',
                'code' => 'bsi',
                'code_num' => '190290',
                'country' => '',
                'country_code' => '0000',
                'api_type' => 0,
                'sort' => 3950,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            394 => 
            array (
                'id' => 395,
                'name' => 'CXC',
                'code' => 'cxc',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 3960,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            395 => 
            array (
                'id' => 396,
                'name' => 'Kenya Post',
                'code' => 'kenya-post',
                'code_num' => '11021',
                'country' => 'Kenya',
                'country_code' => '1102',
                'api_type' => 0,
                'sort' => 3970,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            396 => 
            array (
                'id' => 397,
                'name' => 'Landmark Global',
                'code' => 'landmark-global',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 3980,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            397 => 
            array (
                'id' => 398,
                'name' => 'The Courier Guy',
                'code' => 'thecourierguy',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 3990,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            398 => 
            array (
                'id' => 399,
                'name' => 'SMSA Express',
                'code' => 'smsa-express',
                'code_num' => '100034',
                'country' => '',
                'country_code' => '0000',
                'api_type' => 0,
                'sort' => 4000,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            399 => 
            array (
                'id' => 400,
                'name' => 'King Kong Express',
                'code' => 'kke',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 4010,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            400 => 
            array (
                'id' => 401,
                'name' => 'CJ Dropshipping',
                'code' => 'cj-dropshipping',
                'code_num' => '190283',
                'country' => '',
                'country_code' => '0000',
                'api_type' => 0,
                'sort' => 4020,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            401 => 
            array (
                'id' => 402,
                'name' => 'Jayeek',
                'code' => 'jayeek',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 4030,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            402 => 
            array (
                'id' => 403,
            'name' => 'Best Express(logistic)',
                'code' => '800best',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 4040,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            403 => 
            array (
                'id' => 404,
                'name' => '17Feia Express',
                'code' => '17feia',
                'code_num' => '190164',
                'country' => '',
                'country_code' => '0000',
                'api_type' => 0,
                'sort' => 4050,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            404 => 
            array (
                'id' => 405,
                'name' => 'Cosmetics Now',
                'code' => 'costmeticsnow',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 4060,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            405 => 
            array (
                'id' => 406,
                'name' => 'Blue Sky Express',
                'code' => 'blueskyexpress',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 4070,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            406 => 
            array (
                'id' => 407,
                'name' => 'Kyrgyzstan Post',
                'code' => 'kyrgyzpost',
                'code_num' => '11091',
                'country' => 'Kyrgyzstan',
                'country_code' => '1109',
                'api_type' => 0,
                'sort' => 4080,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            407 => 
            array (
                'id' => 408,
                'name' => 'SFC Service',
                'code' => 'sfcservice',
                'code_num' => '190113',
                'country' => '',
                'country_code' => '0000',
                'api_type' => 0,
                'sort' => 4090,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            408 => 
            array (
                'id' => 409,
                'name' => 'Suto Logistics',
                'code' => 'sut56',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 4100,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            409 => 
            array (
                'id' => 410,
                'name' => 'Laos Post',
                'code' => 'laos-post',
                'code_num' => '12016',
                'country' => 'Laos',
                'country_code' => '1201',
                'api_type' => 0,
                'sort' => 4110,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            410 => 
            array (
                'id' => 411,
                'name' => 'Poslaju',
                'code' => 'poslaju',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 4120,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            411 => 
            array (
                'id' => 412,
                'name' => 'Latvia Post',
                'code' => 'latvijas-pasts',
                'code_num' => '12021',
                'country' => 'Latvia',
                'country_code' => '1202',
                'api_type' => 0,
                'sort' => 4130,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            412 => 
            array (
                'id' => 413,
                'name' => 'EC-Firstclass',
                'code' => 'ec-firstclass',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 4140,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            413 => 
            array (
                'id' => 414,
                'name' => 'YJI',
                'code' => 'yji',
                'code_num' => '190106',
                'country' => '',
                'country_code' => '0000',
                'api_type' => 0,
                'sort' => 4150,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            414 => 
            array (
                'id' => 415,
                'name' => 'DHL Global Forwarding',
                'code' => 'dhl-global-logistics',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 4160,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            415 => 
            array (
                'id' => 416,
                'name' => 'Lebanon Post',
                'code' => 'liban-post',
                'code_num' => '12031',
                'country' => 'Lebanon',
                'country_code' => '1203',
                'api_type' => 0,
                'sort' => 4170,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            416 => 
            array (
                'id' => 417,
                'name' => 'Safexpress',
                'code' => 'safexpress',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 4180,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            417 => 
            array (
                'id' => 418,
                'name' => 'Lesotho Post',
                'code' => 'lesotho-post',
                'code_num' => '12041',
                'country' => 'Lesotho',
                'country_code' => '1204',
                'api_type' => 0,
                'sort' => 4190,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            418 => 
            array (
                'id' => 419,
                'name' => 'DHL ECommerce',
                'code' => 'dhlglobalmail',
                'code_num' => '07047',
                'country' => 'Germany',
                'country_code' => '0704',
                'api_type' => 0,
                'sort' => 4200,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            419 => 
            array (
                'id' => 420,
                'name' => 'WeDo Logistics',
                'code' => 'wedo',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 4210,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            420 => 
            array (
                'id' => 421,
                'name' => 'SpeedPAK',
                'code' => 'speedpak',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 4220,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            421 => 
            array (
                'id' => 422,
                'name' => 'DPD UK',
                'code' => 'dpd-uk',
                'code_num' => '100010',
                'country' => '',
                'country_code' => '0000',
                'api_type' => 0,
                'sort' => 4230,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            422 => 
            array (
                'id' => 423,
                'name' => 'TAQBIN Malaysia',
                'code' => 'taqbin-my',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 4240,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            423 => 
            array (
                'id' => 424,
                'name' => 'TNT UK',
                'code' => 'tnt-uk',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 4250,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            424 => 
            array (
                'id' => 425,
                'name' => 'Zajil',
                'code' => 'zajil',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 4260,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            425 => 
            array (
                'id' => 426,
                'name' => 'Liechtenstein Post',
                'code' => 'liechtenstein-post',
                'code_num' => '12071',
                'country' => 'Liechtenstein',
                'country_code' => '1207',
                'api_type' => 0,
                'sort' => 4270,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            426 => 
            array (
                'id' => 427,
                'name' => 'Lithuania Post',
                'code' => 'lietuvos-pastas',
                'code_num' => '12081',
                'country' => 'Lithuania',
                'country_code' => '1208',
                'api_type' => 0,
                'sort' => 4280,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            427 => 
            array (
                'id' => 428,
            'name' => 'Lazada (LEX)',
                'code' => 'lgs',
                'code_num' => '100080',
                'country' => '',
                'country_code' => '0000',
                'api_type' => 0,
                'sort' => 4290,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            428 => 
            array (
                'id' => 429,
                'name' => 'Luxembourg Post',
                'code' => 'luxembourg-post',
                'code_num' => '12101',
                'country' => 'Luxembourg',
                'country_code' => '1210',
                'api_type' => 0,
                'sort' => 4300,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            429 => 
            array (
                'id' => 430,
                'name' => 'GLS Italy',
                'code' => 'gls-italy',
                'code_num' => '100024',
                'country' => '',
                'country_code' => '0000',
                'api_type' => 0,
                'sort' => 4310,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            430 => 
            array (
                'id' => 431,
                'name' => 'InPost Paczkomaty',
                'code' => 'inpost-paczkomaty',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 4320,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            431 => 
            array (
                'id' => 432,
                'name' => 'Shree Tirupati Courier',
                'code' => 'shree-tirupati',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 4330,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            432 => 
            array (
                'id' => 433,
                'name' => 'Dekun',
                'code' => 'dekun',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 4340,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            433 => 
            array (
                'id' => 434,
                'name' => 'DSV',
                'code' => 'dsv',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 4350,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            434 => 
            array (
                'id' => 435,
                'name' => 'Star Track Express',
                'code' => 'star-track',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 4360,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            435 => 
            array (
                'id' => 436,
                'name' => 'Pony Express',
                'code' => 'pony-express',
                'code_num' => '100037',
                'country' => '',
                'country_code' => '0000',
                'api_type' => 0,
                'sort' => 4370,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            436 => 
            array (
                'id' => 437,
                'name' => 'Macedonia Post',
                'code' => 'macedonia-post',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 4380,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            437 => 
            array (
                'id' => 438,
                'name' => 'Echo',
                'code' => 'echo',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 4390,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            438 => 
            array (
                'id' => 439,
                'name' => 'IML Logistics',
                'code' => 'imlb2c',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 4400,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            439 => 
            array (
                'id' => 440,
                'name' => 'DPD Ireland',
                'code' => 'dpd-ireland',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 4410,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            440 => 
            array (
                'id' => 441,
                'name' => 'EMPS Express',
                'code' => 'empsexpress',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 4420,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            441 => 
            array (
                'id' => 442,
                'name' => 'YMDD',
                'code' => 'yimidida',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 4430,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            442 => 
            array (
                'id' => 443,
                'name' => 'Toll IPEC',
                'code' => 'toll-ipec',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 4440,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            443 => 
            array (
                'id' => 444,
                'name' => 'OnTrac',
                'code' => 'ontrac',
                'code_num' => '100049',
                'country' => '',
                'country_code' => '0000',
                'api_type' => 0,
                'sort' => 4450,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            444 => 
            array (
                'id' => 445,
                'name' => 'HiveWMS',
                'code' => 'hivewms',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 4460,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            445 => 
            array (
                'id' => 446,
                'name' => 'Malaysia Post',
                'code' => 'malaysia-post',
                'code_num' => '13051',
                'country' => 'Malaysia',
                'country_code' => '1305',
                'api_type' => 0,
                'sort' => 4470,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            446 => 
            array (
                'id' => 447,
                'name' => 'Asendia USA',
                'code' => 'asendia-usa',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 4480,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            447 => 
            array (
                'id' => 448,
                'name' => 'CPacket',
                'code' => 'cpacket',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 4490,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            448 => 
            array (
                'id' => 449,
                'name' => 'ECPOST',
                'code' => 'ecpost',
                'code_num' => '190206',
                'country' => '',
                'country_code' => '0000',
                'api_type' => 0,
                'sort' => 4500,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            449 => 
            array (
                'id' => 450,
                'name' => 'Maldives Post',
                'code' => 'maldives-post',
                'code_num' => '13061',
                'country' => 'Maldives',
                'country_code' => '1306',
                'api_type' => 0,
                'sort' => 4510,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            450 => 
            array (
                'id' => 451,
                'name' => 'Asendia UK',
                'code' => 'asendia-uk',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 4520,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            451 => 
            array (
                'id' => 452,
                'name' => 'Cacesa Postal',
                'code' => 'cacesapostal',
                'code_num' => '100045',
                'country' => '',
                'country_code' => '0000',
                'api_type' => 0,
                'sort' => 4530,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            452 => 
            array (
                'id' => 453,
                'name' => 'Yodel',
                'code' => 'yodel',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 4540,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            453 => 
            array (
                'id' => 454,
                'name' => 'CRE',
                'code' => 'cre',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 4550,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            454 => 
            array (
                'id' => 455,
                'name' => 'OCS Worldwide',
                'code' => 'ocs-worldwide',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 4560,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            455 => 
            array (
                'id' => 456,
                'name' => 'Malta Post',
                'code' => 'malta-post',
                'code_num' => '13081',
                'country' => 'Malta',
                'country_code' => '1308',
                'api_type' => 0,
                'sort' => 4570,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            456 => 
            array (
                'id' => 457,
                'name' => 'Asendia Germany',
                'code' => 'asendia-de',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 4580,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            457 => 
            array (
                'id' => 458,
                'name' => 'Bonds Couriers',
                'code' => 'bondscouriers',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 4590,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            458 => 
            array (
                'id' => 459,
                'name' => 'Uskyexpress',
                'code' => 'uskyexpress',
                'code_num' => '190122',
                'country' => '',
                'country_code' => '0000',
                'api_type' => 0,
                'sort' => 4600,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            459 => 
            array (
                'id' => 460,
                'name' => 'Famiport',
                'code' => 'famiport',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 4610,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            460 => 
            array (
                'id' => 461,
                'name' => 'Estafeta USA',
                'code' => 'estafetausa',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 4620,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            461 => 
            array (
                'id' => 462,
                'name' => 'Kerry Express',
                'code' => 'kerry-logistics',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 4630,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            462 => 
            array (
                'id' => 463,
                'name' => 'Mauritius Post',
                'code' => 'mauritius-post',
                'code_num' => '13131',
                'country' => 'Mauritius',
                'country_code' => '1313',
                'api_type' => 0,
                'sort' => 4640,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            463 => 
            array (
                'id' => 464,
                'name' => 'CourierPost',
                'code' => 'courierpost',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 4650,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            464 => 
            array (
                'id' => 465,
                'name' => 'Ark express',
                'code' => 'arkexpress',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 4660,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            465 => 
            array (
                'id' => 466,
                'name' => 'Taiwan Pelican Express',
                'code' => 'e-can',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 4670,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            466 => 
            array (
                'id' => 467,
                'name' => 'Art Logexpress',
                'code' => 'artlogexpress',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 4680,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            467 => 
            array (
                'id' => 468,
                'name' => 'Moldova Post',
                'code' => 'moldova-post',
                'code_num' => '13161',
                'country' => 'Moldova',
                'country_code' => '1316',
                'api_type' => 0,
                'sort' => 4690,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            468 => 
            array (
                'id' => 469,
                'name' => 'Purolator',
                'code' => 'purolator',
                'code_num' => '100042',
                'country' => '',
                'country_code' => '0000',
                'api_type' => 0,
                'sort' => 4700,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            469 => 
            array (
                'id' => 470,
                'name' => 'ACOMMERCE',
                'code' => 'acommerce',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 4710,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            470 => 
            array (
                'id' => 471,
                'name' => 'Winlink logistics',
                'code' => 'winlink',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 4720,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            471 => 
            array (
                'id' => 472,
                'name' => 'Meest Express',
                'code' => 'meest',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 4730,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            472 => 
            array (
                'id' => 473,
                'name' => 'Trending Times',
                'code' => 'deltafille',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 4740,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            473 => 
            array (
                'id' => 474,
                'name' => 'Monaco Post',
                'code' => 'la-poste-monaco',
                'code_num' => '13171',
                'country' => 'Monaco',
                'country_code' => '1317',
                'api_type' => 0,
                'sort' => 4750,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            474 => 
            array (
                'id' => 475,
                'name' => '139 ECONOMIC Package',
                'code' => '139express',
                'code_num' => '190135',
                'country' => '',
                'country_code' => '0000',
                'api_type' => 0,
                'sort' => 4760,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            475 => 
            array (
                'id' => 476,
                'name' => 'DD Express',
                'code' => 'ddexpress',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 4770,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            476 => 
            array (
                'id' => 477,
                'name' => 'Monaco EMS',
                'code' => 'monaco-ems',
                'code_num' => '13171',
                'country' => 'Monaco',
                'country_code' => '1317',
                'api_type' => 0,
                'sort' => 4780,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            477 => 
            array (
                'id' => 478,
                'name' => 'Boxc Logistics',
                'code' => 'boxc',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 4790,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            478 => 
            array (
                'id' => 479,
                'name' => 'Mongol Post',
                'code' => 'mongol-post',
                'code_num' => '13181',
                'country' => 'Mongolia',
                'country_code' => '1318',
                'api_type' => 0,
                'sort' => 4800,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            479 => 
            array (
                'id' => 480,
                'name' => 'UBI Logistics',
                'code' => 'ubi-logistics',
                'code_num' => '190112',
                'country' => '',
                'country_code' => '0000',
                'api_type' => 0,
                'sort' => 4810,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            480 => 
            array (
                'id' => 481,
                'name' => 'Xpresspost',
                'code' => 'xpresspost',
                'code_num' => '03041',
                'country' => 'Canada',
                'country_code' => '0304',
                'api_type' => 0,
                'sort' => 4820,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            481 => 
            array (
                'id' => 482,
                'name' => 'DPD Romania',
                'code' => 'dpd-ro',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 4830,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            482 => 
            array (
                'id' => 483,
                'name' => 'Montenegro Post',
                'code' => 'posta-crne-gore',
                'code_num' => '13191',
                'country' => 'Montenegro',
                'country_code' => '1319',
                'api_type' => 0,
                'sort' => 4840,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            483 => 
            array (
                'id' => 484,
                'name' => 'Fastway New Zealand',
                'code' => 'fastway-nz',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 4850,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            484 => 
            array (
                'id' => 485,
                'name' => 'Ltian',
                'code' => 'ltian',
                'code_num' => '190274',
                'country' => '',
                'country_code' => '0000',
                'api_type' => 0,
                'sort' => 4860,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            485 => 
            array (
                'id' => 486,
                'name' => 'Maroc Poste',
                'code' => 'poste-maroc',
                'code_num' => '13211',
                'country' => 'Morocco',
                'country_code' => '1321',
                'api_type' => 0,
                'sort' => 4870,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            486 => 
            array (
                'id' => 487,
                'name' => 'DPEX',
                'code' => 'dpex',
                'code_num' => '100014',
                'country' => '',
                'country_code' => '0000',
                'api_type' => 0,
                'sort' => 4880,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            487 => 
            array (
                'id' => 488,
                'name' => 'Fastway Australia',
                'code' => 'fastway-au',
                'code_num' => '100044',
                'country' => '',
                'country_code' => '0000',
                'api_type' => 0,
                'sort' => 4890,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            488 => 
            array (
                'id' => 489,
                'name' => 'Direct Freight',
                'code' => 'directfreight-au',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 4900,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            489 => 
            array (
                'id' => 490,
                'name' => 'ePacket',
                'code' => 'epacket',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 4910,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            490 => 
            array (
                'id' => 491,
                'name' => 'Buffalo',
                'code' => 'buffaloex',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 4920,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            491 => 
            array (
                'id' => 492,
                'name' => 'RRS Logistics',
                'code' => 'rrs',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 4930,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            492 => 
            array (
                'id' => 493,
                'name' => 'Fastway Ireland',
                'code' => 'fastway-ie',
                'code_num' => '100068',
                'country' => '',
                'country_code' => '0000',
                'api_type' => 0,
                'sort' => 4940,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            493 => 
            array (
                'id' => 494,
                'name' => 'MRW',
                'code' => 'mrw-spain',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 4950,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            494 => 
            array (
                'id' => 495,
                'name' => 'USPS International',
                'code' => 'usps-international',
                'code_num' => '21051',
                'country' => 'United States',
                'country_code' => '2105',
                'api_type' => 0,
                'sort' => 4960,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            495 => 
            array (
                'id' => 496,
                'name' => 'SJTSZ Express',
                'code' => 'sjtsz',
                'code_num' => '190232',
                'country' => '',
                'country_code' => '0000',
                'api_type' => 0,
                'sort' => 4970,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            496 => 
            array (
                'id' => 497,
                'name' => 'Saia LTL Freight',
                'code' => 'saia-freight',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 4980,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            497 => 
            array (
                'id' => 498,
                'name' => 'Namibia Post',
                'code' => 'namibia-post',
                'code_num' => '14011',
                'country' => 'Namibia',
                'country_code' => '1401',
                'api_type' => 0,
                'sort' => 4990,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            498 => 
            array (
                'id' => 499,
                'name' => 'DTDC Plus',
                'code' => 'dtdc-plus',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 5000,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            499 => 
            array (
                'id' => 500,
                'name' => 'Packlink',
                'code' => 'packlink',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 5010,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
        ));
        \DB::table('directory_carriers')->insert(array (
            0 => 
            array (
                'id' => 501,
                'name' => 'KUAYUE EXPRESS',
                'code' => 'kye',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 5020,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            1 => 
            array (
                'id' => 502,
                'name' => 'Come One express',
                'code' => 'com1express',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 5030,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            2 => 
            array (
                'id' => 503,
                'name' => 'FERCAM Logistics & Transport',
                'code' => 'fercam',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 5040,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            3 => 
            array (
                'id' => 504,
                'name' => 'UPU',
                'code' => 'upu',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 5050,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            4 => 
            array (
                'id' => 505,
                'name' => 'Arrow XL',
                'code' => 'arrowxl',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 5060,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            5 => 
            array (
                'id' => 506,
                'name' => 'I-parcel',
                'code' => 'i-parcel',
                'code_num' => '100015',
                'country' => '',
                'country_code' => '0000',
                'api_type' => 0,
                'sort' => 5070,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            6 => 
            array (
                'id' => 507,
                'name' => 'Colis Privé',
                'code' => 'colis-prive',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 5080,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            7 => 
            array (
                'id' => 508,
                'name' => 'Fedex Freight',
                'code' => 'fedex-freight',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 5090,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            8 => 
            array (
                'id' => 509,
                'name' => 'Grand Slam Express',
                'code' => 'grandslamexpress',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 5100,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            9 => 
            array (
                'id' => 510,
                'name' => 'Antron Express',
                'code' => '168express',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 5110,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            10 => 
            array (
                'id' => 511,
                'name' => 'Bluedart',
                'code' => 'bluedart',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 5120,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            11 => 
            array (
                'id' => 512,
                'name' => 'Lasership',
                'code' => 'lasership',
                'code_num' => '100052',
                'country' => '',
                'country_code' => '0000',
                'api_type' => 0,
                'sort' => 5130,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            12 => 
            array (
                'id' => 513,
                'name' => 'Fast Express',
                'code' => 'kjkd',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 5140,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            13 => 
            array (
                'id' => 514,
                'name' => 'China Russia56',
                'code' => 'china-russia56',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 5150,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            14 => 
            array (
                'id' => 515,
                'name' => 'Fedex Ground',
                'code' => 'fedex-ground',
                'code_num' => '100003',
                'country' => '',
                'country_code' => '0000',
                'api_type' => 0,
                'sort' => 5160,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            15 => 
            array (
                'id' => 516,
                'name' => 'New Caledonia Post',
                'code' => 'new-caledonia-post',
                'code_num' => '97021',
                'country' => 'New Caledonia [FR]',
                'country_code' => '9702',
                'api_type' => 0,
                'sort' => 5170,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            16 => 
            array (
                'id' => 517,
                'name' => 'DTDC',
                'code' => 'dtdc',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 5180,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            17 => 
            array (
                'id' => 518,
                'name' => 'DMM Network',
                'code' => 'dmm-network',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 5190,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            18 => 
            array (
                'id' => 519,
                'name' => 'LiBang International Logistics',
                'code' => 'lbexps',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 5200,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            19 => 
            array (
                'id' => 520,
                'name' => 'Correos Express',
                'code' => 'correosexpress',
                'code_num' => '100048',
                'country' => '',
                'country_code' => '0000',
                'api_type' => 0,
                'sort' => 5210,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            20 => 
            array (
                'id' => 521,
                'name' => 'Nicaragua Post',
                'code' => 'nicaragua-post',
                'code_num' => '14071',
                'country' => 'Nicaragua',
                'country_code' => '1407',
                'api_type' => 0,
                'sort' => 5220,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            21 => 
            array (
                'id' => 522,
                'name' => 'GoJavas',
                'code' => 'gojavas',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 5230,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            22 => 
            array (
                'id' => 523,
                'name' => 'Fetchr',
                'code' => 'fetchr',
                'code_num' => '100083',
                'country' => '',
                'country_code' => '0000',
                'api_type' => 0,
                'sort' => 5240,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            23 => 
            array (
                'id' => 524,
                'name' => 'FedEx Poland Domestic',
                'code' => 'opek',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 5250,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            24 => 
            array (
                'id' => 525,
                'name' => 'XDP Express',
                'code' => 'xdp-uk',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 5260,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            25 => 
            array (
                'id' => 526,
                'name' => 'EFSPost',
                'code' => 'efspost',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 5270,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            26 => 
            array (
                'id' => 527,
                'name' => 'DHL Hong Kong',
                'code' => 'dhl-hong-kong',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 5280,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            27 => 
            array (
                'id' => 528,
                'name' => 'eTotal',
                'code' => 'etotal',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 5290,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            28 => 
            array (
                'id' => 529,
                'name' => 'FBB LOGISTICS',
                'code' => 'fbb',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 5300,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            29 => 
            array (
                'id' => 530,
                'name' => 'Nigeria Post',
                'code' => 'nigeria-post',
                'code_num' => '14101',
                'country' => 'Nigeria',
                'country_code' => '1410',
                'api_type' => 0,
                'sort' => 5310,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            30 => 
            array (
                'id' => 531,
                'name' => 'First Flight',
                'code' => 'first-flight',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 5320,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            31 => 
            array (
                'id' => 532,
                'name' => 'SkyNet Worldwide Express',
                'code' => 'skynetworldwide',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 5330,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            32 => 
            array (
                'id' => 533,
                'name' => 'SGT Corriere Espresso',
                'code' => 'sgt-it',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 5340,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            33 => 
            array (
                'id' => 534,
                'name' => 'Eyou800',
                'code' => 'eyoupost',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 5350,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            34 => 
            array (
                'id' => 535,
                'name' => 'UPS Freight',
                'code' => 'ups-freight',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 5360,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            35 => 
            array (
                'id' => 536,
                'name' => 'Gati-KWE',
                'code' => 'gati-kwe',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 5370,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            36 => 
            array (
                'id' => 537,
                'name' => 'IMEX Global Solutions',
                'code' => 'imexglobalsolutions',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 5380,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            37 => 
            array (
                'id' => 538,
                'name' => 'ELTA Courier',
                'code' => 'elta-courier-gr',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 5390,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            38 => 
            array (
                'id' => 539,
                'name' => 'Oman Post',
                'code' => 'oman-post',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 5400,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            39 => 
            array (
                'id' => 540,
                'name' => 'KGM Hub',
                'code' => 'kgmhub',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 5410,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            40 => 
            array (
                'id' => 541,
                'name' => 'UPS Ground',
                'code' => 'ups-ground',
                'code_num' => '100002',
                'country' => '',
                'country_code' => '0000',
                'api_type' => 0,
                'sort' => 5420,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            41 => 
            array (
                'id' => 542,
                'name' => 'UEQ',
                'code' => 'ueq',
                'code_num' => '190266',
                'country' => '',
                'country_code' => '0000',
                'api_type' => 0,
                'sort' => 5430,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            42 => 
            array (
                'id' => 543,
                'name' => 'Easy Mail',
                'code' => 'easy-mail',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 5440,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            43 => 
            array (
                'id' => 544,
                'name' => 'Qxpress',
                'code' => 'qxpress',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 5450,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            44 => 
            array (
                'id' => 545,
                'name' => 'IDEX',
                'code' => 'idexpress',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 5460,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            45 => 
            array (
                'id' => 546,
                'name' => 'FD Express',
                'code' => 'fd-express',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 5470,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            46 => 
            array (
                'id' => 547,
                'name' => 'UPS Mail Innovations',
                'code' => 'ups-mail-innovations',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 5480,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            47 => 
            array (
                'id' => 548,
                'name' => 'Hound Express',
                'code' => 'hound',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 5490,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            48 => 
            array (
                'id' => 549,
                'name' => 'ROSAN EXPRESS',
                'code' => 'rosan',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 5500,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            49 => 
            array (
                'id' => 550,
                'name' => 'RR Donnelley',
                'code' => 'rrdonnelley',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 5510,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            50 => 
            array (
                'id' => 551,
                'name' => 'eParcel Korea',
                'code' => 'eparcel-kr',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 5520,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            51 => 
            array (
                'id' => 552,
                'name' => 'TIPSA',
                'code' => 'tip-sa',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 5530,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            52 => 
            array (
                'id' => 553,
                'name' => 'WSGD Logistics',
                'code' => 'wsgd-logistics',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 5540,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            53 => 
            array (
                'id' => 554,
                'name' => 'Parcel Express',
                'code' => 'parcel-express',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 5550,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            54 => 
            array (
                'id' => 555,
                'name' => 'Con-way Freight',
                'code' => 'con-way',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 5560,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            55 => 
            array (
                'id' => 556,
                'name' => 'Ninja Van',
                'code' => 'ninjavan',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 5570,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            56 => 
            array (
                'id' => 557,
                'name' => 'ESHUN International Logistics',
                'code' => 'zes-express',
                'code_num' => '190075',
                'country' => '',
                'country_code' => '0000',
                'api_type' => 0,
                'sort' => 5580,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            57 => 
            array (
                'id' => 558,
                'name' => 'AnserX',
                'code' => 'anserx',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 5590,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            58 => 
            array (
                'id' => 559,
                'name' => 'SRE Korea',
                'code' => 'srekorea',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 5600,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            59 => 
            array (
                'id' => 560,
                'name' => 'Speedex Courier',
                'code' => 'speedexcourier',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 5610,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            60 => 
            array (
                'id' => 561,
                'name' => 'Sum Xpress',
                'code' => 'sumxpress',
                'code_num' => '190128',
                'country' => '',
                'country_code' => '0000',
                'api_type' => 0,
                'sort' => 5620,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            61 => 
            array (
                'id' => 562,
                'name' => 'Overseas Territory FR EMS',
                'code' => 'overseas-territory-fr-ems',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 5630,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            62 => 
            array (
                'id' => 563,
                'name' => 'Expeditors',
                'code' => 'expeditors',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 5640,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            63 => 
            array (
                'id' => 564,
                'name' => 'utec',
                'code' => 'utec',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 5650,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            64 => 
            array (
                'id' => 565,
                'name' => 'Auspost',
                'code' => 'auspost',
                'code_num' => '01151',
                'country' => 'Australia',
                'country_code' => '0115',
                'api_type' => 0,
                'sort' => 5660,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            65 => 
            array (
                'id' => 566,
                'name' => 'CHOICE Logistics',
                'code' => 'choice',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 5670,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            66 => 
            array (
                'id' => 567,
                'name' => 'Yamato Japan',
                'code' => 'taqbin-jp',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 5680,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            67 => 
            array (
                'id' => 568,
                'name' => 'SPSR',
                'code' => 'spsr',
                'code_num' => '100013',
                'country' => '',
                'country_code' => '0000',
                'api_type' => 0,
                'sort' => 5690,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            68 => 
            array (
                'id' => 569,
                'name' => 'SUNING',
                'code' => 'suning',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 5700,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            69 => 
            array (
                'id' => 570,
                'name' => 'Chronopost Portugal',
                'code' => 'chronopost-portugal',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 5710,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            70 => 
            array (
                'id' => 571,
                'name' => 'Flyway Express',
                'code' => 'flywayex',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 5720,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            71 => 
            array (
                'id' => 572,
                'name' => 'CNEX',
                'code' => 'jiaji',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 5730,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            72 => 
            array (
                'id' => 573,
                'name' => 'SGT Express',
                'code' => 'sgtwl',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 5740,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            73 => 
            array (
                'id' => 574,
                'name' => 'Sagawa',
                'code' => 'sagawa',
                'code_num' => '100040',
                'country' => '',
                'country_code' => '0000',
                'api_type' => 0,
                'sort' => 5750,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            74 => 
            array (
                'id' => 575,
                'name' => 'DPD Germany',
                'code' => 'dpd-de',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 5760,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            75 => 
            array (
                'id' => 576,
                'name' => 'XpressBees',
                'code' => 'xpressbees',
                'code_num' => '100101',
                'country' => '',
                'country_code' => '0000',
                'api_type' => 0,
                'sort' => 5770,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            76 => 
            array (
                'id' => 577,
                'name' => 'XDEXPRESS',
                'code' => 'xdexpress',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 5780,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            77 => 
            array (
                'id' => 578,
                'name' => 'ABX Express',
                'code' => 'abxexpress-my',
                'code_num' => '100064',
                'country' => '',
                'country_code' => '0000',
                'api_type' => 0,
                'sort' => 5790,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            78 => 
            array (
                'id' => 579,
                'name' => 'Courier IT',
                'code' => 'courier-it',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 5800,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            79 => 
            array (
                'id' => 580,
                'name' => 'JD Logistics',
                'code' => 'jd-logistics',
                'code_num' => '190302',
                'country' => '',
                'country_code' => '0000',
                'api_type' => 0,
                'sort' => 5810,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            80 => 
            array (
                'id' => 581,
                'name' => 'Specialised Freight',
                'code' => 'specialised-freight',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 5820,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            81 => 
            array (
                'id' => 582,
                'name' => 'venucia',
                'code' => 'qichen',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 5830,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            82 => 
            array (
                'id' => 583,
                'name' => 'COE',
                'code' => 'coe',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 5840,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            83 => 
            array (
                'id' => 584,
                'name' => 'Overseas Territory US Post',
                'code' => 'overseas-territory-us-post',
                'code_num' => '21051',
                'country' => 'United States',
                'country_code' => '2105',
                'api_type' => 0,
                'sort' => 5850,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            84 => 
            array (
                'id' => 585,
                'name' => 'Mypostonline',
                'code' => 'mypostonline',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 5860,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            85 => 
            array (
                'id' => 586,
                'name' => 'UPS Mail Innovations',
                'code' => 'ups-mi',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 5870,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            86 => 
            array (
                'id' => 587,
                'name' => 'Ledii',
                'code' => 'ledii',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 5880,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            87 => 
            array (
                'id' => 588,
                'name' => 'Line Clear Express & Logistics',
                'code' => 'line-clear',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 5890,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            88 => 
            array (
                'id' => 589,
                'name' => 'Pakistan Post',
                'code' => 'pakistan-post',
                'code_num' => '16011',
                'country' => 'Pakistan',
                'country_code' => '1601',
                'api_type' => 0,
                'sort' => 5900,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            89 => 
            array (
                'id' => 590,
                'name' => '13ten',
                'code' => '13-ten',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 5910,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            90 => 
            array (
                'id' => 591,
                'name' => 'SprintPack',
                'code' => 'sprintpack',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 5920,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            91 => 
            array (
                'id' => 592,
                'name' => 'Jam Express',
                'code' => 'jam-express',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 5930,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            92 => 
            array (
                'id' => 593,
                'name' => 'DPE South Africa',
                'code' => 'dpe-south-africa',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 5940,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            93 => 
            array (
                'id' => 594,
                'name' => 'Hanxuan international express',
                'code' => 'hxgj56',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 5950,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            94 => 
            array (
                'id' => 595,
                'name' => 'OCA Argentina',
                'code' => 'oca-ar',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 5960,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            95 => 
            array (
                'id' => 596,
                'name' => 'Dawn Wing',
                'code' => 'dawn-wing',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 5970,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            96 => 
            array (
                'id' => 597,
                'name' => '138sd',
                'code' => '138sd',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 5980,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            97 => 
            array (
                'id' => 598,
                'name' => 'lbex',
                'code' => 'lbex',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 5990,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            98 => 
            array (
                'id' => 599,
                'name' => 'Panama Post',
                'code' => 'correos-panama',
                'code_num' => '16031',
                'country' => 'Panama',
                'country_code' => '1603',
                'api_type' => 0,
                'sort' => 6000,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            99 => 
            array (
                'id' => 600,
            'name' => 'Jayon Express (JEX)',
                'code' => 'jayonexpress',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 6010,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            100 => 
            array (
                'id' => 601,
                'name' => 'Fastrak Services',
                'code' => 'fastrak-services',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 6020,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            101 => 
            array (
                'id' => 602,
                'name' => 'KJY Logistics',
                'code' => 'kjy',
                'code_num' => '190276',
                'country' => '',
                'country_code' => '0000',
                'api_type' => 0,
                'sort' => 6030,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            102 => 
            array (
                'id' => 603,
                'name' => 'Asendia',
                'code' => 'asendia',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 6040,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            103 => 
            array (
                'id' => 604,
                'name' => 'Papua New Guinea Post',
                'code' => 'postpng',
                'code_num' => '16041',
                'country' => 'Papua New Guinea',
                'country_code' => '1604',
                'api_type' => 0,
                'sort' => 6050,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            104 => 
            array (
                'id' => 605,
                'name' => 'Nova Poshta',
                'code' => 'nova-poshta',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 6060,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            105 => 
            array (
                'id' => 606,
                'name' => 'KWT Express',
                'code' => 'kwt56',
                'code_num' => '190054',
                'country' => '',
                'country_code' => '0000',
                'api_type' => 0,
                'sort' => 6070,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            106 => 
            array (
                'id' => 607,
                'name' => 'LHT Express',
                'code' => 'lhtex',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 6080,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            107 => 
            array (
                'id' => 608,
                'name' => 'Paraguay Post',
                'code' => 'correo-paraguayo',
                'code_num' => '16051',
                'country' => 'Paraguay',
                'country_code' => '1605',
                'api_type' => 0,
                'sort' => 6090,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            108 => 
            array (
                'id' => 609,
                'name' => 'RPX Indonesia',
                'code' => 'rpx',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 6100,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            109 => 
            array (
                'id' => 610,
                'name' => 'Euasia Express',
                'code' => 'euasia',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 6110,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            110 => 
            array (
                'id' => 611,
                'name' => 'Espost',
                'code' => 'espost',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 6120,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            111 => 
            array (
                'id' => 612,
                'name' => 'Serpost',
                'code' => 'serpost',
                'code_num' => '16061',
                'country' => 'Peru',
                'country_code' => '1606',
                'api_type' => 0,
                'sort' => 6130,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            112 => 
            array (
                'id' => 613,
                'name' => 'Wanb Express',
                'code' => 'wanbexpress',
                'code_num' => '190086',
                'country' => '',
                'country_code' => '0000',
                'api_type' => 0,
                'sort' => 6140,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            113 => 
            array (
                'id' => 614,
                'name' => 'Philippines Post',
                'code' => 'phlpost',
                'code_num' => '16071',
                'country' => 'Philippines',
                'country_code' => '1607',
                'api_type' => 0,
                'sort' => 6150,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            114 => 
            array (
                'id' => 615,
                'name' => '1DL Express',
                'code' => '1dlexpress',
                'code_num' => '190261',
                'country' => '',
                'country_code' => '0000',
                'api_type' => 0,
                'sort' => 6160,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            115 => 
            array (
                'id' => 616,
                'name' => 'Echindia',
                'code' => 'global-routers',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 6170,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            116 => 
            array (
                'id' => 617,
                'name' => 'Poland Post',
                'code' => 'poczta-polska',
                'code_num' => '16081',
                'country' => 'Poland',
                'country_code' => '1608',
                'api_type' => 0,
                'sort' => 6180,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            117 => 
            array (
                'id' => 618,
                'name' => 'Kawa',
                'code' => 'kawa',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 6190,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            118 => 
            array (
                'id' => 619,
                'name' => 'UVAN Express',
                'code' => 'uvan',
                'code_num' => '190238',
                'country' => '',
                'country_code' => '0000',
                'api_type' => 0,
                'sort' => 6200,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            119 => 
            array (
                'id' => 620,
                'name' => 'wiseloads',
                'code' => 'wiseloads',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 6210,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            120 => 
            array (
                'id' => 621,
                'name' => 'iquick fish',
                'code' => 'kfy',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 6220,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            121 => 
            array (
                'id' => 622,
                'name' => 'Nippon Express',
                'code' => 'nippon',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 6230,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            122 => 
            array (
                'id' => 623,
                'name' => 'UC Express',
                'code' => 'uc-express',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 6240,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            123 => 
            array (
                'id' => 624,
                'name' => 'wndirect',
                'code' => 'wndirect',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 6250,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            124 => 
            array (
                'id' => 625,
                'name' => 'Rwanda Post',
                'code' => 'iposita-rwanda',
                'code_num' => '18041',
                'country' => 'Rwanda',
                'country_code' => '1804',
                'api_type' => 0,
                'sort' => 6260,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            125 => 
            array (
                'id' => 626,
                'name' => 'Ninja Van Vietnam',
                'code' => 'ninjavan-vn',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 6270,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            126 => 
            array (
                'id' => 627,
                'name' => 'Eurodis',
                'code' => 'eurodis',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 6280,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            127 => 
            array (
                'id' => 628,
                'name' => 'GuangChi Express',
                'code' => 'guangchi',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 6290,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            128 => 
            array (
                'id' => 629,
                'name' => 'Saint Lucia Post',
                'code' => 'saint-lucia-post',
                'code_num' => '12091',
                'country' => 'Saint Lucia',
                'country_code' => '1209',
                'api_type' => 0,
                'sort' => 6300,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            129 => 
            array (
                'id' => 630,
                'name' => 'Matdespatch',
                'code' => 'matdespatch',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 6310,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            130 => 
            array (
                'id' => 631,
                'name' => 'Saint Vincent And The Grenadines',
                'code' => 'svgpost',
                'code_num' => '19021',
                'country' => 'Saint Vincent and Grenadines',
                'country_code' => '1902',
                'api_type' => 0,
                'sort' => 6320,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            131 => 
            array (
                'id' => 632,
                'name' => 'Samoa Post',
                'code' => 'samoa-post',
                'code_num' => '19281',
                'country' => 'Samoa',
                'country_code' => '1928',
                'api_type' => 0,
                'sort' => 6330,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            132 => 
            array (
                'id' => 633,
                'name' => 'TNT Australia',
                'code' => 'tnt-au',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 6340,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            133 => 
            array (
                'id' => 634,
                'name' => 'San Marino Post',
                'code' => 'san-marino-post',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 6350,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            134 => 
            array (
                'id' => 635,
                'name' => 'Alljoy',
                'code' => 'alljoy',
                'code_num' => '190163',
                'country' => '',
                'country_code' => '0000',
                'api_type' => 0,
                'sort' => 6360,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            135 => 
            array (
                'id' => 636,
                'name' => 'yakit',
                'code' => 'yakit',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 6370,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            136 => 
            array (
                'id' => 637,
                'name' => 'BirdSystem International Express',
                'code' => 'birdsystem',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 6380,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            137 => 
            array (
                'id' => 638,
                'name' => 'Saudi Post',
                'code' => 'saudi-post',
                'code_num' => '19071',
                'country' => 'Saudi Arabia',
                'country_code' => '1907',
                'api_type' => 0,
                'sort' => 6390,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            138 => 
            array (
                'id' => 639,
                'name' => 'Szendex',
                'code' => 'szendex',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 6400,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            139 => 
            array (
                'id' => 640,
                'name' => 'Senegal Post',
                'code' => 'senegal-post',
                'code_num' => '19081',
                'country' => 'Senegal',
                'country_code' => '1908',
                'api_type' => 0,
                'sort' => 6410,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            140 => 
            array (
                'id' => 641,
                'name' => 'WEL',
                'code' => 'logistics',
                'code_num' => '190170',
                'country' => '',
                'country_code' => '0000',
                'api_type' => 0,
                'sort' => 6420,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            141 => 
            array (
                'id' => 642,
                'name' => 'Serbia Post',
                'code' => 'serbia-post',
                'code_num' => '19091',
                'country' => 'Serbia',
                'country_code' => '1909',
                'api_type' => 0,
                'sort' => 6430,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            142 => 
            array (
                'id' => 643,
                'name' => 'TAQBIN HongKong',
                'code' => 'taqbin-hk',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 6440,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            143 => 
            array (
                'id' => 644,
                'name' => 'Spoton Logistics',
                'code' => 'spoton',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 6450,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            144 => 
            array (
                'id' => 645,
                'name' => 'Seychelles Post',
                'code' => 'seychelles-post',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 6460,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            145 => 
            array (
                'id' => 646,
                'name' => 'Un-line',
                'code' => 'un-line',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 6470,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            146 => 
            array (
                'id' => 647,
                'name' => 'UBon  Express',
                'code' => 'ubonex',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 6480,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            147 => 
            array (
                'id' => 648,
                'name' => 'Dachser',
                'code' => 'dachser',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 6490,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            148 => 
            array (
                'id' => 649,
                'name' => 'BAB international',
                'code' => 'bab-ru',
                'code_num' => '190076',
                'country' => '',
                'country_code' => '0000',
                'api_type' => 0,
                'sort' => 6500,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            149 => 
            array (
                'id' => 650,
                'name' => 'Profit Fields',
                'code' => '8dt',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 6510,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            150 => 
            array (
                'id' => 651,
                'name' => 'TopYou',
                'code' => 'topyou',
                'code_num' => '190074',
                'country' => '',
                'country_code' => '0000',
                'api_type' => 0,
                'sort' => 6520,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            151 => 
            array (
                'id' => 652,
                'name' => 'Slovakia Post',
                'code' => 'slovakia-post',
                'code_num' => '19141',
                'country' => 'Slovakia',
                'country_code' => '1914',
                'api_type' => 0,
                'sort' => 6530,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            152 => 
            array (
                'id' => 653,
                'name' => 'CNILINK',
                'code' => 'cnilink',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 6540,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            153 => 
            array (
                'id' => 654,
                'name' => 'CSD Express',
                'code' => 'csd',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 6550,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            154 => 
            array (
                'id' => 655,
                'name' => 'Slovenia Post',
                'code' => 'slovenia-post',
                'code_num' => '19151',
                'country' => 'Slovenia',
                'country_code' => '1915',
                'api_type' => 0,
                'sort' => 6560,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            155 => 
            array (
                'id' => 656,
                'name' => '2U Express',
                'code' => '2uex',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 6570,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            156 => 
            array (
                'id' => 657,
                'name' => 'SAP Express',
                'code' => 'sap-express',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 6580,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            157 => 
            array (
                'id' => 658,
                'name' => 'Solomon Post',
                'code' => 'solomon-post',
                'code_num' => '19161',
                'country' => 'Solomon Islands',
                'country_code' => '1916',
                'api_type' => 0,
                'sort' => 6590,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            158 => 
            array (
                'id' => 659,
                'name' => 'HKD',
                'code' => 'hkdexpress',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 6600,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            159 => 
            array (
                'id' => 660,
                'name' => 'COSCO eGlobal',
                'code' => 'cosco',
                'code_num' => '190138',
                'country' => '',
                'country_code' => '0000',
                'api_type' => 0,
                'sort' => 6610,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            160 => 
            array (
                'id' => 661,
                'name' => 'Ane Express',
                'code' => 'ane66',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 6620,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            161 => 
            array (
                'id' => 662,
                'name' => 'Huida Express',
                'code' => 'huidaex',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 6630,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            162 => 
            array (
                'id' => 663,
                'name' => 'A PLUS EXPRESS',
                'code' => 'aplus100',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 6640,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            163 => 
            array (
                'id' => 664,
                'name' => 'OTHER',
                'code' => 'other',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 9999,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            164 => 
            array (
                'id' => 665,
                'name' => 'XingYunYi',
                'code' => 'xingyunyi',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 2000,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            165 => 
            array (
                'id' => 666,
                'name' => 'PostaPlus',
                'code' => 'postaplus',
                'code_num' => '100058',
                'country' => '',
                'country_code' => '0000',
                'api_type' => 0,
                'sort' => 2100,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            166 => 
            array (
                'id' => 667,
                'name' => '1SD',
                'code' => '1shida',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 2140,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
            167 => 
            array (
                'id' => 668,
                'name' => 'ZTO International',
                'code' => 'zto-international',
                'code_num' => '01051',
                'country' => 'Andorra',
                'country_code' => '0105',
                'api_type' => 0,
                'sort' => 2290,
                'created_at' => '2020-10-09 19:56:53',
                'updated_at' => NULL,
            ),
        ));
        
        
    }
}