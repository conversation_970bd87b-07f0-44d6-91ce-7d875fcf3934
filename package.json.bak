{"private": true, "scripts": {"dev": "npm run development", "development": "node node_modules/webpack/bin/webpack.js --progress --hide-modules --config=node_modules/laravel-mix/setup/webpack.config.js", "watch": "npm run development -- --watch", "watch-poll": "npm run watch -- --watch-poll", "hot": "node node_modules/webpack-dev-server/bin/webpack-dev-server.js --inline --hot --config=node_modules/laravel-mix/setup/webpack.config.js", "prod": "npm run production", "production": "node node_modules/webpack/bin/webpack.js --progress --hide-modules --config=node_modules/laravel-mix/setup/webpack.config.js"}, "dependencies": {"@ckeditor/ckeditor5-alignment": "^11.2.0", "@ckeditor/ckeditor5-build-balloon": "^12.4.0", "@ckeditor/ckeditor5-build-balloon-block": "^12.4.0", "@ckeditor/ckeditor5-build-classic": "^12.4.0", "@ckeditor/ckeditor5-build-decoupled-document": "^12.4.0", "@ckeditor/ckeditor5-build-inline": "^12.4.0", "@fortawesome/fontawesome-free": "^5.12.1", "@fullcalendar/bootstrap": "^4.4.2", "@fullcalendar/core": "^4.4.2", "@fullcalendar/daygrid": "^4.4.2", "@fullcalendar/google-calendar": "^4.4.2", "@fullcalendar/interaction": "^4.4.2", "@fullcalendar/list": "^4.4.2", "@fullcalendar/timegrid": "^4.4.2", "@popperjs/core": "^2.4.2", "@shopify/draggable": "^1.0.0-beta.8", "@uppy/core": "^1.11.0", "@uppy/progress-bar": "^1.3.15", "@uppy/tus": "^1.6.0", "@yaireo/tagify": "3.7.0", "animate.css": "^3.5.2", "apexcharts": "^3.19.2", "autosize": "^4.0.2", "block-ui": "^2.70.1", "bootstrap": "^4.5.0", "bootstrap-datepicker": "^1.9.0", "bootstrap-daterangepicker": "^3.1.0", "bootstrap-datetime-picker": "^2.4.4", "bootstrap-markdown": "^2.10.0", "bootstrap-maxlength": "^1.6.0", "bootstrap-notify": "^3.1.3", "bootstrap-select": "1.13.14", "bootstrap-switch": "3.3.4", "bootstrap-timepicker": "^0.5.2", "bootstrap-touchspin": "^4.2.5", "clipboard": "^2.0.4", "counterup": "^1.0.2", "cropperjs": "^1.5.7", "datatables.net-autofill-bs4": "^2.3.4", "datatables.net-bs4": "^1.10.20", "datatables.net-buttons-bs4": "^1.6.1", "datatables.net-colreorder-bs4": "^1.5.2", "datatables.net-fixedcolumns-bs4": "^3.3.0", "datatables.net-fixedheader-bs4": "^3.1.6", "datatables.net-keytable-bs4": "^2.5.1", "datatables.net-responsive-bs4": "^2.2.5", "datatables.net-rowgroup-bs4": "^1.1.1", "datatables.net-rowreorder-bs4": "^1.2.6", "datatables.net-scroller-bs4": "^2.0.1", "datatables.net-select-bs4": "^1.3.1", "dropzone": "^5.7.1", "dual-listbox": "1.1.0", "es6-promise": "^4.2.8", "es6-promise-polyfill": "^1.2.0", "es6-shim": "^0.35.5", "esri-leaflet": "^2.4.1", "esri-leaflet-geocoder": "^2.3.3", "flot": "^3.2.2", "gmaps": "^0.4.24", "handlebars": "^4.7.6", "inputmask": "^4.0.6", "ion-rangeslider": "^2.3.1", "jkanban": "^1.2.1", "jquery": "3.4.1", "jquery-form": "^4.3.0", "jquery-validation": "1.19.0", "jquery.repeater": "^1.2.1", "jqvmap": "^1.5.1", "jstree": "^3.3.10", "jszip": "^3.5.0", "leaflet": "^1.6.0", "line-awesome": "^1.3.0", "markdown": "^0.5.0", "moment": "^2.26.0", "morris.js": "^0.5.0", "nouislider": "^14.1.1", "owl.carousel": "^2.2.0", "pace": "0.0.4", "pace-js": "^1.0.2", "pdfmake": "^0.1.63", "perfect-scrollbar": "^1.5.0", "popper.js": "^1.14.7", "prismjs": "^1.18.0", "quill": "^1.3.6", "raphael": "^2.2.7", "select2": "^4.0.12", "socicon": "^3.0.5", "sticky-js": "^1.3.0", "stream-exhaust": "^1.0.2", "summernote": "0.8.12", "sweetalert2": "^9.15.1", "tagify": "^0.1.1", "tether": "^1.4.3", "tinymce": "^5.3.2", "toastr": "^2.1.4", "tooltip.js": "^1.2.0", "typeahead.js": "^0.11.1", "underscore": "^1.8.3", "uppy": "^1.16.0", "waypoints": "^4.0.1", "whatwg-fetch": "^3.0.0", "wnumb": "^1.1.0"}, "devDependencies": {"axios": "^0.19", "laravel-mix": "^5.0.1", "lodash": "^4.17.13", "replace-in-file-webpack-plugin": "^1.0.6", "resolve-url-loader": "^3.1.0", "sass": "^1.26.8", "sass-loader": "^8.0.2", "vue-template-compiler": "^2.6.11"}}