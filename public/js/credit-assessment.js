function generateTableRowHtml(data, index) {
    var html = '';

    html += '<tr class="has-many-table-mcc_table-form fields-group" data-select2-id="select2-data-' + index + '-mcc_table">';

        // 第一列：MCC（只读）
        html += '<td>';
            html += '<div class="form-group row form-field">';
                html += '<div class="col-md-0 text-capitalize hidden control-label">';
                    html += '<span>MCC</span>';
                html += '</div>';
                html += '<div class="col-md-12">';
                    html += '<div class="help-block with-errors"></div>';
                    html += '<div class="input-group">';
                        html += '<span class="input-group-prepend"><span class="input-group-text bg-white"><i class="feather icon-edit-2"></i></span></span>';
                        html += '<input build-ignore="1" readonly type="text"';
                            html += ' name="mcc_table[' + index + '][mcc]"';
                            html += ' value="' + (data.mcc || '') + '"';
                            html += ' class="form-control field_mcc_table_' + index + ' field_mcc_' + index + ' field_mcc"';
                            html += ' placeholder="输入 MCC">';
                        html += '</input>';
                    html += '</div>';
                html += '</div>';
            html += '</div>';
        html += '</td>';

        // 第二列：MCC描述（只读 textarea）
        html += '<td>';
            html += '<div class="form-group row form-field">';
                html += '<label class="col-md-0 text-capitalize hidden control-label">MCC描述</label>';
                html += '<div class="col-md-12">';
                    html += '<div class="help-block with-errors"></div>';
                    html += '<textarea';
                        html += ' name="mcc_table[' + index + '][mcc_description]"';
                        html += ' class="form-control field_mcc_table_' + index + ' field_mcc_description_' + index + ' field_mcc_description"';
                        html += ' rows="5"';
                        html += ' placeholder="输入 MCC描述"';
                        html += ' build-ignore="1"';
                        html += ' readonly>';
                        html += (data.mcc_description || '');
                    html += '</textarea>';
                html += '</div>';
            html += '</div>';
        html += '</td>';

        // 第三列：信用风险等级（下拉框 select2）
        html += '<td data-select2-id="select2-data-' + index + '-risk-level">';
            html += '<div class="form-group row form-field">';
                html += '<div class="col-md-0 text-capitalize hidden control-label">';
                    html += '<span>信用风险等级</span>';
                html += '</div>';
                html += '<div class="col-md-12">';
                    html += '<div class="help-block with-errors"></div>';
                    html += '<input type="hidden" name="mcc_table[' + index + '][credit_risk_rating]">';
                    html += '<select class="form-control field_mcc_table_' + index + ' field_credit_risk_rating_' + index + ' field_credit_risk_rating"';
                        html += ' style="width: 100%"';
                        html += ' name="mcc_table[' + index + '][credit_risk_rating]"';
                        html += ' build-ignore="1"';
                        html += ' data-value="' + (data.credit_risk_rating || '') + '"';
                        html += '>';
                        html += '<option value=""></option>';
                        html += '<option value="Low"' + (data.credit_risk_rating === 'Low' ? ' selected' : '') + '>Low</option>';
                        html += '<option value="Medium"' + (data.credit_risk_rating === 'Medium' ? ' selected' : '') + '>Medium</option>';
                        html += '<option value="High"' + (data.credit_risk_rating === 'High' ? ' selected' : '') + '>High</option>';
                        html += '<option value="Restricted"' + (data.credit_risk_rating === 'Restricted' ? ' selected' : '') + '>Restricted</option>';
                        html += '<option value="Prohibited"' + (data.credit_risk_rating === 'Prohibited' ? ' selected' : '') + '>Prohibited</option>';
                    html += '</select>';
                html += '</div>';
            html += '</div>';
        html += '</td>';

        // 第四列：商品交付天数（可编辑 input）
        html += '<td>';
            html += '<div class="form-group row form-field">';
                html += '<div class="col-md-0 text-capitalize asterisk hidden control-label">';
                    html += '<span>商品交付天数</span>';
                html += '</div>';
                html += '<div class="col-md-12">';
                    html += '<div class="help-block with-errors"></div>';
                    html += '<div class="input-group">';
                        html += '<span class="input-group-prepend"><span class="input-group-text bg-white"><i class="feather icon-edit-2"></i></span></span>';
                        html += '<input build-ignore="1" type="number" required="1"';
                            html += ' name="mcc_table[' + index + '][delivery_days]"';
                            html += ' value="' + (data.delivery_days || '') + '"';
                            html += ' class="form-control field_mcc_table_' + index + ' field_delivery_days_' + index + ' field_delivery_days"';
                            html += ' placeholder="输入 商品交付天数">';
                        html += '</input>';
                    html += '</div>';
                html += '</div>';
            html += '</div>';
        html += '</td>';

        // 第五列：APV百分比（数值范围 1~100）
        html += '<td>';
            html += '<div class="form-group row form-field">';
                html += '<div class="col-md-0 text-capitalize asterisk hidden control-label">';
                    html += '<span>APV百分比（%）</span>';
                html += '</div>';
                html += '<div class="col-md-12">';
                    html += '<div class="help-block with-errors"></div>';
                    html += '<div class="input-group">';
                        html += '<span class="input-group-prepend"><span class="input-group-text bg-white"><i class="feather icon-edit-2"></i></span></span>';
                        html += '<input build-ignore="1" type="number" required="1" min="1" max="100"';
                            html += ' name="mcc_table[' + index + '][percentage_of_apv]"';
                            html += ' value="' + (data.percentage_of_apv || '') + '"';
                            html += ' class="form-control field_mcc_table_' + index + ' field_percentage_of_apv_' + index + ' field_percentage_of_apv"';
                            html += ' placeholder="输入 APV百分比（%）">';
                        html += '</input>';
                    html += '</div>';
                    html += '<span class="help-block">';
                        html += '<i class="fa feather icon-help-circle"></i>&nbsp;累计APV百分比必须等于100%';
                    html += '</span>';
                html += '</div>';
            html += '</div>';
        html += '</td>';

        // 第六列：商户网站（URL 输入）
        html += '<td>';
            html += '<div class="form-group row form-field">';
                html += '<div class="col-md-0 text-capitalize hidden control-label">';
                    html += '<span>商户网站</span>';
                html += '</div>';
                html += '<div class="col-md-12">';
                    html += '<div class="help-block with-errors"></div>';
                    html += '<div class="input-group">';
                        html += '<span class="input-group-prepend"><span class="input-group-text bg-white"><i class="feather icon-edit-2"></i></span></span>';
                        html += '<input build-ignore="1" required="1"';
                            html += ' name="mcc_table[' + index + '][merchant_website]"';
                            html += ' value="' + (data.merchant_website || '') + '"';
                            html += ' class="form-control field_mcc_table_' + index + ' field_merchant_website_' + index + ' field_merchant_website"';
                            html += ' placeholder="输入 商户网站">';
                        html += '</input>';
                    html += '</div>';
                html += '</div>';
            html += '</div>';
        html += '</td>';

        // 第七列：隐藏字段（id 和 _remove_）
        html += '<td class="hidden">';
            html += '<input type="hidden" name="mcc_table[' + index + '][id]" value="' + (data.id || '') + '" class="field_mcc_table_' + index + ' field_id_' + index + ' field_id" build-ignore="1">';
            html += '<input type="hidden" name="mcc_table[' + index + '][_remove_]" value="0" class="field_mcc_table_' + index + ' field__remove__' + index + ' field__remove_ form-removed _normal_" build-ignore="1">';
        html += '</td>';

    html += '</tr>';

    return html;
}

function generateNdxDetailsRow(data, index) {
    var html = '';

    // 开始 tr 标签
    html += '<tr class="has-many-table-ndx_details-form fields-group">';

        // 第一列：标题（title）
        html += '<td>';
            html += '<div class="form-group row form-field">';
                html += '<div class="col-md-0 text-capitalize hidden control-label">';
                    html += '<span>风险明细</span>';
                html += '</div>';
                html += '<div class="col-md-12">';
                    html += '<div class="help-block with-errors"></div>';
                    html += '<div class="input-group">';
                        html += '<span class="input-group-prepend"><span class="input-group-text bg-white"><i class="feather icon-edit-2"></i></span></span>';
                        html += '<input build-ignore="1" readonly type="text"';
                            html += ' name="ndx_details[' + index + '][title]"';
                            html += ' value="' + (data.title || '') + '"';
                            html += ' class="form-control field_ndx_details_' + index + ' field_title_' + index + ' field_title"';
                            html += ' placeholder="输入 风险明细">';
                        html += '</input>';
                    html += '</div>';
                html += '</div>';
            html += '</div>';
        html += '</td>';

        // 第二列：天数（days）
        html += '<td>';
            html += '<div class="form-group row form-field">';
                html += '<div class="col-md-0 text-capitalize asterisk hidden control-label">';
                    html += '<span>天数</span>';
                html += '</div>';
                html += '<div class="col-md-12">';
                    html += '<div class="help-block with-errors"></div>';
                    html += '<div class="input-group">';
                        html += '<span class="input-group-prepend"><span class="input-group-text bg-white"><i class="feather icon-edit-2"></i></span></span>';
                        html += '<input build-ignore="1" type="number" readonly required="1"';
                            html += ' name="ndx_details[' + index + '][days]"';
                            html += ' value="' + (data.days || '') + '"';
                            html += ' class="form-control field_ndx_details_' + index + ' field_days_' + index + ' field_days"';
                            html += ' placeholder="输入 天数">';
                        html += '</input>';
                    html += '</div>';
                html += '</div>';
            html += '</div>';
        html += '</td>';

        // 第三列：比率（rate）
        html += '<td>';
            html += '<div class="form-group row form-field">';
                html += '<div class="col-md-0 text-capitalize hidden control-label">';
                    html += '<span>比率</span>';
                html += '</div>';
                html += '<div class="col-md-12">';
                    html += '<div class="help-block with-errors"></div>';
                    html += '<div class="input-group">';
                        html += '<span class="input-group-prepend"><span class="input-group-text bg-white"><i class="feather icon-edit-2"></i></span></span>';
                        html += '<input build-ignore="1" readonly type="number"';
                            html += ' name="ndx_details[' + index + '][rate]"';
                            html += ' value="' + (data.rate || '') + '"';
                            html += ' class="form-control field_ndx_details_' + index + ' field_rate_' + index + ' field_rate"';
                            html += ' placeholder="输入 比率">';
                        html += '</input>';
                    html += '</div>';
                html += '</div>';
            html += '</div>';
        html += '</td>';

        // 第四列：风险金额（amount）
        html += '<td>';
            html += '<div class="form-group row form-field">';
                html += '<div class="col-md-0 text-capitalize hidden control-label">';
                    html += '<span>风险金额</span>';
                html += '</div>';
                html += '<div class="col-md-12">';
                    html += '<div class="help-block with-errors"></div>';
                    html += '<div class="input-group">';
                        html += '<span class="input-group-prepend"><span class="input-group-text bg-white"><i class="feather icon-edit-2"></i></span></span>';
                        html += '<input build-ignore="1" readonly type="number" step="0.01"';
                            html += ' name="ndx_details[' + index + '][amount]"';
                            html += ' value="' + (data.amount || '') + '"';
                            html += ' class="form-control field_ndx_details_' + index + ' field_amount_' + index + ' field_amount"';
                            html += ' placeholder="输入 风险金额">';
                        html += '</input>';
                    html += '</div>';
                html += '</div>';
            html += '</div>';
        html += '</td>';

        // 第五列：隐藏字段 _remove_
        html += '<td class="hidden">';
            html += '<input type="hidden" name="ndx_details[' + index + '][_remove_]" value=""';
                html += ' class="field_ndx_details_' + index + ' field__remove__' + index + ' field__remove_ form-removed _normal_" build-ignore="1">';
            html += '</input>';
        html += '</td>';

    // 结束 tr 标签
    html += '</tr>';

    return html;
}

(function ($) {
    window.CreditAssessmentFormHandler = function (formSelector) {
        const handler = {};

        let $form = null;

        // NDX表格固定字段索引值
        const unDeliveryIndex = 0;
        const unDeliveryDaysIndex = 1;
        const unDeliveryAmountIndex = 3;
        const RefundRiskIndex = -3;
        const RefundDaysIndex = 1;
        const RefundRateIndex = 2;
        const RefundAmountIndex = 3;
        const ChargebackRiskIndex = -2;
        const ChargebackDaysIndex = 1;
        const ChargebackRateIndex = 2;
        const ChargebackAmountIndex = 3;
        const TotalRiskIndex = -1;
        const TotalAmountIndex = 3;

        // 配置项：字段选择器配置
        const config = {
            selectors: {
                mccSelect: 'select[name="mcc_id[]"]',
                annualProcessingVolume: 'input[name="annual_processing_volume"]',
                chargebackRate: 'input[name="chargeback_rate"]',
                refundRate: 'input[name="refund_rate"]',
                annualNetRevenue: 'input[name="annual_net_revenue"]',
                exposureCoverage: 'input[name="exposure_coverage"]',
                rollingReserveTimeframe: 'input[name="rolling_reserve_timeframe"]',
                averageTransactionValue: 'input[name="average_transaction_value"]',
                totalShareWallet: 'input[name="total_share_wallet"]',
                settlementFrequency: 'input[name="settlement_frequency"]',
                dailyCaptureInput: 'input[name="daily_capture"]',
                fullRangeCaptureInput: 'input[name="full_timeframe_capture"]',
                breakEvenTimeInput: 'input[name="break_even_time"]',
                totalExposureInput: 'input[name="total_exposure"]',
                totalFixedReserveInput: 'input[name="total_fixed_reserve"]',
                rollingReserveInput: 'input[name="rolling_reserve"]'
            },
            tbody: {
                mccTable: 'tbody.has-many-table-mcc_table-forms',
                ndxDetails: 'tbody.has-many-table-ndx_details-forms'
            }
        };

        // 工具函数：是否为整数（基于 Decimal）
        function isInteger(value) {
            if (value === null || value === undefined || value === '') return false;

            try {
                const decimal = new Decimal(value);
                return decimal.isInteger() && decimal.isFinite();
            } catch (e) {
                return false;
            }
        }

        // 工具函数：是否为合法数字（基于 Decimal）
        function isNumber(value) {
            if (value === null || value === undefined || value === '') return false;

            try {
                const decimal = new Decimal(value);
                return decimal.isFinite();
            } catch (e) {
                return false;
            }
        }

        // 截断到两位小数（Decimal 版本）
        function truncateTo2(num) {
            const factor = new Decimal(100);
            return new Decimal(num).times(factor).floor().dividedBy(factor).toNumber();
        }

        // 校验正整数输入
        function validatePositiveInteger($input, errorMessage) {
            const value = $input.val();
            if (!isInteger(value)) {
                Dcat.swal.error('错误', errorMessage);
                $input.val('');
                return false;
            }

            if (new Decimal(value).lessThanOrEqualTo(0)) {
                Dcat.swal.error('错误', errorMessage);
                $input.val('');
                return false;
            }

            return true;
        }

        // 校验最多两位小数的正数
        function validatePositiveNumber($input, errorMessage) {
            const value = $input.val();
            if (!isNumber(value)) {
                Dcat.swal.error('错误', errorMessage);
                $input.val('');
                return false;
            }

            if (new Decimal(value).lessThanOrEqualTo(0)) {
                Dcat.swal.error('错误', errorMessage);
                $input.val('');
                return false;
            }

            const regex = /^(?!0\d)(\d+)(\.\d{1,2})?$/;
            if (!regex.test(value)) {
                Dcat.swal.error('错误', errorMessage);
                $input.val('');
                return false;
            }

            return true;
        }

        // 计算百分比之和
        function calculateTotalPercentage(row) {
            let total = new Decimal(0);
            let allFilled = true;

            $form.find('.field_percentage_of_apv').each(function () {
                const val = $(this).val();

                if (!isNumber(val)) {
                    allFilled = false;
                    return false; // break
                }

                total = total.plus(new Decimal(val));
            });

            if (allFilled) {
                if (!total.equals(100)) {
                    Dcat.swal.error('错误', '百分比之和必须为100');
                    row.val('');
                    return false;
                } else {
                    return true;
                }
            }

            return false;
        }

        // 计算未交付风险天数
        function calculateUnDeliveredRiskDays() {
            const $tbodyTable = $form.find(config.tbody.mccTable);
            const $tbodyNdxDetails = $form.find(config.tbody.ndxDetails);

            const deliveryDaysList = [];
            const apvPercentageList = [];
            let allFilled = true;

            $tbodyTable.find('tr').each(function () {
                const $row = $(this);
                const days = $row.find('.field_delivery_days').val();
                const percentage = $row.find('.field_percentage_of_apv').val();

                if (!isInteger(days) || !isNumber(percentage)) {
                    allFilled = false;
                    return false; // break
                }

                deliveryDaysList.push(new Decimal(days));
                apvPercentageList.push(new Decimal(percentage));
            });

            if (!allFilled) {
                const selector = `tr:eq(${unDeliveryIndex}) td:eq(${unDeliveryDaysIndex}) .field_days`;
                $tbodyNdxDetails.find(selector).val('');
                return;
            }

            const weightedSum = deliveryDaysList.reduce((sum, days, i) => sum.plus(days.times(apvPercentageList[i].dividedBy(100))), new Decimal(0));

            const selector = `tr:eq(${unDeliveryIndex}) td:eq(${unDeliveryDaysIndex}) .field_days`;
            $tbodyNdxDetails.find(selector).val(Math.ceil(weightedSum.toNumber()));
        }

        // 插入 MCC 风险详情行
        function insertNewNdxMccDetail(row) {
            if (!(row instanceof jQuery) || !row.is('tr')) return;

            const index = row.index();
            const mccCode = row.find(`td:eq(0) .field_mcc`).val();
            const deliveryDays = row.find(`.field_delivery_days`).val();
            const percentageOfApv = row.find(`.field_percentage_of_apv`).val();
            const annualVolume = $form.find(config.selectors.annualProcessingVolume).val();

            let riskAmount = '';
            if (
                isInteger(deliveryDays) &&
                isNumber(percentageOfApv) &&
                isInteger(annualVolume)
            ) {
                riskAmount = new Decimal(deliveryDays)
                    .dividedBy(365)
                    .times(new Decimal(annualVolume))
                    .times(new Decimal(percentageOfApv).dividedBy(100));
                riskAmount = truncateTo2(riskAmount);
            }

            const html = generateNdxDetailsRow({
                title: mccCode,
                days: deliveryDays,
                rate: '',
                amount: riskAmount
            }, index + 1);

            $form.find(config.tbody.ndxDetails).append(html);
        }

        // 计算 MCC 对应的风险金额
        function calculateMccRiskAmount(row) {
            const $tbodyNdxDetails = $form.find(config.tbody.ndxDetails);
            const tr = $tbodyNdxDetails.find(`tr:eq(${row.index() + 1})`);

            const deliveryDays = row.find('td:eq(3) .field_delivery_days').val(); // input 输入框
            const percentageOfApv = row.find('td:eq(4) .field_percentage_of_apv').val(); // input 输入框
            const annualProcessingVolume = $form.find(config.selectors.annualProcessingVolume).val();

            let result = '';
            if (isInteger(deliveryDays) && isNumber(percentageOfApv) && isInteger(annualProcessingVolume)) {
                result = new Decimal(deliveryDays)
                    .dividedBy(365)
                    .times(new Decimal(percentageOfApv).dividedBy(100))
                    .times(new Decimal(annualProcessingVolume));
                result = truncateTo2(result);
            }

            tr.find('td:eq(3) .field_amount').val(result);
        }

        // 同步拒退率
        function syncChargebackRate() {
            const $tbodyNdxDetails = $form.find(config.tbody.ndxDetails);
            const selector = `tr:eq(${ChargebackRiskIndex}) td:eq(${ChargebackRateIndex}) .field_rate`;
            $tbodyNdxDetails.find(selector).val(
                $form.find(config.selectors.chargebackRate).val()
            );
        }

        // 同步退款率
        function syncRefundRate() {
            const $tbodyNdxDetails = $form.find(config.tbody.ndxDetails);
            const selector = `tr:eq(${RefundRiskIndex}) td:eq(${RefundRateIndex}) .field_rate`;
            $tbodyNdxDetails.find(selector).val(
                $form.find(config.selectors.refundRate).val()
            );
        }

        // 计算未交付风险金额
        function calculateUnDeliveredRiskAmount() {
            const $tbodyNdxDetails = $form.find(config.tbody.ndxDetails);
            const riskDaysTd = $tbodyNdxDetails.find(`tr:eq(${unDeliveryIndex}) td:eq(${unDeliveryDaysIndex}) .field_days`);
            const riskAmountTd = $tbodyNdxDetails.find(`tr:eq(${unDeliveryIndex}) td:eq(${unDeliveryAmountIndex}) .field_amount`);
            const annualProcessingVolume = $form.find(config.selectors.annualProcessingVolume).val();
            const riskDays = riskDaysTd.val();

            if (!isNumber(riskDays)) {
                riskAmountTd.val('');
                return;
            }

            if (!isInteger(annualProcessingVolume)) {
                Dcat.swal.error('错误', '年处理量请输入有效的整数');
                riskAmountTd.val('');
                return;
            }

            console.log(riskDays, annualProcessingVolume, riskAmountTd);

            const calculated = new Decimal(riskDays)
                .dividedBy(365)
                .times(new Decimal(annualProcessingVolume));
            riskAmountTd.val(truncateTo2(calculated));
        }

        // 计算退款风险金额
        function calculateRefundRiskAmount() {
            const $tbodyNdxDetails = $form.find(config.tbody.ndxDetails);
            const refundRowSelector = `tr:eq(${RefundRiskIndex})`;

            const refundRiskDaysTd = $tbodyNdxDetails.find(`${refundRowSelector} td:eq(${RefundDaysIndex}) .field_days`);
            const refundRiskRateTd = $tbodyNdxDetails.find(`${refundRowSelector} td:eq(${RefundRateIndex}) .field_rate`);
            const refundRiskAmountTd = $tbodyNdxDetails.find(`${refundRowSelector} td:eq(${RefundAmountIndex}) .field_amount`);
            const annualProcessingVolume = $form.find(config.selectors.annualProcessingVolume).val();

            const refundRiskDays = refundRiskDaysTd.val();
            const refundRiskRate = refundRiskRateTd.val();

            let riskAmount = '';
            if (
                isInteger(refundRiskDays) &&
                isNumber(refundRiskRate) &&
                isInteger(annualProcessingVolume)
            ) {
                riskAmount = new Decimal(refundRiskRate)
                    .dividedBy(100)
                    .times(new Decimal(annualProcessingVolume))
                    .times(new Decimal(refundRiskDays).dividedBy(365));
                riskAmount = truncateTo2(riskAmount);
            }

            refundRiskAmountTd.val(riskAmount);
        }

        // 计算拒付风险金额
        function calculateChargebackRiskAmount() {
            const $tbodyNdxDetails = $form.find(config.tbody.ndxDetails);
            const chargebackRowSelector = `tr:eq(${ChargebackRiskIndex})`;

            const chargebackRiskAmountTd = $tbodyNdxDetails.find(`${chargebackRowSelector} td:eq(${ChargebackAmountIndex}) .field_amount`);
            const chargebackRiskRateTd = $tbodyNdxDetails.find(`${chargebackRowSelector} td:eq(${ChargebackRateIndex}) .field_rate`);
            const chargebackRiskDaysTd = $tbodyNdxDetails.find(`${chargebackRowSelector} td:eq(${ChargebackDaysIndex}) .field_days`);
            const annualProcessingVolume = $form.find(config.selectors.annualProcessingVolume).val();

            const chargebackRiskDays = chargebackRiskDaysTd.val();
            const chargebackRiskRate = chargebackRiskRateTd.val();

            let riskAmount = '';
            if (
                isInteger(chargebackRiskDays) &&
                isNumber(chargebackRiskRate) &&
                isInteger(annualProcessingVolume)
            ) {
                riskAmount = new Decimal(chargebackRiskRate)
                    .dividedBy(100)
                    .times(new Decimal(annualProcessingVolume))
                    .times(new Decimal(chargebackRiskDays).dividedBy(365));
                riskAmount = truncateTo2(riskAmount);
            }

            chargebackRiskAmountTd.val(riskAmount);
        }

        // 计算总风险金额
        function calculateTotalRiskAmount() {
            const $tbodyNdxDetails = $form.find(config.tbody.ndxDetails);
            let total = new Decimal(0);
            let exit = false;

            const length = $tbodyNdxDetails.find('tr').length;
            $tbodyNdxDetails.find('tr').each(function (index) {
                if (index === (length - 1)) return;

                const $row = $(this);
                const riskAmount = $row.find(`td:eq(3) .field_amount`).val();

                if (isNumber(riskAmount)) {
                    total = total.plus(new Decimal(riskAmount));
                } else {
                    exit = true;
                    return false;
                }
            });

            const $totalExposureInput = $form.find(config.selectors.totalExposureInput);
            if (exit) {
                $tbodyNdxDetails.find(`tr:eq(${TotalRiskIndex}) td:eq(${TotalAmountIndex}) .field_amount`).val('');
                $totalExposureInput.val('');
                return;
            }

            const finalTotal = truncateTo2(total);
            $tbodyNdxDetails.find(`tr:eq(${TotalRiskIndex}) td:eq(${TotalAmountIndex}) .field_amount`).val(finalTotal);
            $totalExposureInput.val(finalTotal);
        }

        // 计算盈亏平衡时间
        function calculateProfitBalanceTime() {
            const totalRisk = $form.find(config.selectors.totalExposureInput).val();
            const coverage = $form.find(config.selectors.exposureCoverage).val();
            const annualRevenue = $form.find(config.selectors.annualNetRevenue).val();

            const profitBalanceTimeInput = $form.find(config.selectors.breakEvenTimeInput);

            if (!isNumber(totalRisk) || !isNumber(coverage) || !isInteger(annualRevenue)) {
                profitBalanceTimeInput.val('');
                return;
            }

            const t = new Decimal(totalRisk);
            const c = new Decimal(coverage);
            const r = new Decimal(annualRevenue);

            if (r.equals(0)) {
                profitBalanceTimeInput.val('');
                return;
            }

            const yearsFloat = t.minus(c.dividedBy(100)).dividedBy(r);
            let years = yearsFloat.floor();
            let months = yearsFloat.minus(years).times(12).ceil();
            if (months.equals(12)) {
                years = yearsFloat.plus(1).floor();
                months = new Decimal(0);
            }

            profitBalanceTimeInput.val(`${years.toNumber()} yrs ${months.toNumber()} mths`);
        }

        // 计算固定保证金
        function calculateFixedDeposit() {
            const totalExposure = $form.find(config.selectors.totalExposureInput).val();
            const exposureCoverage = $form.find(config.selectors.exposureCoverage).val();

            const totalFlexReserveInput = $form.find(config.selectors.totalFixedReserveInput);

            if (!isNumber(totalExposure) || !isNumber(exposureCoverage)) {
                totalFlexReserveInput.val('');
                return;
            }

            const totalExposureNumber = new Decimal(totalExposure);
            const exposureCoverageNumber = new Decimal(exposureCoverage);
            const totalFixedReserve = totalExposureNumber.times(exposureCoverageNumber.dividedBy(100));

            totalFlexReserveInput.val(truncateTo2(totalFixedReserve));
        }

        // 计算滚动储备百分比
        function calculateRollingReservePercentage() {
            const totalFlexReserve = $form.find(config.selectors.totalFixedReserveInput).val();
            const rollingReserveTimeframe = $form.find(config.selectors.rollingReserveTimeframe).val();
            const annualProcessingVolume = $form.find(config.selectors.annualProcessingVolume).val();
            const rollingReserveInput = $form.find(config.selectors.rollingReserveInput);

            if (!isNumber(totalFlexReserve) || !isInteger(rollingReserveTimeframe) || !isInteger(annualProcessingVolume)) {
                rollingReserveInput.val('');
                return;
            }

            const weeklyRiskAmount = new Decimal(annualProcessingVolume)
                .dividedBy(365)
                .times(7)
                .times(new Decimal(rollingReserveTimeframe));
            if (weeklyRiskAmount.lessThanOrEqualTo(0)) {
                rollingReserveInput.val('');
                return;
            }

            const ratio = new Decimal(totalFlexReserve).dividedBy(weeklyRiskAmount);
            const roundedRatio = ratio.dividedBy(0.005).round().times(0.005);
            rollingReserveInput.val(roundedRatio.times(100).toNumber());
        }

        // 计算每日捕获金额
        function calculateDailyCaptureAmount() {
            const rollingReserve = $form.find(config.selectors.rollingReserveInput).val();
            const annualProcessingVolume = $form.find(config.selectors.annualProcessingVolume).val();
            const dailyCaptureInput = $form.find(config.selectors.dailyCaptureInput);

            if (!isNumber(rollingReserve) || !isInteger(annualProcessingVolume)) {
                dailyCaptureInput.val('');
                return;
            }

            const dailyCaptureAmount = new Decimal(rollingReserve)
                .dividedBy(100)
                .times(new Decimal(annualProcessingVolume))
                .dividedBy(365);
            dailyCaptureInput.val(truncateTo2(dailyCaptureAmount));
        }

        // 计算完整范围捕获金额
        function calculateFullRangeCaptureAmount() {
            const dailyCapture = $form.find(config.selectors.dailyCaptureInput).val();
            const rollingReserveTimeframe = $form.find(config.selectors.rollingReserveTimeframe).val();
            const fullRangeCaptureInput = $form.find(config.selectors.fullRangeCaptureInput);

            if (!isNumber(dailyCapture) || !isInteger(rollingReserveTimeframe)) {
                fullRangeCaptureInput.val('');
                return;
            }

            const fullRangeCaptureAmount = new Decimal(dailyCapture).times(new Decimal(rollingReserveTimeframe).times(7));
            fullRangeCaptureInput.val(truncateTo2(fullRangeCaptureAmount));
        }

        // 更新未交付风险数据
        function updateUndeliveredRiskData() {
            calculateUnDeliveredRiskDays();
            calculateUnDeliveredRiskAmount();
        }

        // 更新所有风险相关数据
        function updateTotalRiskAndExposureData() {
            calculateTotalRiskAmount();
            calculateProfitBalanceTime();
            calculateFixedDeposit();
            calculateRollingReservePercentage();
            calculateDailyCaptureAmount();
            calculateFullRangeCaptureAmount();
        }

        // 解绑所有事件
        function unbindAllEvents() {
            const $tbodyTable = $form.find(config.tbody.mccTable);
            const $tbodyNdxDetails = $form.find(config.tbody.ndxDetails);

            $form.find(config.selectors.mccSelect).off('change');
            $form.find(config.selectors.annualProcessingVolume).off('change');
            $form.find(config.selectors.chargebackRate).off('change');
            $form.find(config.selectors.refundRate).off('change');
            $form.find(config.selectors.annualNetRevenue).off('change');
            $form.find(config.selectors.exposureCoverage).off('change');
            $form.find(config.selectors.rollingReserveTimeframe).off('change');
            $form.find(config.selectors.averageTransactionValue).off('change');
            $form.find(config.selectors.totalShareWallet).off('change');
            $form.find(config.selectors.settlementFrequency).off('change');
            $form.find('input[type=number]').off('wheel');

            $tbodyTable.off('change', '.field_percentage_of_apv');
            $tbodyTable.off('change', '.field_delivery_days');
            $tbodyNdxDetails.off('change', `tr:eq(${ChargebackRiskIndex}) td:eq(${ChargebackDaysIndex}) .field_days`);
            $tbodyNdxDetails.off('change', `tr:eq(${RefundRiskIndex}) td:eq(${RefundDaysIndex}) .field_days`);
        }

        // 绑定所有事件
        function bindEvents() {
            const $tbodyTable = $form.find(config.tbody.mccTable);
            const $tbodyNdxDetails = $form.find(config.tbody.ndxDetails);

            // 封装 selector 以提高可读性
            const chargebackRowSelector = `tr:eq(${ChargebackRiskIndex})`;
            const refundRowSelector = `tr:eq(${RefundRiskIndex})`;

            // 监听 MCC 多选框变化
            $form.find(config.selectors.mccSelect).off('change').on('change', function () {
                const currentMccTableData = [];

                $tbodyTable.find('tr').each(function () {
                    const $row = $(this);
                    currentMccTableData.push({
                        mcc: $row.find('td:eq(0) .field_mcc').val(),
                        mcc_description: $row.find('td:eq(1) .field_mcc_description').val(),
                        credit_risk_rating: $row.find('td:eq(2) .field_credit_risk_rating').val(),
                        delivery_days: $row.find('td:eq(3) .field_delivery_days').val(),
                        percentage_of_apv: $row.find('td:eq(4) .field_percentage_of_apv').val(),
                        merchant_website: $row.find('td:eq(5) .field_merchant_website').val(),
                        id: $row.find('td:eq(6) .field_id').val()
                    });
                });

                $.ajax({
                    url: '/admin/risk_control/risk_credit_assessments/process_mcc_table',
                    type: 'post',
                    data: {
                        mcc: $(this).val(),
                        currentMcc: currentMccTableData,
                    },
                    success: function (data) {
                        $tbodyTable.find('tr').remove();
                        const $rows = $tbodyNdxDetails.find('tr');
                        $tbodyNdxDetails.empty();

                        for (let i = 0; i < data.length; i++) {
                            const itemTd = generateTableRowHtml(data[i], i);
                            $tbodyTable.append(itemTd);
                        }

                        // 添加第一行
                        if ($rows.length > 0) {
                            $tbodyNdxDetails.append($rows.first());
                        }

                        // 插入 MCC 风险详情行
                        $tbodyTable.find('tr').each(function () {
                            insertNewNdxMccDetail($(this));
                        });

                        // 新的ndxDetails的行数
                        const ndxDetailsLength = $tbodyNdxDetails.find('tr').length;
                        // 添加最后三行
                        if ($rows.length >= 4) {
                            $rows.slice(-3).each(function (index) {
                                const html = generateNdxDetailsRow({
                                    title: $(this).find('td:eq(0) .field_title').val(),
                                    days: $(this).find('td:eq(1) .field_days').val(),
                                    rate: $(this).find('td:eq(2) .field_rate').val(),
                                    amount: $(this).find('td:eq(3) .field_amount').val()
                                }, ndxDetailsLength + index);

                                $tbodyNdxDetails.append(html);
                            });
                        } else {
                            Dcat.swal.error('NDX详情数据不正确，本次无法进行审核');
                        }

                        updateUndeliveredRiskData();
                        updateTotalRiskAndExposureData();
                    }
                });
            });

            // 百分比改变
            $tbodyTable.off('change', '.field_percentage_of_apv').on('change', '.field_percentage_of_apv', function () {
                if (!validatePositiveNumber($(this), '只能为最多两位小数的正数')) return;
                calculateMccRiskAmount($(this).closest('tr'));

                if (!calculateTotalPercentage($(this))) return;

                updateUndeliveredRiskData();
                updateTotalRiskAndExposureData();
            });

            // 商品交付天数改变
            $tbodyTable.off('change', '.field_delivery_days').on('change', '.field_delivery_days', function () {
                if (!validatePositiveInteger($(this), '请输入有效的正整数')) return;

                const tr = $tbodyNdxDetails.find(`tr:eq(${$(this).closest('tr').index() + 1})`);
                tr.find(`td:eq(${unDeliveryDaysIndex}) .field_days`).val($(this).val());

                calculateMccRiskAmount($(this).closest('tr'));
                updateUndeliveredRiskData();
                updateTotalRiskAndExposureData();
            });

            // 年处理量改变
            $form.find(config.selectors.annualProcessingVolume).off('change').on('change', function () {
                if (!validatePositiveInteger($(this), '请输入有效的正整数')) return;

                $tbodyTable.find('tr').each(function () {
                    calculateMccRiskAmount($(this));
                });

                calculateUnDeliveredRiskAmount();
                calculateChargebackRiskAmount();
                updateTotalRiskAndExposureData();
            });

            // 拒退率改变
            $form.find(config.selectors.chargebackRate).off('change').on('change', function () {
                if (!validatePositiveNumber($(this), '只能为最多两位小数的正数')) return;
                syncChargebackRate();
                calculateChargebackRiskAmount();
                updateTotalRiskAndExposureData();
            });

            // 退款率改变
            $form.find(config.selectors.refundRate).off('change').on('change', function () {
                if (!validatePositiveNumber($(this), '只能为最多两位小数的正数')) return;
                syncRefundRate();
                calculateRefundRiskAmount();
                updateTotalRiskAndExposureData();
            });

            // 拒付风险天数改变
            $tbodyNdxDetails.off('change', `${chargebackRowSelector} td:eq(${ChargebackDaysIndex}) .field_days`)
                .on('change', `${chargebackRowSelector} td:eq(${ChargebackDaysIndex}) .field_days`, function () {
                    if (!validatePositiveInteger($(this), '请输入有效的正整数')) return;
                    calculateChargebackRiskAmount();
                    updateTotalRiskAndExposureData();
                });

            // 退款风险天数改变
            $tbodyNdxDetails.off('change', `${refundRowSelector} td:eq(${RefundDaysIndex}) .field_days`)
                .on('change', `${refundRowSelector} td:eq(${RefundDaysIndex}) .field_days`, function () {
                    if (!validatePositiveInteger($(this), '请输入有效的正整数')) return;
                    calculateRefundRiskAmount();
                    updateTotalRiskAndExposureData();
                });

            // 年净收入改变
            $form.find(config.selectors.annualNetRevenue).off('change').on('change', function () {
                if (!validatePositiveNumber($(this), '只能为最多两位小数的正数')) return;
                $form.find('div.field_annual_net_revenue_display').text($(this).val());
                calculateProfitBalanceTime();
            });

            // 风险覆盖率改变
            $form.find(config.selectors.exposureCoverage).off('change').on('change', function () {
                if (!validatePositiveNumber($(this), '只能为最多两位小数的正数')) return;
                calculateProfitBalanceTime();
                calculateFixedDeposit();
                calculateRollingReservePercentage();
                calculateDailyCaptureAmount();
                calculateFullRangeCaptureAmount();
            });

            // 循环保证金结算范围改变
            $form.find(config.selectors.rollingReserveTimeframe).off('change').on('change', function () {
                if (!validatePositiveInteger($(this), '请输入有效的正整数')) return;
                calculateRollingReservePercentage();
                calculateDailyCaptureAmount();
                calculateFullRangeCaptureAmount();
            });

            // 平均交易金额改变
            $form.find(config.selectors.averageTransactionValue).off('change').on('change', function () {
                validatePositiveNumber($(this), '只能为最多两位小数的正数');
            });

            // 收单机构总占比改变
            $form.find(config.selectors.totalShareWallet).off('change').on('change', function () {
                validatePositiveNumber($(this), '只能为最多两位小数的正数');
            });

            // 结算频率改变
            $form.find(config.selectors.settlementFrequency).off('change').on('change', function () {
                validatePositiveInteger($(this), '只能为正整数');
            });

            // 阻止 input[type=number] 的滚轮行为（事件委托方式）
            $form.off('wheel', 'input[type=number]').on('wheel', 'input[type=number]', function (e) {
                e.preventDefault();
            });
        }

        // 初始化方法
        handler.init = function () {
            $form = $(formSelector);
            if ($form.length === 0) {
                Dcat.swal.warning(`表单 ${formSelector} 不存在`);
                return;
            }

            unbindAllEvents();
            bindEvents();

            $form.find('div.field_annual_net_revenue_display').text(
                $form.find(config.selectors.annualNetRevenue).val()
            ).val();
        };

        return handler;
    };

})(jQuery);

// Dcat Admin 入口
Dcat.ready(function () {
    const form = new CreditAssessmentFormHandler('form#' + window.creditAssessmentConfig.data);
    form.init();
});
