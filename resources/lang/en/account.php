<?php

use App\Models\TransferAccount;

return [
    'labels' => [
        '提现账户' => 'Withdrawal Account',
        'currency_type_map' => [
            'CNY', 'ForeignCurrency', 'Crypto'
        ],
    ],
    'fields' => [
        'bank_name'                                         => 'Deposit Bank',
        'bank_name' . TransferAccount::TYPE_DOMESTIC        => 'Deposit Bank',
        'bank_name' . TransferAccount::TYPE_ABROAD          => 'Deposit Bank',
        'cardholder' . TransferAccount::TYPE_DOMESTIC       => 'Cardholder name',
        'cardholder' . TransferAccount::TYPE_ABROAD         => 'Cardholder name',
        'bank_account' . TransferAccount::TYPE_DOMESTIC     => 'Bank account',
        'bank_account' . TransferAccount::TYPE_ABROAD       => 'Bank account',
        'bank_account' . TransferAccount::TYPE_CRYPTO       => 'Crypto address',
        'bank_account'                                      => 'Bank account/Crypto address',
        'bank_address' . TransferAccount::TYPE_DOMESTIC     => 'Deposit Bank Address',
        'bank_address' . TransferAccount::TYPE_ABROAD       => 'Deposit Bank Address',
        'bank_address'                                      => 'Deposit Bank Address',
        'nickname'                                          => 'Account Aliases',
        'convert_currency'                                  => 'Disbursement Currency Type',
        'convert_currency' . TransferAccount::TYPE_DOMESTIC => 'Disbursement Currency Type',
        'convert_currency' . TransferAccount::TYPE_ABROAD   => 'Disbursement Currency Type',
        'convert_currency' . TransferAccount::TYPE_CRYPTO   => 'Disbursement Currency Type',
        'swift_iban'                                        => 'SWIFT/IBAN',
        'tip' => [
            'rmb_support'                     => 'CNY accounts only support personal accounts',
            'not_rmb_support'                 => 'Foreign currency accounts only support corporate accounts',
            'bank_name'                       => 'eg：xx Bank xx Branch xx Subbranch',
            'bank_mobile'                     => 'Bank bank reserved mobile number cannot be blank',
            'bank_mobile_placeholder'         => 'Bank card reserved mobile number',
            'bank_mobile_error'               => 'Wrong format of bank reserved mobile number',
            'bank_address_null_error'         => 'The address of deposit bank cannot be blank',
            'bank_null_error'                 => 'Deposit bank cannot be blank',
            'bank_account_null_error'         => 'Bank account number cannot be empty',
            'crypto_address_null_error'       => 'Crypto address cannot be empty',
            'inter_bank_number'               => 'The inter-bank number cannot be empty',
            'cardholder_id_card'              => 'Cardholder ID number cannot be blank',
            'cardholder_id_name'              => 'Cardholder name cannot be blank',
            'cardholder_id_card_error'        => 'Cardholder ID card number format error',
            'disbursement_currency'           => 'Disbursement currency cannot be empty',
            'bank_english_null_error'         => 'Deposit bank (English) cannot be blank',
            'bank_address_english_null_error' => 'Deposit bank address (English) cannot be blank',
            'account_name_null_error'         => 'Account name cannot be empty',
            'company_address_null_error'      => 'Company address (English) cannot be empty',
        ],
        'cannot_be_empty' => 'cannot be empty',
    ],
    'options' => [
        'status_merchant_map' => [
            '审核中' => 'Under review',
            '请重新提交' => 'Please resubmit',
            '已过审' => 'Reviewed',
        ],
        'currency_type_map' => [
            '人民币' => 'CNY',
            '外币' => 'ForeignCurrency',
        ],
    ],
];
