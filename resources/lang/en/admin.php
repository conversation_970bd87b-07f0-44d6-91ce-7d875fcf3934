<?php

return [
    'scaffold'                  => [
        'header'                        => 'Scaffold',
        'choose'                        => 'choose',
        'table'                         => 'Table',
        'model'                         => 'Model',
        'controller'                    => 'Controller',
        'repository'                    => 'Repository',
        'add_field'                     => 'Add field',
        'pk'                            => 'Primary key',
        'soft_delete'                   => 'Soft delete',
        'create_migration'              => 'Create migration',
        'create_model'                  => 'Create model',
        'create_repository'             => 'Create repository',
        'create_controller'             => 'Create controller',
        'run_migrate'                   => 'Run migrate',
        'create_lang'                   => 'Create lang',
        'field'                         => 'field',
        'translation'                   => 'translation',
        'comment'                       => 'comment',
        'default'                       => 'default',
        'field_name'                    => 'field name',
        'type'                          => 'type',
        'nullable'                      => 'nullable',
        'key'                           => 'key',
        'translate_title'               => 'Translate Title',
        'sync_translation_with_comment' => 'Sync translation and comment',
    ],
    'client'                    => [
        'delete_confirm'    => 'Are you sure to delete this item ?',
        'confirm'           => 'Confirm',
        'cancel'            => 'Cancel',
        'refresh_succeeded' => 'Refresh succeeded !',
        'close'             => 'Close',
        'selected_options'  => ':num options selected',
        'exceed_max_item'   => 'Maximum items exceeded.',
        'no_preview'        => 'No preview available.',

        '500' => 'Internal server error !',
        '403' => 'Permission deny !',
        '401' => 'Unauthorized !',
        '419' => 'Page expired !',
    ],
    'online'                    => 'Online',
    'login'                     => 'Login',
    'logout'                    => 'Logout',
    'setting'                   => 'Setting',
    'name'                      => 'Name',
    'username'                  => 'Username',
    'user'                      => 'User',
    'alias'                     => 'Alias',
    'routes'                    => 'Routes',
    'route_action'              => 'Route Action',
    'middleware'                => 'Middleware',
    'method'                    => 'Method',
    'old_password'              => 'Old password',
    'password'                  => 'Password',
    'password_confirmation'     => 'Password confirmation',
    'old_password_error'        => 'Incorrect password',
    'remember_me'               => 'Remember me',
    'user_setting'              => 'User setting',
    'avatar'                    => 'Avatar',
    'list'                      => 'List',
    'new'                       => 'New',
    'create'                    => 'Create',
    'delete'                    => 'Delete',
    'remove'                    => 'Remove',
    'edit'                      => 'Edit',
    'quick_edit'                => 'Quick Edit',
    'view'                      => 'View',
    'continue_editing'          => 'Continue editing',
    'continue_creating'         => 'Continue creating',
    'detail'                    => 'Detail',
    'browse'                    => 'Browse',
    'reset'                     => 'Reset',
    'export'                    => 'Export',
    'batch_delete'              => 'Batch delete',
    'save'                      => 'Save',
    'refresh'                   => 'Refresh',
    'order'                     => 'Order',
    'expand'                    => 'Expand',
    'collapse'                  => 'Collapse',
    'filter'                    => 'Filter',
    'search'                    => 'Search',
    'close'                     => 'Close',
    'show'                      => 'Show',
    'entries'                   => 'entries',
    'captcha'                   => 'Captcha',
    'action'                    => 'Action',
    'title'                     => 'Title',
    'description'               => 'Description',
    'back'                      => 'Back',
    'back_to_list'              => 'Back to List',
    'submit'                    => 'Submit',
    'menu'                      => 'Menu',
    'input'                     => 'Input',
    'succeeded'                 => 'Succeeded',
    'failed'                    => 'Failed',
    'delete_confirm'            => 'Are you sure to delete this item ?',
    'delete_succeeded'          => 'Delete succeeded !',
    'delete_failed'             => 'Delete failed !',
    'update_succeeded'          => 'Update succeeded !',
    'update_failed'             => 'Update failed !',
    'save_succeeded'            => 'Save succeeded !',
    'save_failed'               => 'Save failed !',
    'refresh_succeeded'         => 'Refresh succeeded !',
    'login_successful'          => 'Login successful',
    'choose'                    => 'Choose',
    'choose_file'               => 'Select file',
    'choose_image'              => 'Select image',
    'more'                      => 'More',
    'deny'                      => 'Permission denied',
    'administrator'             => 'Administrator',
    'no_data'                   => 'No data.',
    'roles'                     => 'Roles',
    'permissions'               => 'Permissions',
    'slug'                      => 'Slug',
    'created_at'                => 'Created At',
    'updated_at'                => 'Updated At',
    'alert'                     => 'Alert',
    'parent_id'                 => 'Parent',
    'icon'                      => 'Icon',
    'uri'                       => 'URI',
    'operation_log'             => 'Operation log',
    'parent_select_error'       => 'Parent select error',
    'tree'                      => 'Tree',
    'table'                     => 'Table',
    'default'                   => 'Default',
    'import'                    => 'Import',
    'is_not_import'             => 'No',
    'selected_options'          => ':num options selected',
    'pagination'                => [
        'range' => 'Showing :first to :last of :total entries',
    ],
    'role'                      => 'Role',
    'permission'                => 'Permission',
    'route'                     => 'Route',
    'confirm'                   => 'Confirm',
    'cancel'                    => 'Cancel',
    'selectall'                 => 'Select all',
    'http'                      => [
        'method' => 'HTTP method',
        'path'   => 'HTTP path',
    ],
    'all_methods_if_empty'      => 'All methods if empty',
    'all'                       => 'All',
    'current_page'              => 'Current page',
    'selected_rows'             => 'Selected rows',
    'upload'                    => 'Upload',
    'new_folder'                => 'New folder',
    'time'                      => 'Time',
    'size'                      => 'Size',
    'between_start'             => 'Start',
    'between_end'               => 'End',
    'next_page'                 => 'Next',
    'prev_page'                 => 'Previous',
    'next_step'                 => 'Next',
    'prev_step'                 => 'Previous',
    'done'                      => 'Done',
    'listbox'                   => [
        'text_total'         => 'Showing all {0}',
        'text_empty'         => 'Empty list',
        'filtered'           => '{0} / {1}',
        'filter_clear'       => 'Show all',
        'filter_placeholder' => 'Filter',
    ],
    'responsive'                => [
        'display_all' => 'Display all',
        'display'     => 'Display',
        'focus'       => 'Focus',
    ],
    'uploader'                  => [
        'add_new_media'          => 'Browse',
        'drag_file'              => 'Or drag file here',
        'max_file_limit'         => 'The :attribute may not be greater than :max.',
        'exceed_size'            => 'Exceeds the maximum file-size',
        'interrupt'              => 'Interrupt',
        'upload_failed'          => 'Upload failed! Please try again.',
        'selected_files'         => ':num files selected，size: :size。',
        'selected_has_failed'    => 'Uploaded: :success, failed: :fail, <a class="retry"  href="javascript:"";">retry </a>or<a class="ignore" href="javascript:"";"> ignore</a>',
        'selected_success'       => ':num(:size) files selected, Uploaded: :success.',
        'dot'                    => ', ',
        'failed_num'             => 'failed::fail.',
        'pause_upload'           => 'Pause',
        'go_on_upload'           => 'Go On',
        'start_upload'           => 'Upload',
        'upload_success_message' => ':success files uploaded successfully',
        'go_on_add'              => 'New File',
        'Q_TYPE_DENIED'          => 'Sorry, the type of this file is not allowed!',
        'Q_EXCEED_NUM_LIMIT'     => 'Sorry, maximum number of allowable file uploads has been exceeded!',
        'F_EXCEED_SIZE'          => 'Sorry，the maximum file-size has been exceeded!',
        'Q_EXCEED_SIZE_LIMIT'    => 'Sorry, the maximum file-size has been exceeded!',
        'F_DUPLICATE'            => 'Duplicate file.',
        'confirm_delete_file'    => 'Are you sure delete this file from server?',
        'dimensions'             => 'The image dimensions is invalid.',
    ],
    'import_extension_confirm'  => 'Are you sure import the extension?',
    'quick_create'              => 'Quick create',
    'grid_items_selected'       => '{n} items selected',
    'nothing_updated'           => 'Nothing has been updated.',
    'welcome_back'              => 'Welcome back, please login to your account.',
    'documentation'             => 'Documentation',
    'demo'                      => 'Demo',
    'extensions'                => 'Extensions',
    'version'                   => 'Version',
    'current_version'           => 'Current version',
    'latest_version'            => 'Latest version',
    'upgrade_to_version'        => 'Upgrade to version :version',
    'enable'                    => 'Enable',
    'disable'                   => 'Disable',
    'uninstall'                 => 'Uninstall',
    'confirm_uninstall'         => 'Please confirm that you wish to uninstall this extension. This may result in potential data loss.',
    'marketplace'               => 'Marketplace',
    'theme'                     => 'Theme',
    'application'               => 'Application',
    'install_from_local'        => 'Install From Local',
    'install_succeeded'         => 'Install succeeded !',
    'invalid_extension_package' => 'Invalid extension package !',
    'copied'                    => 'Copied',
    'auth_failed'               => 'These credentials do not match our records.',
    'validation'                => [
        'match'     => 'The :attribute and :other must match.',
        'minlength' => 'The :attribute must be at least :min characters.',
        'maxlength' => 'The :attribute may not be greater than :max characters.',
    ],
    'merchant_title'            => 'Merchant Platform',
    'merchant_id'               => 'Merchant Id',
    'generate_random_password'  => 'Generate Random Password',
    'validate'                  => 'Validate',
    'new_password'              => 'New Password',
    'verification_code_tip'     => 'Please enter the verification code displayed by the 2FA mobile application',
    'qr_code_tip'               => 'Scan the QR code below to download the MFA Validator',
    'password_tip'              => 'Three months without changing password, need to reset the password',
    'secret_key_tip'            => 'Open your 2FA mobile app (e.g. AliCloud App) and scan the following QR code',
    'content_tip'               => 'If your 2FA mobile app does not support QR codes, enter the following directly into the mobile app',
    'email'                     => 'Email',
    'random_password'           => 'Random Password',
    'click_generate'            => 'Click Generate random password',
    '验证码已使用'              => 'The verification code has been used',
    '验证通过'                  => 'Verification passed',
    '验证失败'                  => 'Verification failed',
];
