<?php
return [
    'labels' => [
        '卡批次' => 'Card Virtual Batch',
        'internal_type_map' => [
            'Regular Card', 'Sharecard Card',
        ],
        'internal_status_map' => [
            'Pending', 'Processing', 'Processing failed', 'Complete',
        ],
        'default_cardholder_map' => [
            'No', 'Yes'
        ],
        'commit_error'             => 'Commit error',
        'limit_error'              => 'Exceeded the maximum limit of ',
        'minimum_amount_error'     => 'Minimum amount submitted is ',
        'start_time_error'         => 'The start time cannot be later than the end time',
        'interval_time_error'      => 'The maximum interval is 7 days',
        'card_holders_count_error' => 'The number of cards must be equal to the number of cardholders',
        'card_holders_error'       => 'Abnormal cardholder information is present',
        'card_holders_unknown'     => 'Default cardholder not filled in',
    ],
    'fields' => [
        'batch_id'                  => 'Batch number',
        'bin'                       => 'BIN segment',
        'quantity'                  => 'Number of opened cards',
        'day_amount_limit'          => 'Daily trading limit',
        'recharge_amount'           => 'Recharge amount',
        'is_use_default_cardholder' => 'Use the default cardholder',
        'cardholder_ids'            => 'Select the cardholder',
        'cardholder_checkbox'       => 'Select the cardholder',
    ],
    'options' => [
        'internal_type_map' => [
            '常规卡' => 'Regular Card',
            '共享卡' => 'Sharecard Card',
        ],
        'internal_status_map' => [
            '待处理' => 'Pending',
            '处理中' => 'Processing',
            '处理失败' => 'Processing failed',
            '完成' => 'Complete',
        ],
    ],
];
