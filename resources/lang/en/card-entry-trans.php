<?php

use App\Models\CardEntryTrans;

return [
    'labels'  => [
        'trans_type'   => CardEntryTrans::$transTypeMap,
        'trans_status' => CardEntryTrans::$transStatusToEnMap,
    ],
    'fields'  => [
        'entry_order_id' => 'Entry Order Id',
        'trans_order_id' => 'Trans Order Id',
        'card_number'    => 'Card Number',
        'amount'         => 'amount',
        'currency'       => 'currency',
        'trans_date'     => 'currency',
        'entry_amount'   => 'Entry amount',
        'entry_currency' => 'Entry currency',
        'entry_date'     => 'Entry date',
        'trans_type'     => 'type',
        'trans_status'   => 'status',
        'trans_addr'     => 'address',
        'fail_reason'    => 'Fail reason',
        'trans_desc'     => 'description',
        'trans_mcc'      => 'mcc',
    ],
    'options' => [

    ],
];