<?php

use App\Models\CardTransaction;

return [
    'labels'  => [
        'transaction_type'          => CardTransaction::$transactionTypeMapToEn,
        'transaction_status'        => CardTransaction::$transactionStatusMapEn,
        'transaction_settle_status' => CardTransaction::$transactionChannelStatusMapEn,
        'Not settled'               => 'Not settled',
    ],
    'fields'  => [
        'transaction_order_id'      => 'Channel Transaction ID',
        'card_number'               => 'Card Number',
        'amount'                    => 'amount',
        'currency'                  => 'currency',
        'settle_amount'             => 'Settle amount',
        'settle_currency'           => 'Settle currency',
        'transaction_type'          => 'type',
        'transaction_status'        => 'status',
        'fail_reason'               => 'Fail reason',
        'transaction_description'   => 'description',
        'transaction_mcc'           => 'mcc',
        'transaction_settle_status' => 'Settle status',
        'auth_code'                 => 'Auth Code',
    ],
    'options' => [
        'is_3d'     => [
            '否' => 'No',
            '是' => 'Yes',
        ],
        'is_settle' => [
            '否' => 'No',
            '是' => 'Yes',
        ],
    ],
];
