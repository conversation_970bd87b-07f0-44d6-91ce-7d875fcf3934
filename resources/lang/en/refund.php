<?php 
return [
    'labels' => [
        'RefundError'               => 'RefundError',
        'ConfirmExport'             => 'Are you sure to export refund information ?',
        'order_info_failed'         => 'Failed to get order information !',
        'refund_amount_tip'         => 'The refund amount must be greater than 0.00 and less than',
        'get_merchant_token_failed' => 'Get merchant API Token failed !',
    ],
    'fields' => [
        'refund_id'               => 'Refund Order number',
        'order_number'            => 'Merchant Order ID',
        'available_refund_amount' => 'RefundableAmount',
        'apply_refund_amount'     => 'RefundAmount',
        'order' => [
            'order_number'  => 'Merchant Order number',
            'merchant_name' => 'Merchant Name',
            'currency'      => 'original order currency',
            'amount'        => 'original order amount',
        ],
        'refund_stats' => [
            'success'  => 'Refund successful',
            'pending'  => 'Refund pending approval',
            'failed'   => 'Refund failed',
            'overtime' => 'Refund timeout!',
        ],
    ],
    'options' => [
    ],
];
