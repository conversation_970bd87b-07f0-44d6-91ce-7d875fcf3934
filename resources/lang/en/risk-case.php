<?php

use App\Models\MerchantKyc;

return [
    'labels'  => [
        'RiskCase'     => 'Case',
        'risk-case'    => 'Case',
        'risk_control' => 'RiskControl',
        'cases'        => 'Case',
        'detail'       => 'Detail',
    ],
    'fields'  => [
        'merchant_id'   => 'Merchant ID',
        'case_number'   => 'Case number',
        'case_type'     => 'Case Type',
        'country'       => 'Country of incorporation',
        'case_status'   => 'Case status',
        'audit_result'  => 'Onboarding result',
        'auditor'       => 'Onboarding analyst',
        'user_id'       => 'Onboarding id',
        'completion_at' => 'Completion time',
        'created_at'    => 'Subimission time',
        'assign'        => 'Assign',
        'claim'         => 'Claim',
        'review'        => 'Review',
        'show'          => 'Show',
        'content'       => 'You confirm to claim this case ',
        'error'         => [
            'case_not_exist' => 'case not exist',
            'case_dump'      => 'This case has been assigned to the personnel, please do not distribute it again',
            'submit_success' => 'successfully'
        ]
    ],
    'options' => [
        'case_type'    => ['KYC screening', 'Scheme compliance', 'Credit risk assessment', 'Financial analysis', 'Fraud risk rules', 'Case Summary'],
        'case_status'  => ['Under Review', 'Pending Review', 'Review Completed', 'Under Re-Review'],
        'audit_result' => ['', 'Approved', 'Rejected', 'Merchant Termination'],
        'country'      => [
            -1                                 => '',
            MerchantKyc::TYPE_CHINESE_MAINLAND => 'China',
            MerchantKyc::TYPE_CHINESE_HONGKONG => 'Hong Kong,China',
            MerchantKyc::TYPE_OVERSEAS         => 'other countries',
        ],
    ],
];
