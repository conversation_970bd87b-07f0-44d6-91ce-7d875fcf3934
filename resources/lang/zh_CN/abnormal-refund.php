<?php
return [
    'labels' => [
        'AbnormalRefund' => '异常订单退款信息',
        'abnormal-refund' => '异常订单退款信息',
    ],
    'fields' => [
        'refund_id'                 => '退款订单号',
        'order_id'                  => '订单号',
        'payment_refund_id'         => '渠道订单号',
        'currency'                  => '币种',
        'amount'                    => '金额',
        'status'                    => '状态',
        'code'                      => '返回代码',
        'result'                    => '返回结果',
        'remark'                    => '备注',
        'completed_at'              => '完成时间',
        'payment_info'              => '渠道信息',
        'merchant_id'               => 'MID',
        'order_number'              => '商户订单号',
        'available_refund_amount'   => '可退金额',
        'apply_refund_amount'       => '退款金额',
        'code/result/remark'        => '返回代码/返回结果/备注',
        'paymentCode/paymentResult' => '渠道返回代码/返回结果',
    ],
    'options' => [
    ],
];
