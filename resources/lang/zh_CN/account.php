<?php

use App\Models\TransferAccount;

return [
    'labels' => [
        'TransferAccount' => 'TransferAccount',
        '提现账户' => '提现账户',
        'currency_type_map' => [
            '人民币', '外币', 'Crypto'
        ],
    ],
    'fields' => [
        'cardholder_id'                                     => '持卡人ID',
        'cardholder' . TransferAccount::TYPE_DOMESTIC       => '持卡人名称',
        'cardholder' . TransferAccount::TYPE_ABROAD         => '账户名称',
        'bank_name' . TransferAccount::TYPE_DOMESTIC        => '开户银行',
        'bank_name' . TransferAccount::TYPE_ABROAD          => '开户银行（英文）',
        'bank_name'                                         => '开户银行',
        'bank_address' . TransferAccount::TYPE_DOMESTIC     => '开户银行地址',
        'bank_address' . TransferAccount::TYPE_ABROAD       => '开户银行地址（英文）',
        'bank_account' . TransferAccount::TYPE_DOMESTIC     => '银行账号',
        'bank_account' . TransferAccount::TYPE_ABROAD       => '银行账号',
        'bank_account' . TransferAccount::TYPE_CRYPTO       => 'Crypto address',
        'bank_account'                                      => '银行账号/Crypto address',
        'bank_mobile'                                       => '银行预留手机号',
        'cardholder_id_card'                                => '持卡人身份证号码',
        'inter_bank_number'                                 => '联行号',
        'nickname'                                          => '账户别名',
        'convert_currency' . TransferAccount::TYPE_DOMESTIC => '出款货币',
        'convert_currency' . TransferAccount::TYPE_ABROAD   => '出款货币',
        'convert_currency' . TransferAccount::TYPE_CRYPTO   => '出款货币',
        'convert_currency'                                  => '出款货币',
        'company_address'                                   => '公司地址（英文）',
        'swift_iban'                                        => 'SWIFT/IBAN',
        'type'                                              => '账户类型',
        'status'                                            => '状态',
        'remarks'                                           => '备注',
        'tip' => [
            'rmb_support'                     => '人民币账户仅支持对私账户',
            'not_rmb_support'                 => '外币账户仅支持对公账户',
            'bank_name'                       => '格式示例：xx 银行 xx 分行 xx 支行',
            'bank_mobile'                     => '银行银行预留手机号不能为空',
            'bank_mobile_placeholder'         => '银行银行预留手机号',
            'bank_mobile_error'               => '银行预留手机号格式错误',
            'bank_address_null_error'         => '开户银行地址不能为空',
            'bank_null_error'                 => '开户银行不能为空',
            'bank_account_null_error'         => '银行账号不能为空',
            'crypto_address_null_error'       => 'Crypto address不能为空',
            'inter_bank_number'               => '联行号不能为空',
            'cardholder_id_card'              => '持卡人身份证号码不能为空',
            'cardholder_id_name'              => '持卡人姓名不能为空',
            'cardholder_id_card_error'        => '持卡人身份证号码格式错误',
            'disbursement_currency'           => '出款货币不能为空',
            'bank_english_null_error'         => '开户银行（英文）不能为空',
            'bank_address_english_null_error' => '开户银行地址（英文）不能为空',
            'account_name_null_error'         => '账户名称不能为空',
            'company_address_null_error'      => '公司地址（英文）不能为空',
        ],
        'cannot_be_empty' => '不能为空',
    ],
    'options' => [
        'status_merchant_map' => [
            '审核中' => '审核中',
            '请重新提交' => '请重新提交',
            '已过审' => '已过审',
        ],
        'currency_type_map' => [
            '人民币' => '人民币',
            '外币' => '外币',
        ],

    ],
];
