<?php
return [
    'labels'  => [
        '持卡人信息列表'         => '持卡人信息列表',
        'add_to_card_holder'     => '添加持卡人',
        'add_to_new_card_holder' => '您确认添加新的持卡人',
        'gender'                 => [
            '女', '男',
        ],

        'id_type' => [
            1 => '居民身份证',
            2 => '军人或武警身份证',
            3 => '港澳台通行证',
            4 => '护照',
            5 => '其他有效旅行证件',
            6 => '其他类个人有效证件',
        ],

        'company_position' => [
            1 => '法人代表',
            2 => '董事',
            3 => '高级管理员',
            4 => '经理',
            5 => '职员',
        ],

        'subject_type' => [
            1 => '员工',
//            2 => '合作企业',
        ],

        'holder_type'                        => [
            1 => '法人',
            2 => '其他管理员',
        ],
        'security_index'                     => [
            "1" => "你的第一只宠物叫什么名字?",
            "2" => "你外祖母的娘家姓是什么?",
            "3" => "你儿时最喜欢的朋友叫什么名字?",
            "4" => "你的第一辆车是什么品牌的?",
            "5" => "你的父母是在哪个城市认识的?"
        ],
        '该商户下已有默认持卡人, 无法设置'   => '该商户下已有默认持卡人, 无法设置',
        '是否修改该默认持卡人的状态'         => '是否修改该默认持卡人的状态',
        '修改失败'                           => '修改失败',
        '修改成功'                           => '修改成功',
        '已有默认持卡人, 无法设置'           => '已有默认持卡人, 无法设置',
        '是否修改该持卡人状态'               => '是否修改该持卡人状态',
        '已有审核完成的持卡人信息, 无法修改' => '已有审核完成的持卡人信息, 无法修改',
        '虚拟卡数据不存在'                   => '虚拟卡数据不存在',
        '该虚拟卡已绑定持卡人信息'           => '该虚拟卡已绑定持卡人信息',
        '绑定成功'                           => '绑定成功',
        '您确定要绑定该持卡人吗'             => '您确定要绑定该持卡人吗',
        '该持卡人已审核, 无法修改'           => '该持卡人已审核, 无法修改',
        '启用'                               => '启用',
        '禁用'                               => '禁用',
        '删除'                               => '删除',
        '您确定要启用该持卡人吗'             => '您确定要启用该持卡人吗',
        '您确定要禁用该持卡人吗'             => '您确定要禁用该持卡人吗',
        '您确定要删除该持卡人吗'             => '您确定要删除该持卡人吗',
        '该持卡人不存在'                     => '该持卡人不存在',
        '该持卡人已被删除'                   => '该持卡人已被删除',
        '该持卡人已绑定虚拟卡，无法删除'      => '该持卡人已绑定虚拟卡，无法删除',
        '默认持卡人'                         => '默认持卡人',
        '取消默认持卡人'                     => '取消默认持卡人',
        '只能设置一个默认持卡人'             => '只能设置一个默认持卡人',
        '您确定设置为默认持卡人吗'           => '您确定设置为默认持卡人吗',
        '您确定取消默认持卡人吗'             => '您确定取消默认持卡人吗',
        '您确定进行批量删除吗'               => '您确定进行批量删除吗',
        '批量删除'                           => '批量删除',
        '删除失败'                           => '删除失败',
        '删除成功'                           => '删除成功',
        '存在已绑定虚拟卡的持卡人，无法删除'  => '存在已绑定虚拟卡的持卡人，无法删除',
        '持卡人信息审核未通过'               => '持卡人信息审核未通过',
        '持卡人年龄在18~65岁之间'            => '持卡人年龄在18~65岁之间',
        '国家/地区与手机号码前缀不匹配'      => '国家/地区与手机号码前缀不匹配'
    ],
    'fields'  => [
        'first_name'            => '姓氏',
        'last_name'             => '名字',
        'full_name'             => '姓名',
        'gender'                => '性别',
        'calling_prefix'        => '手机号码前缀',
        'phone'                 => '手机号码',
        'email'                 => '邮箱',
        'postal_code'           => '邮政编码',
        'country'               => '国家 / 地区',
        'city'                  => '城市',
        'province'              => '省份',
        'address'               => '地址',
        'id_type'               => '证件类型',
        'id_number'             => '证件号码',
        'birth_date'            => '出生日期',
        'nationality'           => '国籍',
        'photo_front'           => '证件正面照片',
        'photo_back'            => '证件反面照片',
        'company_position'      => '公司职位',
        'subject_type'          => '主体类型',
        'holder_type'           => '持卡人身份',
        'is_default_cardholder' => '是否使用默认持卡人',
        'status'                => '状态',
        'audit_status'          => '审核状态',
        'audit_remark'          => '审核备注',
        'reviewer_user_name'    => '审核人员名称',
        'cardholder_id'         => '持卡人',
        'security_index'        => '密保问题',
        'security_answer'       => '密保答案',
    ],
    'options' => [
        'gender' => [
            '男' => '男',
            '女' => '女',
        ],

        'id_type' => [
            '居民身份证'         => '居民身份证',
            '军人或武警身份证'   => '军人或武警身份证',
            '港澳台通行证'       => '港澳台通行证',
            '护照'               => '护照',
            '其他有效旅行证件'   => '其他有效旅行证件',
            '其他类个人有效证件' => '其他类个人有效证件',
        ],

        'company_position' => [
            '法人代表'   => '法人代表',
            '董事'       => '董事',
            '高级管理员' => '高级管理员',
            '经理'       => '经理',
            '职员'       => '职员'
        ],

        'subject_type' => [
            '员工'     => '员工',
//            '合作企业' => '合作企业',
        ],

        'holder_type' => [
            '法人'       => '法人',
            '其他管理员' => '其他管理员',
        ],

        'audit_status'          => [
            '待审核'     => '待审核',
            '审核通过'   => '审核通过',
            '审核不通过' => '审核不通过',
        ],
        'status'                => [
            '启用' => '启用',
            '禁用' => '禁用',
        ],
        'is_default_cardholder' => [
            '是' => '是',
            '否' => '否',
        ],
    ],
];
