<?php
return [
    'labels' => [
        'Chargeback' => '拒付工单',
        '拒付工单'    => '拒付工单'
    ],
    'fields' => [
        'chargeback_history'  => '拒付录入记录',
        'chargeback_id'       => '拒付工单号',
        'order_id'            => '交易订单号',
        'status'              => '工单状态',
        'is_appeal_success'   => '是否申诉成功',
        'chargeback_status'   => '拒付状态',
        'chargeback_deadline' => '处理截止日期',
        'appeal_at'           => '申诉日期',
        'pre_arbitration_at'  => '预仲裁日期',
        'remarks'             => '备注',
        'currency'            => '拒付货币',
        'amount'              => '拒付金额',
        'type'                => '拒付类型',
        'chargeback_code'     => '拒付原因码',
        'input_status'        => '录入状态',
        'input_fail_cause'    => '录入失败原因',
        'appeal_materials'    => '申诉材料',
        'chargeback_at'       => '拒付日期',
        'arn'                 => 'arn',
        'originator'          => '发起人',
        'appeal_materials_at' => '提交申诉材料时间',
        'submitter'           => '提交人',
        'submitter_email'     => '提交人联系邮箱',
        'order_number'        => '商户订单号',
        'url_name'            => '交易网址',
        'created_at'          => '原交易日期',
        'chargeback_at'       => '拒付通知日期',
    ],
    'options' => [],
];
