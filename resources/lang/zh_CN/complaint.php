<?php
return [
    'labels'  => [
        'OrderComplaint' => 'OrderComplaint',
        '争议工单' => '争议工单',
        'is_reply_map' => [
            '否', '是',
        ],
        'is_refund_map' => [
            '未退款', '全额退款', '部分退款',
        ],
        'is_chargeback_map' => [
            '否', '是',
        ],
    ],
    'fields'  => [
        'order_id'       => '订单号',
        'merchant_id'    => 'MID',
        'merchant_name'  => '商户名称',
        'business_id'    => 'BID',
        'order_number'   => '商户订单号',
        'bill_name'      => '持卡人姓名',
        'bill_email'     => '持卡人邮箱',
        'notice_email'   => '通知邮箱',
        'd_title_id'     => '标题ID',
        'content'        => '内容',
        'status'         => '处理状态',
        'is_reply'       => '是否回复',
        'xxx'            => 'ninini',
        'device'         => '设备',
        'ip'             => 'IP',
        'confirmed_type' => '确认信类型',
        'replied_at'     => '回复时间',
        'dictionary'     => [
            'name' => '标题'
        ],
        'order_relation' => [
            'is_refund'     => '是否退款',
            'is_chargeback' => '是否拒付'
        ],
        'notice_status' => '关注状态',
        'is_refund' => '是否退款',
        'is_chargeback' => '是否拒付',
        'id' => '标题',
    ],
    'options' => [
        'is_reply_map' => [
            '是' => '是',
            '否' => '否',
        ],
        'is_refund_map' => [
            '未退款' => '未退款',
            '全额退款' => '全额退款',
            '部分退款' => '部分退款'
        ],
        'is_chargeback_map' => [
            '是' => '是',
            '否' => '否',
        ],
    ],
];
