<?php
return [
    'fields'  => [
        'order_id'             => '订单号',
        'type'                 => '交易类型',
        'status'               => '交易状态',
        'merchant_id'          => 'MID',
        'business_id'          => 'BID',
        'merchant_name'        => '商户名',
        'cc_type'              => '卡种',
        'card_mask'            => '卡号',
        'card_country'         => '卡国家',
        'url_name'             => '交易网址',
        'bill_name'            => '账单人姓名',
        'bill_email'           => '账单人邮箱',
        'bill_country'         => '账单人国家',
        'bill_state'           => '账单人省份地区',
        'bill_city'            => '账单人城市',
        'bill_address'         => '账单人地址',
        'bill_postcode'        => '账单人邮编',
        'bill_phone'           => '账单人电话',
        'ship_name'            => '收件人姓名',
        'ship_email'           => '收件人邮箱',
        'ship_country'         => '收件人国家',
        'ship_state'           => '收件人省份地区',
        'ship_city'            => '收件人城市',
        'ship_address'         => '收件人地址',
        'ship_postcode'        => '收件人邮编',
        'ship_phone'           => '收件人电话',
        'order_number'         => '商户订单号',
        'currency'             => '支付货币',
        'amount'               => '支付金额',
        'code'                 => '返回代码',
        'result'               => '返回结果',
        'remark'               => '备注',
        'parent_order_id'      => '原始订单号',
        'is_3d'                => '是否3d',
        'tracking_type'        => '运单类型',
        'tracking_number'      => '运单号',
        'expired_at'           => '有效期',
        'is_settle'            => '是否结算',
        'settle_at'            => '结算日期',
        'created_at'           => '创建时间',
        'updated_at'           => '更新时间',
        'completed_at'         => '完成时间',
        'ip'                   => 'IP',
        'channel_suppliers'    => '渠道名',
        'is_chargeback'        => '是否拒付',
        'card_mask_ciphertext' => '卡密文',
        'payment_amount'       => '授权金额',
        'amount_usd'           => '支付USD金额',
        'payment_amount_usd'   => '授权USD金额',
        'payment_code'         => '渠道返回码',
        'payment_result'       => '渠道返回结果',
        'payment_remark'       => '渠道备注',
        'access_type'          => '接入方式',
        'payment_order_number' => '渠道订单号',
        'payment_order_id'     => '渠道交易流水号',
        'product_name'         => '产品名称',
        'product_url'          => '产品Url',
        'product_sum'          => '产品数量',
        
        'chargeback_order_id' => '交易订单号',
        'chargeback_id'       => '拒付工单号',
        'channel'             => '账单标识',
        'chargeback_code'     => '拒付原因码',
        'chargeback_type'     => '拒付类型',
        'order_currency'      => '订单币种',
        'order_amount'        => '订单金额',
        'chargeback_status'   => '工单状态',
        'email'               => '邮箱',
        'arn'                 => 'ARN',
        'order_created_at'    => '原交易日期',
        'chargeback_at'       => '拒付时间',
        'chargeback_deadline' => '处理截止日期',
        'is_refund'           => '是否退款',
        
        'refund_id'               => '退款订单号',
        'original_order_currency' => '原始订单币种',
        'original_order_amount'   => '原始订单金额',
        'refund_currency'         => '币种',
        'refund_amount'           => '金额',
        'refund_status'           => '状态',
        'payment_refund_id'           => '退款渠道订单号',
    ]
];
