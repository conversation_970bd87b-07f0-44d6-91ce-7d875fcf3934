<?php

use App\Models\MerchantKyc;
use App\Models\MerchantKycBeneficial;
use App\Models\RiskCase;

return [
    'labels'  => [
        'status'            => [
            '待审核', '审核通过', '驳回',
        ],
        'company_details'   => '企业基本信息',
        'holding_co'        => '控股公司',
        'ubo'               => '最终受益人',
        'director'          => '董事',
        'contacts'          => '业务联系人',
        'kyc'               => 'kyc 基本情况',
        'audit_information' => '审核信息',
        'business_info'     => '业务信息',
        'authorized'        => '授权签署人',
        'case'              => '商户kyc审核',
    ],
    'fields'  => [
        'register'                       => '注册',
        'submit'                         => '提交',
        'back_to_login'                  => '返回',
        'operation'                      => '操作',
        'add_to'                         => '添加',
        'delete'                         => '删除',
        'title'                          => '商户KYC填写',
        'imgTip'                         => '图片未上传,请上传后点击Upload按钮！',
        'xlsxTip'                        => '表格未上传,请上传后点击Upload按钮！',
        'company_entity'                 => '公司主体',
        'company_type'                   => [
            'chinese_mainland' => '中国大陆',
            'chinese_hongkong' => '中国香港',
            'overseas'         => '海外'
        ],
        'business_type'                  => '业务类型',
        'business_select'                => [
            'order'   => '收单业务',
            'virtual' => '虚拟卡业务',
        ],
        'register_img'                   => '公司注册证明书',
        'registered_address'             => '注册地址',
        'office_address'                 => '办公地址',
        'mainland_type_info'             => [
            'certificate_no'     => '营业执照编号',
            'certificate_img'    => '营业执照',
            'enforcer_name'      => '法人姓名',
            'enforcer_code'      => '法人身份证图片(正反面)',
            'enforcer_hold_code' => '法人手持身份证',
            'contacts_email'     => '业务联系人邮箱',
        ],
        'hongkong_type_info'             => [
            'certificate_no'     => '商业登记证号码',
            'certificate_img'    => '商业登记证',
            'enforcer_name'      => '董事姓名',
            'enforcer_code'      => '董事护照',
            'enforcer_hold_code' => '董事手持护照',
            'contacts_email'     => '业务联系人邮箱',
        ],
        'overseas_type_info'             => [
            'certificate_no'     => '注册证书编号',
            'certificate_img'    => '注册证书',
            'enforcer_name'      => '董事姓名',
            'enforcer_code'      => '董事护照',
            'enforcer_hold_code' => '董事手持护照',
            'contacts_email'     => '业务联系人邮箱',
        ],
        'mainland_shareholder_info'      => [
            'info'             => '股东信息(持股比例需超过25%)',
            'shareholder_name' => '股东姓名',
            'shareholder_code' => '股东身份证(正反面)',
        ],
        'hongkong_shareholder_info'      => [
            'info'             => '股东信息(持股比例需超过25%)',
            'shareholder_name' => '股东姓名',
            'shareholder_code' => "股东护照",
        ],
        'overseas_shareholder_info'      => [
            'info'             => '股东信息(持股比例需超过25%)',
            'shareholder_name' => '股东姓名',
            'shareholder_code' => '股东护照',
        ],
        'equity_certificate_img'         => '股权证明',
        'bank_account_img'               => '银行账户证明',
        'merchant_apply_file'            => '商户申请表',
        'contacts_name'                  => '业务联系人姓名',
        'contacts_phone'                 => '电话',
        'contacts_position'              => '职务',
        'contacts_email'                 => '业务联系人邮箱',
        'password'                       => '密码',
        'confirm_password'               => '确认密码',
        'tip'                            => [
            'success'                => '申请成功请注意接收邮件信息!',
            'shareholders_max'       => '最多添加四位股东!',
            'creation_failed'        => '注册失败,业务邮箱已经登记或填写信息有误！',
            'shareholder_name_empty' => '股东姓名填写不全！',
            'register_img_empty'     => '公司注册证明书未上传!',
            'status_error'           => '状态已经是终态!',
            'password_error'         => '两次密码填写不一致!',
            'password_min'           => '必填，8-14位并且包括：字母/数字/标点符号至少两种!',
            'merchant_apply_file'    => '请根据附件内容填写提交!',
            'template_one'           => '附件一',
            'template_two'           => '附件二',
            'img_restrictions'       => '支持文件类型：jpg, jpeg, png, pdf',
            'file_restrictions'      => '支持文件类型：xlsx',
            'system_error'           => '系统繁忙，请联系运营'
        ],
        'already_registered'             => '商户已经申请,请登录系统查看审核进度!',
        'reject'                         => '驳回原因',
        'country'                        => '注册国家',
        'audit_result'                   => '审核结果',
        'office_phone'                   => '办公电话',
        'company_name'                   => '企业注册名称',
        'company_address'                => '企业注册地址',
        'cert_validity'                  => '商业登记证书有效期',
        'export_country'                 => '出口国家',
        'reg_cert_no'                    => '企业注册证书编号',
        'business_reg_no'                => '商业登记编号',
        'found_date'                     => '成立日期',
        'fin_statement'                  => '财务报表',
        'comp_reg_cert'                  => '企业注册证书',
        'business_reg_cert'              => '商业登记证书',
        'shareholder_structure'          => '股东结构图',
        'annual_rpt'                     => '周年申报表 NAR1',
        'bank_statement'                 => '银行对账单',
        'comp_addr_cert'                 => '公司地址证明',
        'equity_ratio'                   => '股权占比',
        'cert_type'                      => '证件类型',
        'nationality'                    => '国籍',
        'name'                           => '全名',
        'birth_date'                     => '出生日期',
        'cert_number'                    => '身份证/护照号码',
        'address'                        => '居住地址',
        'is_signatory'                   => 'UBO是否作为授权签署人',
        'cert_file'                      => '彩色护照证（正反面）/香港身份证/身份证（正反面）',
        'residence_doc'                  => '居住地址文件',
        'hold_cert_photo'                => 'UBO手持身份证',
        'hold_cert_photo_b'              => '董事手持身份证',
        'hold_cert_photo_c'              => '授权委托书',
        'is_listed_in_nar1'              => 'UBO是否担任董事',
        'is_listed_in_nar1_b'            => '董事是否列在 NAR1',
        'is_signatory_b'                 => '董事是否作为授权签字人',
        'is_signatory_c'                 => '授权签署人',
        'edd'                            => '升级 EDD',
        'edd_user_id'                    => 'EDD审核人员',
        'audit_remark'                   => '内部备注',
        'merchant_remark'                => '商户可见备注',
        'files'                          => '补充附件',
        'holding_co'                     => '控股公司',
        'ubo'                            => '最终受益人',
        'director'                       => '董事',
        'authorized'                     => '授权签署人',
        'share_ratio'                    => '股权占比',
        'random_password'                => '随机密码',
        'get_password'                   => '获取随机密码',
        'download_template'              => '点击下载模板',
        'nature_of_business'             => '经营内容',
        'business_model'                 => '业务模式',
        'merchant_portal_url_app'        => '商户入网网址/APP名称',
        'mcc_code'                       => 'MCC',
        'payment_channel'                => '业务类型',
        'transaction_fee_rate'           => '费率',
        'additional_fees'                => '其他费用',
        'settlement_cycle'               => '清算周期',
        'transaction_currency'           => '交易币种',
        'settlement_currency'            => '结算币种',
        'store_info'                     => '门店资料',
        'store_name_zh'                  => '门店名称（中）',
        'store_name_en'                  => '门店名称（英）',
        'store_address'                  => '门店地址',
        'store_business_address'         => '经营地址',
        'store_es_address'               => '其他地址',
        'average_transaction_value'      => '平均每笔交易金额',
        'avg_daily_transaction_count'    => '日均交易笔数',
        'avg_daily_transaction_volume'   => '日均交易金额',
        'avg_monthly_transaction_count'  => '月均交易笔数',
        'avg_monthly_transaction_volume' => '月均交易金额',
        'merchant'                       => ['source' => '商户来源'],
        'case'                           => ['merchant_id'   => '商户编号',
                                             'case_number'   => '案例编号',
                                             'case_type'     => '案例类型',
                                             'auditor'       => '审核人员',
                                             'user_id'       => '审核人员 id',
                                             'completion_at' => '完成时间',
                                             'created_at'    => '提交时间'],
        'help'                           => [
            'comp_reg_cert'         => '请提供在有效期内的企业注册证书',
            'business_reg_cert'     => '请提供在有效期内的商业登记证。',
            'fin_statement'         => '最新的财务报表，用于评估公司的财务情况',
            'shareholder_structure' => '公司持股情况，需提供股权结构说明至最终受益人必须在最近三个月内签署并注明日期。',
            'annual_rpt'            => '过去12个月内有效的NAR1，或适用于成立未满1年的商户的NNC1。',
            'bank_statement'        => '香港主体名下公司账户的正式开户信或者银行对账单，需有完整账户名、账号、开户行信息',
            'comp_addr_cert'        => '公司地址的地址证明，可以是近三个月水电账单、电话账单、税单、近一年租赁合同等，任一即可',
            'residence_doc'         => '用于验证自然人在注册地址的真实性，可提供水电费账单、租赁协议或政府签发的信函。必须为最近三个月内的日期。',
            'random_password'       => '获取随机密码成功',
            'generate_password'     => '点击生成随机密码',
            'ubo'                   => '最少添加一个最终受益人',
            'director'              => '最少添加一个董事',
            'annual_rpt_required'   => '成立未满一年的企业需提交年度报告',
            'authorized'            => '最少添加一个授权签署人'
        ],
        'ln_result'                      => 'LN扫描结果'
    ],
    'options' => [
        'business_type'   => [
            '收单', '虚拟卡',
        ],
        'type'            => [
            '中国大陆', '中国香港', '海外',
        ],
        'confirm'         => [
            true  => '是',
            false => '否',
        ],
        'business_model'  => [
            1 => '线上业务', 2 => '线下业务',
        ],
        'payment_channel' => MerchantKyc::$paymentChannelMap,
        'store_address'   => MerchantKyc::$storeAddressTypeMap,
        'cert_type'       => MerchantKycBeneficial::$certTypeMap,
        'is_signatory'    => [1 => '最终受益人/董事', 0 => '委托人'],
        'audit'           => RiskCase::$auditResultSelectMap,
        'source'          => ['请选择', '外呼线索', '来电线索']
    ],
];
