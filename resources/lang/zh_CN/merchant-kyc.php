<?php
return [
    'fields'  => [
        'company_name'              => '公司名称',
        'merchant_id'               => 'MID',
        'address'                   => '注册地址',
        'type'                      => '主体类型',
        'register_img'              => '公司注册证明书',
        'registered_address'        => '注册地址',
        'office_phone'              => '办公电话',
        'office_address'            => '办公地址',
        'mainland_type_info'        => [
            'certificate_no'     => '营业执照编号',
            'certificate_img'    => '营业执照',
            'enforcer_name'      => '法人姓名',
            'enforcer_code'      => '法人身份证图片(正反面)',
            'enforcer_hold_code' => '法人手持身份证',
        ],
        'hongkong_type_info'        => [
            'certificate_no'     => '商业登记证号码',
            'certificate_img'    => '商业登记证',
            'enforcer_name'      => '董事姓名',
            'enforcer_code'      => '董事护照(正反面)',
            'enforcer_hold_code' => '董事手持护照',
        ],
        'overseas_type_info'        => [
            'certificate_no'     => '注册证书编号',
            'certificate_img'    => '注册证书',
            'enforcer_name'      => '董事姓名',
            'enforcer_code'      => '董事护照(正反面)',
            'enforcer_hold_code' => '董事手持护照',
        ],
        'mainland_shareholder_info' => [
            'shareholder_name' => '股东姓名',
            'shareholder_code' => '股东身份证(正反面)',
        ],
        'other_shareholder_info'    => [
            'shareholder_name' => '股东姓名',
            'shareholder_code' => '股东护照',
        ],
        'equity_certificate_img'    => '股权证明',
        'bank_account_img'          => '银行账户证明',
        'merchant_apply_file'       => '商户申请表',
        'contacts_name'             => '业务联系人姓名',
        'contacts_phone'            => '电话',
        'contacts_position'         => '职务',
        'contacts_email'            => '业务联系人邮箱',
        'audit_result'              => '状态',
    ],
    'labels' => [
        'detail' => '详情',
    ],
    'options' => [],
];
