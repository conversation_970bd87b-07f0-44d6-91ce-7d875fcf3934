<?php

use App\Models\RiskCase;

return [
    'labels'  => [
        'RiskCase'     => '案例处理',
        'risk-case'    => '案例处理',
        'risk_control' => '风控管理',
        'cases'        => '案例处理',
        'detail'       => '详情',
    ],
    'fields'  => [
        'merchant_id'   => '商户编号',
        'case_number'   => '案例编号',
        'case_type'     => '案例类型',
        'country'       => '注册国家',
        'case_status'   => '案例状态',
        'audit_result'  => '审核结果',
        'auditor'       => '审核人员',
        'user_id'       => '审核人员 id',
        'completion_at' => '完成时间',
        'created_at'    => '提交时间',
        'assign'        => '派发',
        'claim'         => '领取',
        'review'        => '审核',
        'show'          => '查看',
        'content'       => '您确认领取该案例:',
        'error'         => [
            'case_not_exist' => '案例不存在',
            'case_dump'      => '该案例已派发给该人员,请勿重复派发',
            'submit_success' => '提交成功'
        ]
    ],
    'options' => [
        'case_type'    => RiskCase::$caseTypeMap,
        'case_status'  => RiskCase::$caseStatusMap,
        'audit_result' => RiskCase::$auditResultMap,
        'country'      => RiskCase::$countryMap,
    ],
];
