<?php

use App\Models\RiskCase;
use App\Models\RiskSummary;

return [
    'labels'  => [
        'RiskSummary'  => '案例总结',
        'risk-summary' => '案例总结',
        'case'         => '案例总结',
        'summary'      => '交易要点',
        'approval'     => '交易结果',
    ],
    'fields'  => [
        'merchant_dba'              => '商户营业名称',
        'merchant_id'               => '商户编号',
        'merchant_name'             => '商户法定名称',
        'type'                      => '商户注册地国家',
        'cko_legal_entity'          => 'CKO法律实体',
        'export_country'            => '商户目标市场',
        'year_of_incorporation'     => '成立年份',
        'primary_mcc_description'   => '主营业务类别描述',
        'primary_mcc'               => 'MCC代码',
        'future_delivery_days'      => '未来交货周期',
        'urls'                      => '网址信息',
        'risk_rating'               => '风险评级',
        'annual_projected_volume'   => '年度预测交易量',
        'annual_credit_exposure'    => '年度预测信用敞口',
        'avg_transaction_value'     => '平均交易价值',
        'annual_net_revenue'        => '年度预测净收入',
        'share_of_wallet'           => '钱包份额',
        'settlement_frequency'      => '结算频率',
        'commercial_representative' => '商户代表',
        'deal_partner'              => '交易合作伙伴',
        'business_background'       => '商户背景',
        'approval_summary'          => '批准总结',
        'supplementary_annex'       => '补充附件',
        'team'                      => '团队',
        'final_decision'            => '最终决定',
        'final_decision_date'       => '最终决定日期',
        'approval_conditions'       => '审批条件',
        'approver_name'             => '审批人',
        'url'                       => '网址',
        'description'               => '主营业务类别描述',
        'mcc'                       => '主营业务类别代码',
        'urls_case'                 => '卡组合规',
        'merchant'                  => ['source' => '商户来源'],
        'riskCase'                  => ['merchant_id'   => '商户编号',
                                        'case_number'   => '案例编号',
                                        'case_type'     => '案例类型',
                                        'auditor'       => '审核人员',
                                        'user_id'       => '审核人员 id',
                                        'completion_at' => '完成时间',
                                        'created_at'    => '提交时间',
                                        'riskCasesBid'  => 'BID',
        ],
        'help'                      => [
            'dba' => 'DBA指企业对外经营所用的非法定名称'
        ],
    ],
    'options' => [
        'audit_result' => RiskCase::$auditResultMap,
        'team'         => RiskSummary::$teamMap,
    ],
];
