<style>
    .login-box {
        padding: 5px;
    }

    .login-card-body {
        padding: 1.5rem 1.8rem 1.6rem;
    }

    .card,
    .card-body {
        border-radius: 1.25rem
    }

    .login-btn {
        padding-left: 2rem !important;
        padding-right: 1.5rem !important;
    }

    .content {
        overflow-x: hidden;
    }

    .form-group .control-label {
        text-align: left;
    }

    .bg-40 {
        background: url(/media/bg/bg-12.png) no-repeat center center !important;
        z-index: 1;
    }

    .mb-2 {
        font-size: 28px;
        font-family: Source Han Sans CN;
        font-weight: 500;
        color: #000000;
    }

    .text-right {
        float: right;
    }

    .sign-btn {
        width: 80%;
        height: 40px;
        margin: 0px 10% !important;
        border-radius: 25px !important;
    }

    .login-vector {
        position: fixed;
        top: 0;
        left: 0;
        z-index: 2;
    }

    .login-vector img {
        width: 40%;
        margin: 10%;
    }

    .btn.btn-primary.btn-file {
        border-color: #414750;
    }

    .nav-language {
        position: fixed;
        top: 1.5rem;
        left: 90%;
        z-index: 3;
    }

    .nav-link {
        color: #000000;
    }

    .row {
        justify-content: center;
    }

    .download-row {
        border-top: 1px solid #666;
        padding-top: 5px;
        text-align:center;
    }

    .download-body {
        float: left;
        margin-bottom: 15px;
        min-width: 220px;
    }

    .container {
      margin-bottom: 5px;
    }
</style>
<div class="login-vector">
    {{-- <img src="/media/logos/embracy-logo-vector.png"> --}}
</div>
<div class="login-page bg-40">
    <div class="login-box">
        <div class="card">
            <div class="card-body login-card-body shadow-100" style="text-align: center;">
                {{-- <div class="login-logo mb-2">
                    {{ $company }}
                </div> --}}
                <div class="container spark-screen">
                    @if ($type == 'password')
                        {{ trans('admin.password_tip') }}<br /><br />
                    @else
                        <div style="{{ $display }}">
                            <div class="row">
                                <div class="col-md-10 col-md-offset-1">
                                    <div class="panel panel-default">
                                        <div class="panel-heading">2FA Secret Key</div>
                                        <div class="panel-body">
                                            {{ trans('admin.secret_key_tip') }}
                                            <br />
                                            <img width="200" alt="Image of QR barcode" src="{{ $image }}" />
                                            <br />
                                            {{ trans('admin.content_tip') }}: <code>{{ $secret }}</code>
                                            <br /><br />
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="row download-row">
                                <div class="col-md-10 col-md-offset-1">
                                    <div class="panel panel-default">
                                        <div class="panel-heading" style="font-weight: bold;">{{ trans('admin.qr_code_tip') }}</div>
                                        <div class="panel-body download-body">
                                          <div style="float: left;">
                                            <img alt="Image of QR barcode" width="100" height="100" src="/media/2fa/authenticator_android.png" /><br/><code>android</code>
                                          </div>
                                          <div style="float: right;"><img alt="Image of QR barcode" width="100" height="100" src="/media/2fa/authenticator_ios.png" /><br/><code>ios</code></div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        {{ trans('admin.verification_code_tip') }}
                    @endif
                </div>
                <form id="2fa-form" method="POST" action="{{ $url }}">
                    <input type="hidden" name="_token" value="{{ csrf_token() }}" />

                    @if ($type == 'password')
                        <fieldset class="form-label-group form-group position-relative has-icon-left">
                            <input minlength="8" maxlength="14" id="password" type="password"
                                class="form-control {{ $errors->has('password') ? 'is-invalid' : '' }}" name="password"
                                placeholder="{{ trans('admin.new_password') }}" required autocomplete="current-password">
                            <div class="form-control-position" onclick="togglePassword('password')">
                                <i class="feather icon-eye"></i>
                            </div>
                            <label for="password">{{ trans('admin.new_password') }}</label>
                            <div class="help-block with-errors"></div>
                        </fieldset>

                        <fieldset class="form-label-group form-group position-relative has-icon-left">
                            <input minlength="8" maxlength="14" id="confirm_password" type="password"
                                class="form-control {{ $errors->has('confirm_password') ? 'is-invalid' : '' }}"
                                name="confirm_password" placeholder="{{ trans('admin.password_confirmation') }}" required
                                autocomplete="current-password">
                            <div class="form-control-position" onclick="togglePassword('confirm_password')">
                                <i class="feather icon-eye"></i>
                            </div>
                            <label for="confirm_password">{{ trans('admin.password_confirmation') }}</label>
                            <div class="help-block with-errors"></div>
                        </fieldset>

                        <span style="cursor: pointer" class="copy-group"><a href="javascript:;" id="get_random_password">{{ trans('admin.generate_random_password') }}</a></span>
                        <span style="cursor: pointer" class="copy-group" id="random_password"></span>
                    @else
                        <input type="hidden" name="secret" value="{{ $secret }}" />
                        <input type="hidden" name="mode" value="{{ $mode ?? 0 }}" />
                        <fieldset class="form-label-group form-group position-relative has-icon-left">
                            <input minlength="6" maxlength="20" id="verify_code" type="password"
                                class="form-control {{ $errors->has('verify_code') ? 'is-invalid' : '' }}"
                                name="verify_code" placeholder="{{ trans('admin.validate') }}" required
                                autocomplete="current-password">
                            <div class="form-control-position">
                                <i class="feather icon-lock"></i>
                            </div>
                            <label for="verify_code">{{ trans('admin.validate') }}</label>
                            <div class="help-block with-errors"></div>
                        </fieldset>
                    @endif
                    <button type="submit" class="btn btn-info float-right login-btn sign-btn">
                        {{ trans('admin.submit') }}
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
    function togglePassword(id) {
        var passwordInput = document.getElementById(id);
        if (passwordInput.type === 'password') {
            passwordInput.type = 'text';
        } else {
            passwordInput.type = 'password';
        }
    }

    $('#get_random_password').on('click', () => {
        $.ajax({
            url: '/get_random_password',
            type: 'GET',
            success: function(res) {
                $('#random_password').text(res);
                $('input[name=password]').val(res);
                $('input[name=confirm_password]').val(res);
            }
        });
    });

    Dcat.ready(function() {
        // ajax表单提交
        $('#2fa-form').form({
            validate: true,
            success: function(data) {
                if (!data.status) {
                    Dcat.error(data.message);

                    return false;
                }

                Dcat.success(data.message);
                window.location.href = data.redirect;
                return false;
            }
        });
    });
</script>
