<div class="ser-speak">
  <div class="ser-shop-item to-chat-with-btn shrink-btn">
    <div class="ser-shop-title">
      Dispute Order
      <b>-</b>
    </div>
  </div>
  <div style="display: block;" class="to-chat-with-box shrink-box">
    <div id="commentBox">
      @if(empty($messageList))
        <div class="ser-shop-item" id="ser-shop-title-hide">
          <div class="ser-shop-title">I want to dispute the transaction:</div>
        </div>
        <div class="ser-matter" id="ser-matter">
          <p class="ser-matter-exp">Please select one reason:</p>
          <ul id="matter-type">
            @foreach ($complaintTagList as $id => $name)
              @if($name == 'Other')
                <li class="ser-matter-active" value="{{ $name }}">{{ $name }}</li>
              @else
                <li class="" value="{{ $name }}">{{ $name }}</li>
              @endif
            @endforeach
          </ul>
        </div>
      @endif

      <div class="ser-main">
        <div class="ser-main-box" id="ser-main-box">
          <div id="ser-main">
            @if(!empty($messageList))
              @foreach($messageList as $message)
                <div class="ser-main-p @if($message['type'] == 'Platform') ser-main-p-left @else ser-main-p-right @endif">
                  @if(isset($message['content']) && !empty($message['content']))
                    @if($message['type'] == 'Platform')
                      <p class="ser-main-avatar ser-main-your">Y</p>
                    @endif
                    <div class="ser-main-cont">
                      @if(isset($message['content']['text']) && $message['content']['text'] !== '')
                        <div class="ser-main-cont-text">{!! str_replace("\n", "<br />", $message['content']['text']) !!}</div>
                      @elseif(isset($message['content']['pic']) && !empty($message['content']['pic']))
                        <div class="ser-main-cont-text" onclick="showImg(this)"><img src="{{ $message['content']['pic']['url'] }}" /></div>
                      @endif
                      <p class="ser-main-cont-other">
                        <b>-@if($message['type'] == 'cardholder') {{ $message['by_added'] }} @else {{ $message['type'] }} @endif</b>, <span>{{ date('Y-m-d H:i:s', strtotime('-8 hours', strtotime($message['date_added']))) . 'UTC' }}</span>
                      </p>
                    </div>
                    @if($message['type'] == 'Merchant')
                        <p class="ser-main-avatar ser-main-buy">M</p>
                    @elseif($message['type'] == 'cardholder')
                        <p class="ser-main-avatar ser-main-buy">C</p>
                    @endif
                  @endif
                </div>
              @endforeach
            @endif
          </div>
        </div>
      </div>
    </div>
    <div class="more-feeback">More Feedback</div>
    <div class="ser-foot">
      <form id="commentForm" enctype="multipart/form-data">
        @csrf()
        <div class="ser-foot-input">
          <textarea name="text" id="ser-input" placeholder="@if(empty($messageList)) Let us know more about your order @else More Feedback? @endif" ></textarea>
        </div>
        <div class="ser-foot-email">
          <label for="notice-email">I'd like to receive feedback to this email address: </label>
          <input type="text" name="email_address" value="@if(!empty($orderComplaint)) {{ $orderComplaint['notice_email'] }} @elseif(!empty($orderAddress)) {{ $orderAddress['bill_email'] }} @endif" disabled id="notice-email" />
        </div>
        <div class="ser-foot-btn">
          <div class="ser-foot-btn-symbol" name="default">
            <button type="button" onclick="noticeUpdate(1)" data-name="sigh" @if(!empty($orderComplaint) && $orderComplaint['notice_status'] == '1') class="action" @endif >!</button>
            <button type="button" onclick="noticeUpdate(2)" data-name="ask" @if(!empty($orderComplaint) && $orderComplaint['notice_status'] == '2') class="action" @endif >?</button>
            <button type="button" onclick="noticeUpdate(3)" data-name="hook" @if(!empty($orderComplaint) && $orderComplaint['notice_status'] == '3') class="action" @endif >√</button>
            <button type="button" onclick="noticeUpdate(4)" data-name="finish" @if(!empty($orderComplaint) && $orderComplaint['notice_status'] == '4') class="action" @endif >O</button>
            <button type="button" onclick="noticeUpdate(0)" data-name="default" @if(!empty($orderComplaint) && $orderComplaint['notice_status'] == '0') class="action" @endif >.</button>
          </div>
          <button id="submit-text">Submit</button>
          <button id="reset">Reset</button>
          <div class="ser-foot-btn-img">
            <button id="submitImg">Send Images</button>
            <input type="file" name="imgUpload[]" class="img-upload" id="imgUpload" multiple="multiple" accept="image/png,image/jpg,image/jpeg*" />
          </div>
          <button id="close">Close</button>
        </div>
      </form>
    </div>
  </div>
</div>
