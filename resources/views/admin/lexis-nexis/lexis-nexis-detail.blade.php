@php use Carbon\Carbon; @endphp
<div class="row">
  <div class="col-md-12">
    <div class="card">
      <div class="card-body">

        <!-- 手风琴容器 -->
        <div id="multiAccordion" class="accordion">

          <!-- 面板 1 -->
          <div class="card">
            <div class="card-header" id="headingOne">
              <h5 class="mb-0">
                <a data-toggle="collapse" href="#collapseOne"
                   aria-expanded="true" aria-controls="collapseOne" role="button">
                  {{ admin_trans('lexis-nexis.fields.profile_date') }}
                </a>
              </h5>
            </div>

            <div id="collapseOne" class="collapse show" aria-labelledby="headingOne">
              <div class="card-body">
                <div class="panel panel-default">
                  <!-- Table -->
                  <table class="table">
                    <thead>
                    <tr>
                      <th></th>
                      <th>{{ admin_trans('lexis-nexis.fields.input_date') }}</th>
                      <th>{{ admin_trans('lexis-nexis.fields.profile_date') }}</th>
                      <th>{{ admin_trans('lexis-nexis.fields.search_core_analysis') }}</th>
                    </tr>
                    </thead>
                    <tbody>
                    <tr>
                      <th>{{ admin_trans('lexis-nexis.fields.entity_type') }}</th>
                      <td>{{ data_get($details, 'EntityDetails.EntityType', '') }}</td>
                      <td>Front-end Developer</td>
                      <td>{{ data_get($details, 'EntityDetails.EntityScore') }}</td>
                    </tr>
                    <tr>
                      <th>{{ admin_trans('lexis-nexis.fields.name') }}</th>
                      <td>{{ $details['BestName'] ?? '' }}</td>
                      <td>
                        @php $akas = data_get($details, 'EntityDetails.AKAs', []); @endphp
                        @if(is_array($akas))
                          @foreach($akas as $aka)
                            @continue(empty(data_get($aka, 'Name.Full')))
                            {{ data_get($aka, 'Name.Full') }}
                            @if(data_get($aka, 'Type'))
                              ({{ data_get($aka, 'Type') }})
                            @endif
                            <br>
                          @endforeach
                        @endif
                      </td>
                      <td>{{ data_get($details, 'EntityDetails.EntityScore') }}</td>
                    </tr>
                    <tr>
                      <th>{{ admin_trans('lexis-nexis.fields.gender') }}</th>
                      <td></td>
                      <td>{{ data_get($details, 'EntityDetails.Gender') }}</td>
                      <td></td>
                    </tr>
                    <tr>
                      <th>{{ admin_trans('lexis-nexis.fields.identification') }}</th>
                      <td></td>
                      <td>
                        @php $ids = data_get($details, 'EntityDetails.IDs', []); @endphp
                        @if(is_array($ids))
                          @foreach($ids as $identifier)
                            @continue(empty(data_get($identifier, 'Number')))
                            {{ data_get($identifier, 'Number') }}
                            @if(data_get($identifier, 'Type'))
                              ({{ data_get($identifier, 'Type') }})
                            @endif
                            <br>
                          @endforeach
                        @endif
                      </td>
                      <td></td>
                    </tr>
                    <tr>
                      <th>{{ admin_trans('lexis-nexis.fields.address') }}</th>
                      <td></td>
                      <td>
                        @php $addresses = data_get($details, 'EntityDetails.Addresses', []); @endphp
                        @if(is_array($addresses))
                          @foreach($addresses as $address)
                            @continue(empty(data_get($address, 'Country')))
                            {{ data_get($address, 'Country') }}
                            @if(data_get($address, 'Type'))
                              ({{ data_get($address, 'Type') }})
                            @endif
                            <br>
                          @endforeach
                        @endif
                      </td>
                      <td></td>
                    </tr>
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
          </div>

          <!-- 面板 2 -->
          <div class="card">
            <div class="card-header" id="headingTwo">
              <h5 class="mb-0">
                <a class="collapsed" data-toggle="collapse" href="#collapseTwo"
                   aria-expanded="true" aria-controls="collapseTwo">
                  {{ admin_trans('lexis-nexis.fields.screening_list') }}
                </a>
              </h5>
            </div>

            <div id="collapseTwo" class="collapse show" aria-labelledby="headingTwo">
              <div class="card-body">
                <div class="panel panel-default">
                  <table class="table">
                    <tbody>
                    <tr>
                      <th class="col-md-3">{{ admin_trans('lexis-nexis.fields.screening_list_name') }}</th>
                      <td>
                        @php $file = data_get($details, 'File', []); @endphp
                        {{ data_get($file, 'Name', '') }}
                      </td>
                    </tr>
                    <tr>
                      <th>{{ admin_trans('lexis-nexis.fields.file_build') }}</th>
                      <td>
                        @if(data_get($file, 'Build'))
                          {{ Carbon::parse(data_get($file, 'Build'))->format('Y年m月d日') }}
                        @endif
                      </td>
                    </tr>
                    <tr>
                      <th>{{ admin_trans('lexis-nexis.fields.entity_created') }}</th>
                      <td>
                        @php $entityDetails = data_get($details, 'EntityDetails', []); @endphp
                        @if(data_get($entityDetails, 'DateListed'))
                          {{ Carbon::parse(data_get($entityDetails, 'DateListed'))->format('Y年m月d日') }}
                        @endif
                      </td>
                    </tr>
                    <tr>
                      <th>{{ admin_trans('lexis-nexis.fields.entity_last_update') }}</th>
                      <td>
                        @if(data_get($details, 'DateModified'))
                          {{ Carbon::parse(data_get($details, 'DateModified'))->format('Y年m月d日') }}
                        @endif
                      </td>
                    </tr>
                    <tr>
                      <th>{{ admin_trans('lexis-nexis.fields.position') }}</th>
                      <td>
                        @php $additionalInfo = data_get($entityDetails, 'AdditionalInfo', []); @endphp
                        @if(is_array($additionalInfo))
                          @foreach($additionalInfo as $info)
                            @if(data_get($info, 'Type') == 'Position')
                              {{ data_get($info, 'Value') }}
                            @endif
                          @endforeach
                        @endif
                      </td>
                    </tr>
                    <tr>
                      <th>{{ admin_trans('lexis-nexis.fields.entity_status') }}</th>
                      <td>
                        {{ data_get($details, 'Status') }}
                      </td>
                    </tr>
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
          </div>

          <!-- 面板 3 -->
          <div class="card">
            <div class="card-header" id="headingThree">
              <h5 class="mb-0">
                <a class="collapsed" data-toggle="collapse" href="#collapseThree"
                   aria-expanded="true" aria-controls="collapseThree">
                  {{ admin_trans('lexis-nexis.fields.segments') }}
                </a>
              </h5>
            </div>

            <div id="collapseThree" class="collapse show" aria-labelledby="headingThree">
              <div class="card-body">
                <div class="panel panel-default">
                  <table class="table">
                    <thead>
                    <tr>
                      <th>{{ admin_trans('lexis-nexis.fields.status') }}</th>
                      <th>{{ admin_trans('lexis-nexis.fields.type') }}</th>
                      <th>{{ admin_trans('lexis-nexis.fields.country') }}</th>
                      <th>{{ admin_trans('lexis-nexis.fields.governing_body') }}</th>
                      <th>{{ admin_trans('lexis-nexis.fields.admin_level') }}</th>
                      <th>{{ admin_trans('lexis-nexis.fields.subcategory') }}</th>
                      <th>{{ admin_trans('lexis-nexis.fields.country_role') }}</th>
                      <th>{{ admin_trans('lexis-nexis.fields.governing_institution') }}</th>
                      <th>{{ admin_trans('lexis-nexis.fields.governing_role') }}</th>
                      <th>{{ admin_trans('lexis-nexis.fields.effective_date') }}</th>
                      <th>{{ admin_trans('lexis-nexis.fields.expiration_date') }}</th>
                    </tr>
                    </thead>
                    <tbody>
                    @php $peps = data_get($details, 'PEPs', []); @endphp
                    @if(is_array($peps) && !empty($peps))
                      @foreach($peps as $key => $pep)
                        <tr>
                          <td>
                            @if($key == 0)
                              PEP
                            @else
                              第二级
                            @endif
                          </td>
                          <td>
                            {{ data_get($pep, 'Status') }}
                          </td>
                          <td>
                            @if($key == 0)
                              主要
                            @else
                              第二级
                            @endif
                          </td>
                          <td>
                            {{ data_get($pep, 'Country') }}
                          </td>
                          <td>
                            {{ data_get($pep, 'AdminLevel') }}
                          </td>
                          <td>
                            @if(is_array(data_get($pep, 'SubCategories', [])))
                              @foreach(data_get($pep, 'SubCategories', []) as $sub)
                                {{ $sub }}
                              @endforeach
                            @endif
                          </td>
                          <td>
                            {{ data_get($pep, 'CountryRole') }}
                          </td>
                          <td>
                            {{ data_get($pep, 'GoverningInstitution') }}
                          </td>
                          <td>
                            {{ data_get($pep, 'GoverningRole') }}
                          </td>
                          <td>
                            {{ data_get($pep, 'EffectiveDate') }}
                          </td>
                          <td>
                            {{ data_get($pep, 'ExpirationDate') }}
                          </td>
                        </tr>
                      @endforeach
                    @endif
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
          </div>

          <!-- Panel 4 -->
          <div class="card">
            <div class="card-header" id="headingFour">
              <h5 class="mb-0">
                <a class="collapsed" data-toggle="collapse" href="#collapseFour"
                   aria-expanded="true" aria-controls="collapseFour">
                  {{ admin_trans('lexis-nexis.fields.relationships') }}
                </a>
              </h5>
            </div>

            <div id="collapseFour" class="collapse show" aria-labelledby="headingFour">
              <div class="card-body">
                <div class="panel panel-default">
                  <table class="table">
                    <thead>
                    <tr>
                      <th>{{ admin_trans('lexis-nexis.fields.relationship_entity_name') }}</th>
                      <th>{{ admin_trans('lexis-nexis.fields.relationship_group') }}</th>
                      <th>{{ admin_trans('lexis-nexis.fields.relationship_type') }}</th>
                      <th>{{ admin_trans('lexis-nexis.fields.relationship_ownership_percentage') }}</th>
                      <th>{{ admin_trans('lexis-nexis.fields.segment') }}</th>
                    </tr>
                    </thead>
                    <tbody>
                    @php $relationships = data_get($details, 'Relationships', []); @endphp
                    @if(is_array($relationships) && !empty($relationships))
                      @foreach($relationships as $relationship)
                        <tr>
                          <td>{{ data_get($relationship, 'EntityName') }}</td>
                          <td>{{ data_get($relationship, 'Group') }}</td>
                          <td>{{ data_get($relationship, 'Type') }}</td>
                          <td>{{ data_get($relationship, 'OwnershipPercentage') }}</td>
                          <td>{{ data_get($relationship, 'Segments') }}</td>
                        </tr>
                      @endforeach
                    @else
                      <tr>
                        <td colspan="5" class="text-center text-muted">
                          {{ admin_trans('lexis-nexis.fields.no_relationship_data') }}
                        </td>
                      </tr>
                    @endif
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
          </div>

          <!-- 面板 5 -->
          <div class="card">
            <div class="card-header" id="headingFive">
              <h5 class="mb-0">
                <a class="collapsed" data-toggle="collapse" href="#collapseFive"
                   aria-expanded="true" aria-controls="collapseFive">
                  {{ admin_trans('lexis-nexis.fields.comments') }}
                </a>
              </h5>
            </div>

            <div id="collapseFive" class="collapse show" aria-labelledby="headingFive">
              <div class="card-body">
                <div class="panel panel-default">
                  <div class="card-body">
                    {{ data_get($details, 'EntityDetails.Comments') }}
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div class="card">
            <div class="card-header" id="headingSix">
              <h5 class="mb-0">
                <a class="collapsed" data-toggle="collapse" href="#collapseSix"
                   aria-expanded="true" aria-controls="collapseSix">
                  {{ admin_trans('lexis-nexis.fields.record_sources') }}({{ is_array(data_get($details, 'SourceItems')) ? count(data_get($details, 'SourceItems', [])) : 0 }})
                </a>
              </h5>
            </div>
            <div id="collapseSix" class="collapse show" aria-labelledby="headingSix">
              <div class="card-body">
                <div class="panel panel-default">
                  @php $sourceItems = data_get($details, 'SourceItems', []); @endphp
                  @if(is_array($sourceItems))
                    @forelse($sourceItems as $sourceItem)
                      <a href="{{ data_get($sourceItem, 'SourceURI') }}" target="_blank" class="external-link">
                        <i class="feather icon-external-link"></i>
                        {{ data_get($sourceItem, 'SourceURI') }} <br>
                      </a>
                    @empty
                      <span class="text-muted">
                        {{ admin_trans('lexis-nexis.fields.no_record_sources') }}
                      </span>
                    @endforelse
                  @else
                    <span class="text-muted">
                      {{ admin_trans('lexis-nexis.fields.no_record_sources') }}
                    </span>
                  @endif
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<style>
  th {
    background: #f7fbff;
  }

  table {
    vertical-align: middle;
  }

  tbody tr {
    border-top: 2px solid #e5e7eb;
  }
</style>
