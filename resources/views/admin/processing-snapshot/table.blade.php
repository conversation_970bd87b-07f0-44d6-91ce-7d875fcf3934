<style>
    .custom-table {
        width: 100%;
        table-layout: fixed;
        border-collapse: collapse;
    }

    .custom-table th,
    .custom-table td {
        white-space: normal; /* 允许自动换行 */
        word-break: break-all; /* 长单词或 URL 地址换行到下一行 */
        vertical-align: middle;
        padding: 8px;
    }

    .custom-table th {
        font-weight: bold;
        background-color: #f8f9fa;
    }
</style>

<table class="table table-bordered table-striped text-center custom-table">
    <thead>
        <tr>
            <th>{{ admin_trans('processing-snapshot.labels.month') }}</th>
            <th>{{ admin_trans('processing-snapshot.labels.monthly_processing_volume') }}</th>
            <th>{{ admin_trans('processing-snapshot.labels.transaction_count') }}</th>
            <th>{{ admin_trans('processing-snapshot.labels.average_ticket_value') }}</th>
            <th>{{ admin_trans('processing-snapshot.labels.refund_by_volume') }}</th>
            <th>{{ admin_trans('processing-snapshot.labels.refund_percentage') }}</th>
            <th>{{ admin_trans('processing-snapshot.labels.chargeback_counts') }}</th>
            <th>{{ admin_trans('processing-snapshot.labels.chargeback_percentage_by_count') }}</th>
            <th>{{ admin_trans('processing-snapshot.labels.chargeback_amount') }}</th>
            <th>{{ admin_trans('processing-snapshot.labels.chargeback_percentage_by_amount') }}</th>
            <th>{{ admin_trans('processing-snapshot.labels.average_chargeback_ticket_value') }}</th>
        </tr>
    </thead>
    <tbody>
        @if (!empty($data) && count($data))
            @foreach ($data as $item)
                <tr>
                    <td>{{ $item['date_stat_month'] }}</td>
                    <td>{{ number_format($item['transaction_amount_usd'], 2) }}</td>
                    <td>{{ number_format($item['transaction_qty']) }}</td>
                    <td>{{ number_format($item['avg_transaction_price'], 2) }}</td>
                    <td>{{ number_format($item['refund_amount_usd'], 2) }}</td>
                    <td>{{ number_format($item['refund_rate'] ?? 0.00,  2) }}%</td>
                    <td>{{ number_format($item['dishonour_qty']) }}</td>
                    <td>{{ number_format($item['dishonour_rate_by_count'] ?? 0.00, 2) }}%</td>
                    <td>{{ number_format($item['chargeback_amount_usd'] ?? 0.00, 2) }}</td>
                    <td>{{ number_format($item['dishonour_rate_by_amount'] ?? 0.00, 2) }}%</td>
                    <td>{{ number_format($item['avg_chargeback_amount'] ?? 0.00, 2) }}</td>
                </tr>
            @endforeach

            <!-- 合计行 -->
            <tr class="table-info">
                <th>{{ admin_trans('processing-snapshot.labels.totals') }}</th>
                <td>{{ number_format($total['total_transaction_amount_usd'], 2) }}</td>
                <td>{{ number_format($total['total_transaction_qty']) }}</td>
                <td>{{ number_format($total['total_avg_transaction_price'], 2) }}</td>
                <td>{{ number_format($total['total_refund_amount_usd'], 2) }}</td>
                <td>{{ number_format($total['total_refund_rate'], 2) }}%</td>
                <td>{{ number_format($total['total_dishonour_qty']) }}</td>
                <td>{{ number_format($total['total_dishonour_rate_by_count'] ?? 0.00, 2) }}%</td>
                <td>{{ number_format($total['total_chargeback_amount_usd'] ?? 0.00, 2) }}</td>
                <td>{{ number_format($total['total_dishonour_rate_by_amount'] ?? 0.00, 2) }}%</td>
                <td>{{ number_format($total['total_avg_chargeback_amount'] ?? 0.00, 2) }}</td>
            </tr>

            <!-- 年化交易量 -->
            <tr class="table-success">
                <th>{{ admin_trans('processing-snapshot.labels.annualized_volume') }}</th>
                <td colspan="10" class="text-left font-weight-bold">
                    {{ number_format($total['annualized_volume'], 2) }}
                </td>
            </tr>
        @else
            <tr>
                <td colspan="11" class="text-muted py-4">
                    <i class="feather icon-alert-circle"></i> 暂无数据
                </td>
            </tr>
        @endif
    </tbody>
</table>
