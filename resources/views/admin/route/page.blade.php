<link href="{{ admin_asset('vendor/select/css/jquery.searchableSelect.css')}}" rel="stylesheet">
<style>
  .content {
    text-align:center !important;
  }
  .td-padding {
    padding: 15px 15px 0 0;
  }
</style>
<script src="{{ admin_asset('vendor/select/jquery.searchableSelect.js')}}"></script>
<table class="group-content">
  <colgroup>
    <col width="350">
    <col width="250">
    <col width="100">
  </colgroup>
  <thead class="content">
    <tr>
      <th>账单标识</th>
      <th>权重</th>
      <th>操作</th>
    </tr> 
  </thead>
  <tbody class="group-add">
    @if(!empty($extraAttributes))
      @foreach ($extraAttributes as $key => $vo)
        <tr>
          <td class="td-padding">
            <select name="group[{{$key}}][channel]" >
              <?php 
                echo $channel;
              ?>
            </select>
          </td>
          <td class="td-padding">
            <div class="input-group">
              <input type="text" name="group[{{$key}}][weight]" value="{{$vo['weight']}}" class="form-control field_group_0 field_weight_0 field_weight" placeholder="输入 权重">
          </div>
          </td>
          <td class="content td-padding">
              <span style="cursor: pointer " class="del-group"> <a href="javascript:void(0)"><i class="fa fa-trash-o"></i></a></span>
              <span style="cursor: pointer" class="copy-group"> <a href="javascript:void(0)"><i class="fa fa-copy"></i></a></span>
          </td>
        </tr>
        <script>
          $("select[name='group[{{$key}}][channel]']").val("<?php echo $vo['channel']; ?>")
          $("select[name='group[{{$key}}][channel]']").searchableSelect();
        </script>
      @endforeach
    @endif
  </tbody>
</table>
<div class="col-md-9 " style="margin-top: 8px">
  <div class="add-group btn btn-primary btn-outline btn-sm"><i class="feather icon-plus"></i>&nbsp;新增</div>
</div>
<script>
$(function(){
  var channel = "<?php echo $channel; ?>"

  // 复制按钮
  $(document).on('click', '.copy-group', function () {
      let val = $(this).parents('tr').find('select option:selected').val();
      if (val == '') {
          layer.msg('复制内容为空！');
      } else {
          var input = document.createElement('input'); // 创建input元素
          // 把文字放进input中，供复制
          input.value = val;
          document.body.appendChild(input); // 向页面底部追加输入框
          // 选中创建的input
          input.select();
          var copy_result = document.execCommand('copy'); // 执行复制命令
          if (copy_result) {
              layer.msg('已复制到粘贴板！');
          } else {
              layer.msg('复制失败！');
          }
          document.body.removeChild(input); //删除动态创建的节点
      }
  });

  // 新增账单标识
  $('.add-group').on('click', function(){
    var count = $('.group-add>tr').length;
    var html = "<tr><td class='td-padding'>" +
        "<select style='width: 100%'' name='group["+ count +"][channel]'>" +
        channel +
        "</select></td>" +
      "<td class='td-padding'>" +
        "<div class='input-group'>" +
          "<input type='text' name='group["+ count +"][weight]' value='100' class='form-control field_group_0 field_weight_0 field_weight' placeholder='输入 权重'>" +
      "</div>" +
      "</td>" +
      "<td class='content td-padding'>" +
        "<span style='cursor: pointer' class='del-group'> <a href='javascript:void(0)'><i class='fa fa-trash-o'></i></a></span>" +
        "<span style='cursor: pointer' class='copy-group'> <a href='javascript:void(0)'><i class='fa fa-copy'></i></a></span>" +
      "</td>" +
    "</tr>";
    $('.group-add').append(html)
    $("select[name='group["+ count +"][channel]']").searchableSelect();
  })
  
  // 删除账单标识
  $(document).on('click', '.del-group', function(){
    $(this).parents('tr').remove();
  })
});
</script>