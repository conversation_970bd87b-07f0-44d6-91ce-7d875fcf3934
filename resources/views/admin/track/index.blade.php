<div style="max-height: 800px;overflow: auto">
  @if(!empty($trackList))
    @foreach($trackList as $list)
      <div style="padding: 10px">
        {{ $list['tracking_number'] }}
      </div>

      <div style="padding: 10px">
        <label for="">目的地: </label><h4>{{ isset($list['destination_country']) ? $list['destination_country'] : '' }}</h4>
      </div>

      @if(isset($list['destination_event_list']) && !empty($list['destination_event_list']))
        @foreach($list['destination_event_list'] as $value)
          <div style="padding: 10px">
            {{ $value['date'] . ', ' . (!empty($value['address']) ? $value['address'] . ', ' : '') . $value['details'] }} <br>
          </div>
        @endforeach
      @endif

      <div style="padding: 10px">
        <label for="">发件地: </label><h4>{{ isset($list['original_country']) ? $list['original_country'] : '' }}</h4>
      </div>

      @if(isset($list['original_event_list']) && !empty($list['original_event_list']))
        @foreach($list['original_event_list'] as $value)
          <div style="padding: 10px">
            {{ $value['date'] . ', ' . (!empty($value['address']) ? $value['address'] . ',' : '') . $value['details'] }} <br>
          </div>
        @endforeach
      @endif

      @if(count($trackList) > 1)
        <hr style="margin: 0 auto">
      @endif

    @endforeach
  @endif
</div>
