<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <meta http-equiv="X-UA-Compatible" content="ie=edge">
  <link href="{{ admin_asset('vendor/layui/css/layui.css')}}" rel="stylesheet">
  <title>收银台页面</title>
  <style>
    body {
      background-color: #FAFAFA;
    }

    .whole {
      width: 900px;
      width: 700px;
      margin: 100px auto;
      position: relative;
    }

    .content {
      background-color: #fff;
      width: 620px;
      height: 550px;
    }

    .content-form {
      margin: auto;
      width: 530px;
      height: 395px;
    }

    h2 {
      margin-bottom: 20px;
    }

    .content-input {
      border: 1px solid #e9e6e6;
      padding: 30px 14px 14px 30px;
      border-radius: 16px;
    }

    .div-input {
      width: 230px;
      float: left;
      margin-right: 10px;
    }

    .div-input-row {
      padding-right: 14px;
    }

    .content-div-img {
      margin: 10px 0px 50px 35px;
    }

    .content-img {
      height: 36px;
    }

    .content-top {
      padding: 20px 0 10px 45px;
    }

    .content-top-span {
      font-weight: bold;
      font-size: 16px;
      margin-left: 5px;
    }

    .whole-h2 {
      font-weight: bold;
    }

    .content-form-amount {
      margin-top: 20px;
      font-weight: bold;
      font-size: 16px;
    }

    .content-form-amount-span {
      color: #F47029;
    }

    .content-form-button {
      margin-top: 30px;
      text-align: center;
    }

    .content-form-button-submit {
      background-color: #5257FF;
    }

    .content-form-button-cancel {
      border-color: #5257FF !important;
      color: #5257FF !important;
      margin-left: 20px;
    }

    .content-form-button button {
      width: 120px;
      border-radius: 10px;
    }
  </style>
</head>

<body>
  <div class="whole">
    <h2 class="whole-h2">支付信息</h2>
    <div class="content">
      <div class="content-top">
        <img src="{{url('/media/cashier/OrderToBePaid.png')}}" style="color: #5257FF" alt=""><span class="content-top-span">订单号 </span>
        <span class="content-top-span content-form-amount-span">{{$orderNumber}}</span>
      </div>
      <div class="content-top">
        <img src="{{url('/media/cashier/CardInfo.png')}}" alt=""><span class="content-top-span">卡信息</span>
      </div>
      <div class="content-form">
        <form class="layui-form">
          <input type="hidden" name="_token" value="{{ csrf_token() }}" />
          <div class="content-input">
            <div class="layui-form-item">
              <div class="div-input-row">
                <input type="text" name="card_number" maxlength="19" oninput="this.value=this.value.replace(/\D/g,'')" placeholder="卡号" autocomplete="off" class="layui-input">
              </div>
            </div>

            <div class="layui-form-item">
              <div class="div-input">
                <input type="text" name="card_date" onkeyup="myFunction(event)" maxlength="16" oninput="this.value=this.value.replace(/[^0-9/]/g,'')" maxlength="5" placeholder="到期日（MM/YY）" autocomplete="off" class="layui-input">
              </div>
              <div class="div-input">
                <input type="text" name="card_code" maxlength="4" oninput="this.value=this.value.replace(/\D/g,'')" placeholder="安全码（CVV）" autocomplete="off" class="layui-input">
                <input hidden name="form_json" value="{{$formJson}}">
              </div>
            </div>
          </div>

          <div class="content-div-img">
            <img src="{{url('/media/shoplazza/card.jpg')}}" class="content-img">
            <img src="{{url('/media/shoplazza/card_cvv.jpg')}}" class="content-img">
          </div>

          <div class="content-form-amount">
            <label>币种：<span class="content-form-amount-span">{{$currency}}</span></label><br><br>
            <label>金额：<span class="content-form-amount-span">{{$amount}}</span></label>
          </div>

          <div class="layui-form-item content-form-button">
            <button type="button" id="post" class="layui-btn content-form-button-submit" lay-submit lay-filter="formDemo">提交支付</button>
          </div>
        </form>
      </div>
    </div>
  </div>

</body>
<script src="{{ admin_asset('vendor/dcat-admin/dcat/plugins/vendors.min.js')}}"></script>
<script src="{{ admin_asset('vendor/layui/layui.js')}}"></script>
<script>
  layui.use(['layer', 'form'], function() {
    var layer = layui.layer;
    var form = layui.form;
  });

  // 监听到期日输入框
  function myFunction(e) {
    let keynum = window.event ? e.keyCode : e.which;
    let cardDate = $('input[name="card_date"]');

    if (/^\d+$/.test(e.key) || keynum == 8) {
      if (cardDate.val().length == 2) {
        if (keynum == 8) {
          cardDate.val(cardDate.val().slice(0, 1));
        } else {
          cardDate.val(cardDate.val() + '/');
        }
      }
    }
  }

  $('#post').on('click', function() {
    btnOff();
    let cardNumber = $('input[name="card_number"]').val();
    let cardDate = $('input[name="card_date"]').val();
    let cardCode = $('input[name="card_code"]').val();
    let formJson = $('input[name="form_json"]').val();

    if (!cardNumber || !cardDate || !cardCode || !formJson) {
      layer.msg('参数不全！');
      btnOn();
      return;
    }

    if (cardNumber.length < 14 || cardNumber.length > 19) {
      layer.msg('请输入14到19位卡号！');
      btnOn();
      return;
    }
    if (cardCode.length < 2 || cardCode.length > 3) {
      layer.msg('请输入正确cvv！');
      btnOn();
      return;
    }

    if (!/^(0[1-9]|1[0-2])\/\d{2}$/.test(cardDate)) {
      layer.msg('请输入正确到期日');
      btnOn();
      return;
    }

    // 切割到期日
    let cardDateArr = cardDate.split("/");

    //加载
    var index = layer.load();

    $.ajax({
      type: "POST",
      url: "{{ url('/api/v1/cashier/pay') }}",
      data: {
        cardNumber: cardNumber,
        expirationMonth: cardDateArr[0],
        expirationYear: cardDateArr[1],
        cvv: cardCode,
        formJson: formJson,
      },
      success: (response) => {
        // 关闭加载
        layer.close(index);
        if (response) {
          if (response.errors && response.message) {
            layer.msg(response.message, {
              time: 3000
            });
            setTimeout(function() {
              if (response.redirectUrl && response.redirectUrl != '') {
                window.location = response.redirectUrl;
              }
            }, '3000');
            if (response.IsOpenBtn) {
              btnOn();
            }
          } else {
            if (response.redirectUrl && response.redirectUrl != '') {
              window.location = response.redirectUrl;
            }
          }
        } else {
          layer.msg('Payment failed', {
            time: 3000
          });
        }
      }
    });
  })

  function btnOff() {
    $('#post').addClass('layui-btn-disabled');
    $('#post').attr('disabled', true);
  }

  function btnOn() {
    $('#post').removeClass('layui-btn-disabled');
    $('#post').attr('disabled', false);
  }
</script>

</html>