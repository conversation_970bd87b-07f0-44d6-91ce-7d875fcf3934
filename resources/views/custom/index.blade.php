@extends('layouts.app')

@section('title', 'title')

@section('styles')
    <style>
        /* 清除 bootstrap 样式 */
        a {color: #1F4691;}
        p {margin-bottom: 0;}
        label {font-size: medium;}
        .container {max-width: 100%; background-color: #E0E5EC;}
        html, body {width: 100%; height: 100%; /*font-family: "Fakt";*/ background-color: #E0E5EC;}
        /*input,select,textarea,button {font-family: "Fakt";}*/

        /* scrollbar color */
        ::-webkit-scrollbar-thumb{border-radius: 5px;background-color: #eee;}

        /* 去除移动端点击存在的阴影 */
        a,img,button,input,textarea,div{-webkit-tap-highlight-color:rgba(255,255,255,0);}

        .ser {width: 90%; margin: 0 auto; border-radius: 4px; overflow: hidden;}
        .ser-box {margin-bottom: 20px;}
        .ser-head {height: 60px; padding: 10px 0; box-sizing: border-box; }
        .ser-head img {vertical-align: bottom; float: right; height: 100%;}
        .ser-head a {display: inline-block; margin-bottom: 5px; font-size: 39px; font-weight: 600;}
        .ser-head a:hover {color: #1F4691;}

        .ser-shop {width: 49.8%; float: left;}
        .ser-shop-item {position: relative; border: 1px solid #e6e6e6; border-radius: 5px; overflow: hidden; background-color: #F6F9FC; margin-bottom: 8px;}
        .ser-shop-item .tabs {background-color: #F6F9FC !important;}
        .ser-shop-item-cont1 {max-height: 460px; overflow-y: auto;}
        .ser-shop-title {padding: 0 10px; height: 40px; line-height: 43px; font-size: 23px; color: #333; font-weight: 600;}
        .ser-shop-title b {float: right; display: none;}
        .ser-shop-cont {border-bottom: 1px solid #DDDDDD; font-size: 15px; line-height: 1.4;}
        .ser-shop-cont:last-child {border-bottom: none;}
        .ser-shop-cont-item {display: inline-block; width: 98%; padding: 10px;  vertical-align: top;}
        .ser-shop-cont-item p {font-weight: 700; display: inline-block;}
        .ser-shop-cont-item-col2 {display: inline-block; width: 49.5%; padding: 10px;  vertical-align: top;}
        .ser-shop-cont-item-col2 p {font-weight: 700; display: inline-block;}
        .ser-shop-cont-item-col4 {display: inline-block; width: 24.5%; padding: 10px;  vertical-align: top;}
        .ser-shop-cont-item-col4 p {font-weight: 700; display: inline-block;}
        .shipping-status-tabs-head {height: 50px; padding-left: 5px; border-bottom: 1px solid #dfdfdf;}
        .shipping-status-tabs-head li {display: inline-block; height: 50px; line-height: 48px; margin: 0 4px -1px 0; padding: 0; position: relative;}
        .shipping-status-tabs-head li a {display: inline-block; padding: 0 10px; font-size: 14px; color: #111;}
        .shipping-status-tabs-head-active a {border-bottom: 2px solid #39c; color: #39c !important;}
        .shipping-status-tabs-main-list {padding: 20px; display: none;}
        .shipping-status-tabs-main-list-active {display: block;}
        .shipping-status-tabs-main-list div {margin: 0 0 10px;}
        .shipping-status-tabs-main-list-title {color: #FF0000; line-height: 1.25; font-size: 16px;}
        .shipping-status-tabs-main-list-item {border-bottom: 1px solid #E2E2DF; font-size: 14px; color: #404040; line-height: 1.5;}

        .ser-speak {width: 49.8%; float: right; border-radius: 5px; overflow: hidden;}
        .to-chat-with-btn {display: none;}
        /*.to-chat-with-box {display: block;}*/
        .ser-matter {background-color: #F6F9FC; padding: 10px 0;}
        .ser-matter-exp {padding: 0 10px; height: 31px; line-height: 31px; font-size: 15px;}
        .ser-matter ul {margin-top: 3px; padding: 0 10px; -webkit-user-select:none; -moz-user-select:none; -ms-user-select:none; user-select:none;}
        .ser-matter ul li {display: inline-block; height: 31px; line-height: 31px; border: 1px solid #ddd; padding: 0px 10px; border-radius: 3px; margin-bottom: 5px; font-size: 15px; cursor: pointer;}
        .ser-matter-active {background-color: #EC6C00; border-color: #EC6C00 !important; box-shadow: 0px 0px 3px 2px #ddd; color: #fff;}

        .ser-main {padding: 10px; background-color: #F6F9FC;}
        .ser-main-box {overflow-y: auto; /*max-height: 650px;*/ max-height: 520px; padding: 0 13px 10px 5px; border-top: 1px solid #DDDDDD; margin-top: 8px;}
        .ser-main-order {margin-left: 10px; margin-top: 3px;}
        .ser-main-p {position: relative; margin-top: 30px;}
        .ser-main-p-left {text-align: left;}
        .ser-main-p-right {text-align: right;}
        .ser-main-cont {display: inline-block; padding: 15px; width: 600px; margin-top: 2px; color: #111; border-radius: 4px; box-sizing: border-box; font-size: 14px; letter-spacing: 1.5px; text-align: left; word-wrap: break-word;
            word-break: normal; position: relative; background-color: #fff; box-shadow: 0px 0px 4px 2px #ccc;}
        .ser-main-p-left .ser-main-cont{margin-left: 15px;}
        .ser-main-p-right .ser-main-cont{margin-right: 15px;}
        .ser-main-cont::before {content: ''; position: absolute; top: 18px; width: 10px; height: 10px; background-color: #fff; transform: rotate(135deg); box-shadow: 0px 0px 5px 2px #ccc;}
        .ser-main-cont::after {content: ''; position: absolute; top: 9px; width: 15px; height: 30px; background-color: #fff;}
        .ser-main-p-left .ser-main-cont::before {left: -4px;}
        .ser-main-p-left .ser-main-cont::after {left: 0px;}
        .ser-main-p-right .ser-main-cont::before {right: -4px;}
        .ser-main-p-right .ser-main-cont::after {right: 0px;}
        .ser-main-cont img {max-width: 100%;}
        .ser-main-avatar {display: inline-block; width: 46px; height: 46px; border: 2px solid; vertical-align: top; border-radius: 50%; text-align: center; line-height: 46px; font-size: 17px; letter-spacing: 1px;}
        .ser-main-cont-other {margin-top: 13px;}
        .ser-main-buy {border-color: #CDCDCD;}

        .more-feeback {display: none;}
        .ser-foot {padding: 10px; box-sizing: border-box; background-color: #F6F9FC; }
        .ser-foot-input {height: 200px; margin: 10px 0;}
        .ser-foot-input textarea {border: none; outline: none; width: 100%; height: 100%; padding: 10px; resize: none; font-size: 15px; letter-spacing: 1px; color: #111; background-color: transparent;background-color: #fff;
            box-shadow: 0px 0px 4px 2px #ccc;}
        .ser-foot-email {margin: 12px 0;font-size: 15px;}
        .ser-foot-email input {border: none; outline: none; border-bottom: 1px solid #ccc; background-color: transparent; height: 25px; width: 250px; margin-left: 5px; font-size: 15px; color: #333;}
        .ser-foot-btn {height: 50px; line-height: 40px; padding: 0 20px; text-align: right;}
        .ser-foot-btn span {font-size: 13px; color: #777; margin-right: 8px; vertical-align: middle;}
        .ser-foot-btn button {background-color: transparent; padding: 0; margin: 0; margin-left: 7px; font-size: 14px; letter-spacing: 2px; color: #333; border-radius: 4px; border: 1px solid #DFE0E0; padding: 0px 10px; line-height: 35px; cursor: pointer;}
        .ser-foot-btn #close{display: none;}
        /*.img-upload {display: none;}*/
        .ser-foot-btn-img {display: inline-block; position: relative;}
        .ser-foot-btn-img input {position: absolute; top: 0; left: 0; width: 100%; height: 100%; opacity: 0; }

        @media only screen and (max-width: 1024px) {
            .ser {width: 96%;}
            .ser-head img {height: 31px;}
            .ser-head a {font-size: 24px;}

            .ser-shop {width: 100%;}
            .ser-shop-title {height: 36px; line-height: 36px; font-size: 19px;}
            .ser-shop-cont-item {padding: 0 8px; margin: 5px 0;}
            .ser-shop-cont-item-col2, .ser-shop-cont-item-col4 {display: block; width: 100%; padding: 0px 8px; margin: 5px 0;}
            .ser-shop-title b {display: block;}
            .to-chat-with-btn {display: block;}
            .items-purchased-box, .shipping-status-box, .to-chat-with-box {display: none;}

            .ser-main-box::-webkit-scrollbar-thumb{border-radius: 5px; background-color: #aaa;}
            .ser-main-box::-webkit-scrollbar{width: 4px;}

            #ser-shop-title-hide {display: none;}
            .ser-speak {width: 100%;}
            .ser-main {padding: 0px;}
            .ser-main-box {padding: 0;}
            #ser-main {padding: 0 8px 10px 5px;}
            .ser-main-p {margin-top: 18px;}
            .ser-main-cont {padding: 10px; font-size: 13px; margin-top: 0px;}
            .ser-main-cont-text {padding-left: 6px;}
            .ser-main-cont-other {margin-top: 10px;}
            .ser-main-avatar {width: 30px; height: 30px; line-height: 30px; margin-top: 9px; font-size: 15px;}
            .ser-main-cont {width: 83%;}
            .ser-main-cont img {width: 90%; object-fit: contain;}
            .ser-main-p-left .ser-main-cont {margin-left: 10px;}
            .ser-main-p-right .ser-main-cont {margin-right: 10px;}

            .more-feeback {display: inline-block; margin: 8px 0; border-radius: 3px; width: 100%; height: 33px; line-height: 33px; text-align: center; font-size: 15px; color: #fff; background-color: #1F4691;}
            .ser-foot {display: none;position: fixed; top: 0; left: 0; right: 0; bottom: 0;}
            .ser-foot form {display: block;}
            .ser-foot-input {height: 120px;}
            .ser-foot-input textarea {padding: 3px; font-size: 14px;}
            .ser-foot-btn {padding: 0;}
            .ser-foot-btn button {padding: 0 5px; height: 29px; line-height: 29px; font-size: 13px; margin-left: 2px;}
            .ser-foot-btn #close {display: inline-block;}
        }
    </style>
@endsection

@section('content')

    <div class="ser">
        <div class="ser-head">
            <a href="javascript:;">Customer Care</a>
        </div>
        <div class="ser-box clearfix">
        @include('custom._left')

        @include('custom._right')
        </div>
    </div>

@endsection

@section('scripts')
    <script type="text/javascript">
        window.history.replaceState(null, null, window.location.href); // 清楚刷新页面后, 反复弹出提交表单的默认事件
        var billName = @if(!empty($orderAddress)) '{{ htmlspecialchars($orderAddress['bill_name'], ENT_QUOTES) }}' @else '' @endif

        // 获取 dom
        var ser_input = $("#ser-input"); // 文字输入
        var ser_main_box = $("#ser-main-box"); // 消息显示框
        var ser_main = $("#ser-main");
        var submit = $("#submit-text"); // 发送
        var img_upload = $("#imgUpload"); // 图片上传媒介
        var reset = $("#reset"); // 重置
        var matter_type = $("#matter-type"); // 问题类型
        var matter_title = "Other"; // 问题标题, 默认 Other
        var formSubmit = true; // 防止短时间重复提交的变量
        var more_feeback = $(".more-feeback");
        var ser_foot = $(".ser-foot");
        var phone = false;
        var close = $("#close");
        var message_list = <?php echo empty($messageList) ? 0 : 1 ?>; // 获取是否拥有选项

        // more Feedback 点击
        more_feeback.click(function (){
            ser_foot.show(100);
            ser_input.focus();
        });

        // close 关闭手机端的输入框
        close.click(function (){
            ser_foot.hide(100);
            ser_input.blur();
            return false;
        });

        // 判断是否是手机
        if (Math.round($(document.body).width()) < 1024){
            phone = true;

            // 给对话框设定高度 (对话框上面的总高度为 198, 对话框 下面的按钮高度为 43)
            // 如果有选项时, 高度变小点
            if (!message_list){
                ser_main_box.height(0);
            } else {
                ser_main_box.height($(document.body).height() - (198 + 80));
            }

            // 手机端收起操作
            $(".shrink-btn").each(function (index){
                $(this).click(function (){
                    // 判断当前对应的 div 是否是展开; 如果是展开, 就收起; 如果是收起就展开, 其他的收起
                    if ($(".shrink-box").eq(index).css("display") == "block"){
                        $(this).find("b").text("+");
                        $(".shrink-box").eq(index).slideUp(230);
                    } else {
                        $('.shrink-btn').find("b").text("+");
                        $(this).find("b").text("-");
                        $(".shrink-box").slideUp(230);
                        $(".shrink-box").eq(index).slideDown(230);
                    }
                });
            });
        }

        // 物流选项卡
        $(".shipping-status-tabs-head li").each(function (index){
            $(this).click(function (){
                $(this).addClass("shipping-status-tabs-head-active").siblings().removeClass("shipping-status-tabs-head-active");
                $(".shipping-status-tabs-main-list").eq(index).addClass("shipping-status-tabs-main-list-active").siblings(".shipping-status-tabs-main-list").removeClass("shipping-status-tabs-main-list-active");
            });
        });

        function sendData(type) {
            var form =new FormData($("#commentForm")[0]);

            // 如果是第一次会话向后台传递问题类型
            form.append("title", matter_title); // 将用户选中的标题传递给后台

            // 如果是发送图片, 那么文字就不发送
            if (type=="img"){
                form.delete('text');
            }

            $.ajax({
                url: "{{ route('customs.service', $token) }}",
                type: "POST",
                data: form,
                cache: false,
                processData: false,
                contentType: false,
                success: function(result){
                    if (!result.error){
                      Dcat.success(result.msg);
                      $("#ser-matter").hide();
                      $("#ser-shop-title-hide").hide();

                      var messageFlag = message_list;

                      // 提交到后台成功后, 追加 DOM
                      if (type == "str"){
                        // 追加尾部文字
                        appendMain("str", result.content, messageFlag);
                      } else if (type == "img"){
                        // 追加尾部图片
                        var fileList = img_upload[0].files;
                        if (fileList.length > 0) {
                          for (var i in fileList) {
                            if (i == 'length') {
                              break;
                            }

                            // 多张图片发送时只自动回复一次
                            if (!message_list) {
                              messageFlag = i < fileList.length - 1 ? 1 : 0;
                            }

                            // 预览文件
                            imgPre(fileList[i], messageFlag);
                          }
                        }
                      }

                      // 发送完毕, 手机端隐藏掉输入框(先判断是否是手机端)
                      if (phone){
                        ser_foot.hide(100);
                        ser_input.blur(); // 让其失去焦点 (让手机端的键盘消失)
                      }
                    } else {
                      Dcat.error(result.msg);
                    }
                }
            });
        }

        // 防止短时间重复提交
        function isSubmit (){
            if (formSubmit && ser_input.val() != ""){
                formSubmit = false;
                sendData("str");
                setTimeout(function () {
                    formSubmit = true;
                }, 1000);
            }
        }

        // 图片上传, 当图片上传发送变化时触发
        img_upload.change(function (){
            // 发送数据
            sendData("img");
        });

        // 问题类型点击
        matter_type.find("li").click(function (){
            for (var i=0, j=matter_type.find("li").length; i < j ; i++){
                $(matter_type.find("li")[i]).removeClass('ser-matter-active');
            }
            $(this).addClass('ser-matter-active');
            matter_title = $(this).attr('value');
            $("#matter-title").text(matter_title);
        });

        // 点击发送按钮
        submit.click(function (){
            isSubmit();
            return false;
        });

        // 重置输入框
        reset.click(function (){
            ser_input.val("");
            return false;
        });

        // (视觉)发送图片按钮, 取消默认事件
        $("#submitImg").click(function (){
            return false;
        });

        // 追加尾部消息
        function appendMain (type, value, messageFlag){
            // 重置高度
            if (phone){
                ser_main_box.height($(document.body).height() - (198 + 80));
            }

            var dom = '';

            // 自动回复
            if (!messageFlag){
                var autoMessage = 'Dear customer, the seller has received your dispute and will respond to you ASAP';
                dom += '<div class="ser-main-p ser-main-p-right">';
                dom += '<div class="ser-main-cont">';
                dom += '<div class="ser-main-cont-text">' + autoMessage.replace(/\n/g, "<br/>") + '</div>';
                dom += '<p class="ser-main-cont-other">';
                dom += '<b>-Platform</b>, <span>' + getDate() + '</span>';
                dom += '</p>';
                dom += '</div>';
                dom += '<p class="ser-main-avatar ser-main-buy">P</p>';
                dom += '</div>';

                message_list = 1; // 重新赋值
            }

            if (type == "str"){
                // 如果没有任何输入, 不发送
                if (value == ""){
                    return false;
                }

                dom += '<div class="ser-main-p ser-main-p-left">';
                dom += '<p class="ser-main-avatar ser-main-you">Y</p>';
                dom += '<div class="ser-main-cont">';
                dom += '<div class="ser-main-cont-text">' + value.replace(/\n/g, "<br/>") + '</div>';
                dom += '<p class="ser-main-cont-other">';
                dom += '<b>-' + billName + '</b>, <span>' + getDate() + '</span>';
                dom += '</p>';
                dom += '</div>';
                dom += '</div>';

                // 发送后置空 textarea
                ser_input.val("");
            }

            if (type == "img"){
                dom += '<div class="ser-main-p ser-main-p-left">';
                dom += '<p class="ser-main-avatar ser-main-you">Y</p>';
                dom += '<div class="ser-main-cont">';
                dom += '<div class="ser-main-cont-text"onclick="showImg(this)"><img src="' + value + '" /></div>';
                dom += '<p class="ser-main-cont-other">';
                dom += '<b>-' + billName + '</b>, <span>' + getDate() + '</span>';
                dom += '</p>';
                dom += '</div>';
                dom += '</div>';
            }

            ser_main.prepend(dom);
        }

        // 图片上传前的预览
        function imgPre (file_obj, messageFlag){
            var reader = new FileReader();

            reader.readAsDataURL(file_obj);
            reader.onload = function (){
                var img_src = this.result;
                appendMain("img", img_src, messageFlag);
            }
        }

        // 查看/放大, 图片
        function showImg (obj){
            var imgDiv = $(obj);
            var items = new Array();
            for (var i = 0; i < imgDiv.children("img").length; i++ ){
                items.push({
                    src: imgDiv.children("img")[i].src,
                    title: "Image " + (i+1)
                });
            }
            var options = {
                index: 0
            };
            // var viewer = new PhotoViewer(items, options);
        }

        // 时间
        function getDate (){
            var date = new Date();
            var year = date.getFullYear();
            var month = dateFormat(date.getMonth()+1);
            var day = dateFormat(date.getDate());
            var time = dateFormat(date.getHours());
            var minute = dateFormat(date.getMinutes());
            var seconds = dateFormat(date.getSeconds());
            return year+"-"+month+"-"+day+" "+time+":"+minute+":"+seconds+"UTC";
        }

        function dateFormat (date){
            return date < 10 ? "0"+date : date;
        }
    </script>
@endsection
