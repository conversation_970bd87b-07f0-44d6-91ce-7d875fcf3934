@if ($jobs['total'])
  <table class="table">
    <thead>
    <tr>
      <th scope="col">任务</th>
      <th scope="col">耗时</th>
      <th scope="col">时间</th>
    </tr>
    </thead>
    <tbody>
    @foreach($jobs['jobs']->take(5) as $job)
      <tr class="table-content">
        <td>{{$job->name}}</td>
        <td>{{amount_format($job->failed_at - $job->completed_at)}}s</td>
        <td>{{date('Y-m-d H:i:s', $job->failed_at)}}</td>
      </tr>
    @endforeach
    </tbody>
  </table>
@else
  <div class="d-flex flex-column align-items-center justify-content-center card-bg-secondary p-5 bottom-radius">
    <span>There aren't any jobs.</span>
  </div>
@endif

<style>
  .table-content > td {
    display: table-cell;
    vertical-align: middle
  }
</style>
