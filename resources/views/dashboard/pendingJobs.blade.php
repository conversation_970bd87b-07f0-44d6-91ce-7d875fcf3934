@if ($jobs['total'])
  <table class="table">
    <thead>
    <tr>
      <th scope="col">任务</th>
      <th scope="col">时间</th>
    </tr>
    </thead>
    <tbody>
    @foreach($jobs['jobs']->take(5) as $job)
      <tr class="table-content">
        <td>
          {{$job->name}}
          <br />
          @if(!empty($job->payload->tags))
          <small class="text-muted">
            <span class="text-break">
              @if (is_array($job->payload->tags))
                Tags: {{implode(',', $job->payload->tags)}}</span>
              @elseif (is_string($job->payload->tags))
                Tags: {{$job->payload->tags}}</span>
              @endif
            </span>
          </small>
          @endif
        </td>
        <td>{{date('Y-m-d H:i:s', $job->payload->pushedAt)}}</td>
      </tr>
    @endforeach
    </tbody>
  </table>
@else
  <div class="d-flex flex-column align-items-center justify-content-center card-bg-secondary p-5 bottom-radius">
    <span>There aren't any jobs.</span>
  </div>
@endif

<style>
  .table-content > td {
    display: table-cell;
    vertical-align: middle
  }
</style>
