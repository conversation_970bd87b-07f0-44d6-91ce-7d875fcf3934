<p style="box-sizing: border-box; margin-top: 0px; margin-bottom: 0px; color: rgb(60, 67, 83); font-family: 微软雅黑; font-size: 16px; ">
  Dear <strong>{{ $params['bill_name'] }}</strong>
</p>
<p style="box-sizing: border-box; margin-top: 0px; margin-bottom: 0px; color: rgb(60, 67, 83); font-family: 微软雅黑; font-size: 16px; ">
  <br/>
</p>
<p style="margin-top: 0px; margin-bottom: 0px; white-space: normal; box-sizing: border-box; font-size: 14px; background-color: rgb(255, 255, 255); text-align: justify; font-family: Calibri;">
  <span style="box-sizing: border-box; font-family: 微软雅黑; font-size: 16px;"><span style="font-family: 微软雅黑; text-align: justify; background-color: rgb(255, 255, 255);">Congratulation!</span> You have successfully purchased <strong>{{ $params['product_name'] }}</strong> on <strong>{{ $params['url_name'] }}</strong> on <strong>{{ $params['date_complete'] }}</strong> .&nbsp</span>
</p>
<p style="margin-top: 0px; margin-bottom: 0px; white-space: normal; box-sizing: border-box; font-size: 14px; background-color: rgb(255, 255, 255); text-align: justify; font-family: Calibri;">
  <br/>
</p>
<p style="margin-top: 0px; margin-bottom: 0px; white-space: normal; box-sizing: border-box; font-size: 14px; background-color: rgb(255, 255, 255); text-align: justify; font-family: Calibri;">
  <span style="box-sizing: border-box; font-family: 微软雅黑; font-size: 16px;">Click <strong>{{ $params['service_url'] }}</strong> to see details of your order.</span>
</p>
<p style="margin-top: 0px; margin-bottom: 0px; white-space: normal; box-sizing: border-box; font-size: 14px; background-color: rgb(255, 255, 255); text-align: justify; font-family: Calibri;">
  <br/>
</p>
<p style="margin-top: 0px; margin-bottom: 0px; white-space: normal; box-sizing: border-box; font-size: 14px; background-color: rgb(255, 255, 255); text-align: justify; font-family: Calibri;">
  <span style="box-sizing: border-box; font-family: 微软雅黑; font-size: 16px;"><strong>IMPORTANT: You can track shipping status or dispute your order via above link.</strong></span>
</p>
<p style="margin-top: 0px; margin-bottom: 0px; white-space: normal; box-sizing: border-box; font-size: 14px; background-color: rgb(255, 255, 255); text-align: justify; font-family: Calibri;">
  <span style="box-sizing: border-box; font-family: 微软雅黑; font-size: 16px;"><br/></span>
</p>
<p style="margin-top: 0px; margin-bottom: 0px; white-space: normal; box-sizing: border-box; font-size: 14px; background-color: rgb(255, 255, 255); text-align: justify; font-family: Calibri;">
  <span style="box-sizing: border-box; font-family: 微软雅黑; font-size: 16px;">Have a nice day!</span>
</p>
<p style="margin-top: 0px; margin-bottom: 0px; white-space: normal; box-sizing: border-box; font-size: 14px; background-color: rgb(255, 255, 255); text-align: justify; font-family: Calibri;">
  <br/>
</p>
<p style="margin-top: 0px; margin-bottom: 0px; white-space: normal; box-sizing: border-box; font-size: 14px; background-color: rgb(255, 255, 255); text-align: justify; font-family: Calibri;">
  <span style="box-sizing: border-box; font-family: 微软雅黑; font-size: 16px;">{{ config('app.name') == 'Embracy' ? 'Embracy' : 'Peachypay' }}</span>
</p>
