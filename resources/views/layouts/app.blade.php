<!DOCTYPE html>
<html lang="{{ app()->getLocale() }}">

<head>
  <meta charset="utf-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width, initial-scale=1">

  <!-- CSRF Token -->
  <meta name="csrf-token" content="{{ csrf_token() }}">

  <title>@yield('title', 'title')</title>

  {!! admin_section(Dcat\Admin\Admin::SECTION['HEAD']) !!}
  {!! Dcat\Admin\Admin::asset()->headerJsToHtml() !!}
  {!! Dcat\Admin\Admin::asset()->cssToHtml() !!}

<!-- Styles -->
  <link href="{{ mix('css/app.css') }}" rel="stylesheet">

  @yield('styles')

</head>

<body>
<div id="app" class="page">

  @include('layouts._header')

  <div class="container">

    @yield('content')

  </div>

  @include('layouts._footer')
</div>

{{-- 页面埋点 --}}
{!! admin_section(Dcat\Admin\Admin::SECTION['BODY_INNER_BEFORE']) !!}

<!-- Scripts -->
<script>
  var Dcat = CreateDcat({!! Dcat\Admin\Admin::jsVariables() !!});
</script>
<script src="{{ mix('js/app.js') }}"></script>

@yield('scripts')

{!! Dcat\Admin\Admin::asset()->jsToHtml() !!}
<script>Dcat.boot();</script>

</body>
</html>
