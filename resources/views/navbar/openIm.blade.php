<ul class="nav navbar-nav" id="open-im-icon">
  <li class="dropdown dropdown-notification nav-item">
    <a class="nav-link nav-link-label open-im" href="#" data-toggle="dropdown" aria-expanded="true"><i
        class="fa fa-2x fa-commenting-o" id="notify"></i><span class="badge badge-pill badge-primary badge-up"
                                                               id="message"></span></a>
    <div class="modal" id="imModal" tabindex="-1" role="dialog" aria-hidden="true">
      <div class="modal-dialog modal-lg" style="box-shadow: none;margin: 0;">
        <div class="modal-content" id="modal-content"
             style="border-radius: 8px;box-shadow: 0 0 0 1px rgba(0, 0, 0, .05), 0 2px 3px 0 rgba(0, 0, 0, .1);background-color: #0289fa;"
             title="点击拖动">
          <div class="modal-header"
               style="cursor: move;border-bottom-width: 0;border-top-left-radius: 8px;border-top-right-radius: 8px;"></div>
          <button type="button" class="close" data-dismiss="modal" aria-label="Close" style="top: 0;">
            <span aria-hidden="true">&times;</span>
          </button>
          <div class="modal-body" style="height: 600px">
            <iframe id="imIframe" src="" width="100%" height="100%" frameborder="0" scrolling="no"></iframe>
          </div>
        </div>
      </div>
    </div>
  </li>
</ul>

<script>
  
  $(document).ready(function () {
    const isMobileDevice = () => {
      return navigator.userAgent.match(/Mobi/i) ||
              window.matchMedia('(max-width: 768px)').matches ||
              (typeof navigator.userAgentData !== 'undefined' && navigator.userAgentData.mobile);
    };

    const isMobile = isMobileDevice()
    let openImMobileUrl = "";
    let openImUrl = "";
    let iframe = $('#imIframe');
    const loadOpenUrl = async() => {
       await $.ajax({
          url: '/{{$system}}/open_im/get_login_address',
          type: 'GET',
          success: function (res) {
            if(isMobile){
              openImMobileUrl = `${res?.phone_address}&url=${window.location.href}`
              if (res?.phone_address && isMobile){
                $("#open-im-icon").show()
              }
            }
            else {
              openImUrl = res.address;
              iframe.attr('src', openImUrl);
              if (openImUrl && !isMobile){
                $("#open-im-icon").show()
              }
            }
          }
        });
    }

    loadOpenUrl()
    // 首次加载时，设置 iFrame 的 src 属性
    
    const type = '{{$system}}'
    let openIm = $('.open-im')
    // 当点击 "打开 IM" 链接时
    openIm.on('click', async function (e) {
      e.preventDefault();
      let modalContent = document.querySelector("#imModal .modal-dialog");
      modalContent.style.left = 'calc(50% - 600px + 130px)';
      modalContent.style.top = 'calc(50% - 325px)';
      let notify = $('#notify');
      let message = $('#message');
      notify.removeClass('blink');
      message.removeClass('blink');
      if (isMobile){
        // 打开新页面
        window.open(openImMobileUrl, '_blank');
        return
      }else {
         // 显示模态窗口
        $('#imModal').modal('show');
      }
    });
    // 当点击关闭按钮时
    $('.close').on('click', function (e) {
      e.preventDefault();
      // 隐藏模态窗口
      $('#imModal').modal('hide');
    });
    let modalContent = document.querySelector("#imModal .modal-dialog");
    let modalHeader = document.querySelector("#imModal .modal-dialog .modal-header");
    let dragStartX, dragStartY;
    let isDragging = false;
    let placeholder = null; // 用于表示拖动时的边框
    modalHeader.addEventListener('mousedown', function (e) {
      if (e.button === 0) {
        isDragging = true;
        const rect = modalContent.getBoundingClientRect();
        const computedStyle = window.getComputedStyle(modalContent);
        // 获取 margin 并转为数值（去掉 px 单位）
        const marginLeft = parseFloat(computedStyle.marginLeft) || 0;
        const marginTop = parseFloat(computedStyle.marginTop) || 0;
        dragStartX = e.clientX - rect.left + marginLeft;
        dragStartY = e.clientY - rect.top + marginTop;
        // 创建一个占位符（用边框表示拖动区域）
        placeholder = document.createElement('div');
        placeholder.style.position = 'absolute';
        // 计算相对页面的绝对位置，考虑页面滚动
        placeholder.style.left = rect.left + window.pageXOffset + 'px';
        placeholder.style.top = rect.top + window.pageYOffset + 'px';
        placeholder.style.width = rect.width + 'px';
        placeholder.style.height = rect.height + 'px';
        placeholder.style.border = '2px dashed #000';
        placeholder.style.zIndex = '1000';
        document.body.appendChild(placeholder);
        // 绑定鼠标移动事件
        document.addEventListener('mousemove', onMouseMove);
        e.preventDefault();
      }
    });

    function onMouseMove(e) {
      if (isDragging && placeholder) {
        placeholder.style.left = e.clientX - dragStartX + window.pageXOffset + 'px';
        placeholder.style.top = e.clientY - dragStartY + window.pageYOffset + 'px';
      }
    }

    function stopDragging() {
      if (isDragging && placeholder) {
        modalContent.style.left = placeholder.style.left;
        modalContent.style.top = placeholder.style.top;
        document.body.removeChild(placeholder);
        placeholder = null;
        isDragging = false;
        document.removeEventListener('mousemove', onMouseMove);
      }
    }

    document.addEventListener('mouseup', stopDragging);
    window.addEventListener('mouseleave', stopDragging);
    window.addEventListener('blur', stopDragging);
    window.addEventListener('message', function (e) {
      // 更新按钮上的数字
      let message = $('#message');
      let count = e.data || 0;
      message.text(count);
      if (count > 0) {
        let notify = $('#notify');
        notify.addClass('blink');
        message.addClass('blink')
        message.show();
      } else {
        message.hide();
      }
    }, false);
  });
</script>

<style>
  .modal-backdrop {
    display: none;
  }

  #imIframe {
    border-radius: 6px;
  }

  .modal-lg, .modal-xl {
    max-width: 1200px;
    box-shadow: 0 0 0 1px rgba(0, 0, 0, .05), 0 2px 3px 0 rgba(0, 0, 0, .1);
    border-radius: 6px;
  }

  .modal-body {
    overflow: hidden;
    padding: 0;
    /*background-color: rgba(10, 10, 10, 0.44);*/
  }

  @keyframes blink {
    0% {
      opacity: 1;
    }
    50% {
      opacity: 0.5;
    }
    100% {
      opacity: 1;
    }
  }

  #open-im-icon {
    display: none;
  }

  .blink {
    animation: blink 1s infinite;
  }

  .close {
    position: absolute;
    right: 8px;
    top: 4px;
    color: #fff;
    font-size: 30px;
  }

  #open-im-icon .open-im {
      padding: .8rem;
  }

  #open-im-icon #notify {
    font-size: 2.4em;
  }

  @media screen and (max-width: 768px) {
   
    #selected-language {
      display: none;
    }
    
  }


</style>
