<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <link href="//unpkg.com/layui@2.7.6/dist/css/layui.css" rel="stylesheet">
    <title>授权完成页面</title>
    <style>
      .whole {
        width: 700px;
        margin: 200px auto;
        position: relative;
      }
      .whole-content {
        position: absolute;
        left: 30%;
        margin: auto;
      }
      .whole-content-margin {
        margin-left: 20%;
      }
      .whole-content-top {
        margin-top: 5%;
      }
    </style>
</head>
<body>
  <div class="whole">
    <input type="hidden" name="shop_url" value="{{$shop_url}}">
    <div class="whole-content">
      @if ($result == 'success')
        <img src="/media/shoplazza/auth_success.png" class="whole-content-margin"> 
        <h1 class="whole-content-top">Get Access Token Success!</h1>
      @else
        <img src="/media/shoplazza/auth_failure.png" class="whole-content-margin"> 
        <h1 class="whole-content-top">Get Access Token Failure!</h1>
      @endif
      
      <h3 class="whole-content-margin whole-content-top">5秒后自动跳转上个页面</h3>
    </div>
  </div>


</body>
<script src="//unpkg.com/layui@2.7.6/dist/layui.js"></script>
<script src="https://apps.bdimg.com/libs/jquery/2.1.4/jquery.min.js"></script>
<script>
    layui.use(['layer', 'form'], function (){
        var layer = layui.layer;
        var form = layui.form;
    });
    
    function implement() {
      let shop_url = $('input[name="shop_url"]').val();
      
      if (shop_url != '') {
          setTimeout(function () {
            window.location = shop_url;
          }, '2000'); 
      }
    }

    implement();
</script>
</html>


