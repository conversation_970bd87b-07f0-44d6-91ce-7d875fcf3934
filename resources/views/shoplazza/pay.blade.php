<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <link href="//unpkg.com/layui@2.7.6/dist/css/layui.css" rel="stylesheet">
    <title>Payment</title>
    <style>
        body {
            background-color: #FAFAFA;  
        }
        .whole {
            width: 900px;
            width: 700px;
            margin: 100px auto;
            position: relative;
        }
        .content {
            background-color: #fff;
            width: 620px;
            height: 550px; 
        }
        .content-form {
            margin: auto;
            width: 530px;
            height: 395px;  
        }
        h2 {
            margin-bottom: 20px;
        }
        .content-input {
            border: 1px solid #e9e6e6;
            padding: 30px 14px 14px 30px;
            border-radius: 16px;
        }
        .div-input {
            width: 230px;
            float: left;
            margin-right: 10px;
        }
        .div-input-row {
          padding-right: 14px;
        }
        .content-div-img {
          margin: 10px 0px 50px 35px;
        }
        .content-img {
          height: 36px;
        }
        .content-top {
          padding: 20px 0 10px 45px;
        }
        .content-top-span {
          font-weight: bold;
          font-size: 16px;
          margin-left: 5px;
        }
        .whole-h2 {
          font-weight: bold;
        }
        .content-form-amount {
          margin-top: 20px;
          font-weight: bold;
          font-size: 16px;
        }
        .content-form-amount-span {
          color: #F47029;
        }
        .content-form-button {
          margin-top: 30px;
        }
        .content-form-button-submit {
          background-color:#5257FF;
        }
        .content-form-button-cancel {
          border-color: #5257FF!important;
          color: #5257FF!important;
          margin-left: 20px;
        }
        .content-form-button button {
          width: 120px;
          border-radius:10px;
        }
    </style>
</head>
<body>
    <div class="whole">
        <h2 class="whole-h2">Payment</h2>
        <div class="content">
            <div class="content-top">
              <img src="/media/shoplazza/card_icon.png" alt=""><span class="content-top-span">Credit / Debit Card</span>
            </div>
            <div class="content-form">
                <form class="layui-form">
                    
                    <div class="content-input">
                        <div class="layui-form-item">
                            <div class="div-input">
                              <input type="text" name="first_name" placeholder="Cardholder's First Name" autocomplete="off" class="layui-input">
                            </div>
                            <div class="div-input">
                              <input type="text" name="last_name"  placeholder="Cardholder's Last Name" autocomplete="off" class="layui-input">
                            </div>
                        </div>
                        
                        <div class="layui-form-item">
                          <div class="div-input-row">
                            <input type="text" name="card_number" maxlength="19" oninput="this.value=this.value.replace(/\D/g,'')" placeholder="Card Number" autocomplete="off" class="layui-input">
                          </div>
                        </div>
                        
                        <div class="layui-form-item">
                          <div class="div-input">
                            <input type="text" name="card_date" onkeyup="myFunction(event)" maxlength="16" oninput="this.value=this.value.replace(/[^0-9/]/g,'')" maxlength="5" placeholder="MM/YY" autocomplete="off" class="layui-input">
                          </div>
                          <div class="div-input">
                            <input type="password" name="card_code" maxlength="4" oninput="this.value=this.value.replace(/\D/g,'')" placeholder="CVV/CVV2" autocomplete="off" class="layui-input">
                          </div>
                        </div>
                    </div>
                    
                    <div class="content-div-img">
                      <img src="/media/shoplazza/card.jpg" class="content-img">
                      <img src="/media/shoplazza/card_cvv.jpg" class="content-img">
                    </div>
                    
                    @if (empty($order_number))
                      <hr>
                        <label style="color:red">Order Has Expired, Please Cancel!!!</label>
                      <hr>

                      <div class="layui-form-item content-form-button">
                        <a href="{{$cancel_url}}"> <button type="button" class="layui-btn layui-btn-primary content-form-button-cancel">CANCEL</button></a>
                      </div>
                    @else
                      <hr>
                        <label>Order Number：{{$order_number}}</label>
                        <input type="hidden" name="mark" value="{{$mark}}">
                        <input type="hidden" name="session_id" value="{{$session_id}}">
                      <hr>
                      
                      <div class="content-form-amount">
                          <label>Currency：<span class="content-form-amount-span">{{$currency}}</span></label><br><br>
                          <label>Amount：<span class="content-form-amount-span">{{$amount}}</span></label>
                      </div>

                      <div class="layui-form-item content-form-button">
                        <button type="button" id="post" class="layui-btn content-form-button-submit" lay-submit lay-filter="formDemo">PAY</button>
                        <a href="{{$cancel_url}}"> <button type="button" class="layui-btn layui-btn-primary content-form-button-cancel">CANCEL</button></a>
                      </div>   
                    @endif

                </form>
            </div>
        </div>
    </div>

</body>
<script src="//unpkg.com/layui@2.7.6/dist/layui.js"></script>
<script src="https://apps.bdimg.com/libs/jquery/2.1.4/jquery.min.js"></script>
<script>
    layui.use(['layer', 'form'], function(){
        var layer = layui.layer;
        var form = layui.form;
    });
    
    // 监听到期日输入框
    function myFunction(e){
      let keynum    = window.event ? e.keyCode : e.which;
      let card_date = $('input[name="card_date"]');

      if (/^\d+$/.test(e.key) || keynum == 8) {
        if (card_date.val().length == 2) {
          if (keynum == 8) {
            card_date.val(card_date.val().slice(0,1));
          } else {
            card_date.val(card_date.val() + '/');
          } 
        } 
      }
    }
    
    $('#post').on('click', function(){ 
        let card_number = $('input[name="card_number"]').val();
        let first_name  = $('input[name="first_name"]').val();
        let last_name   = $('input[name="last_name"]').val();
        let card_date   = $('input[name="card_date"]').val();
        let card_code   = $('input[name="card_code"]').val();
        let mark        = $('input[name="mark"]').val();
        let session_id  = $('input[name="session_id"]').val();
        
        if (!card_number || !first_name || !last_name || !card_date || !card_code) {
            layer.msg('Parameter Error!');
            return;
        }
        
        if (card_number.length < 14 || card_number.length > 19) {
            layer.msg('Please Enter 14-19 Digit Card Number!');
            return;
        }
        
        if (!/^(0[1-9]|1[0-2])\/\d{2}$/.test(card_date)) {
            layer.msg('Please Enter The Correct MM/YY!');
            return;
        }
        
        // 切割到期日
        let arr = card_date.split("/");

        //加载
        var index = layer.load();
        
        $.ajax({
            type: "POST",
            url: "/api/v1/shoplazza/pay",
            data: { 
              card_number      : card_number,
              expiration_month : arr[0],
              expiration_year  : arr[1],
              mark             : mark,
              cvv              : card_code,
              session_id       : session_id,
            },
            success: function(msg) {
                // 关闭加载
                layer.close(index);
                if (msg) {
                    if (!msg.redirect_url) {
                        layer.msg('Payment failed，' + msg.message);
                    } else {
                        layer.msg('After payment, you will return to the merchant page');
                        setTimeout(function () {
                          window.location = msg.redirect_url;
                        }, '2000');  
                    }
                } else {
                    layer.msg('Payment failed');
                }
            },
            error: function() {
              // 关闭加载
              layer.close(index);
              layer.msg('Payment failed');
            }
        });
    })
</script>
@if (!empty($fingerprint_device))
  <script>
    (function() {
      function beaconLoad() {
        var store_domain = 'www.novatti.com_embracy';
        var session_id   = '{{$session_id}}';
        var url          = ('https:' == document.location.protocol ? 'https://' : 'http://')
          + "beacon.riskified.com?shop=" + store_domain + "&sid=" + session_id;
        var s       = document.createElement('script');
            s.type  = 'text/javascript';
            s.async = true;
            s.src   = url;
        var x       = document.getElementsByTagName('script')[0];
        x.parentNode.insertBefore(s, x);
      }
      
      if (window.attachEvent)
        window.attachEvent('onload', beaconLoad)
      else
        window.addEventListener('load', beaconLoad, false);
    })();
  </script>
@endif
</html>


