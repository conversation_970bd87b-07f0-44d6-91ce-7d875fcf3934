<style>
    .register-btn {
        width: 80%;
        height: 40px;
        background: #FFFFFF;
        margin: 15px 10% 0px 10% !important;
        border-radius: 25px !important;
        padding-left: 2rem !important;
        padding-right: 1.5rem !important;
    }

    .return-btn {
        height: 40px;
        background: #000000;
        border-radius: 25px;
        padding-left: 2rem !important;
        padding-right: 2rem !important;
        position: fixed;
        bottom: 20px;
        color: #ffffff;
        font-weight: 400;
        margin-left: 16.5em;
        box-shadow: none;
    }

    .register-submit-btn {
        height: 40px;
        background: #000000;
        border-radius: 25px !important;
        padding-left: 2rem !important;
        padding-right: 1.5rem !important;
        position: fixed;
        bottom: 20px;
        color: #ffffff;
        font-weight: 400;
        margin-left: 47.5em;
        box-shadow: none;
    }

    .register-btn-punctual {
        background:linear-gradient(-90deg, #4A5FFF, #4C82FF);
    }

    .return-btn-punctual {
        background: #FFFFFF;
        color: #666666;
        box-shadow: 0px 2px 6px 0px rgba(56,58,81,0.15);
    }

    .register-box {
        width: 1100px;
    }

    .register-card-body::-webkit-scrollbar {
        width: 8px;
    }

    .register-card-body::-webkit-scrollbar-track {
        background-color: #e4e4e4;
        border-radius: 100px;
    }

    .register-card-body::-webkit-scrollbar-thumb {
        background-color: #1eadec;
        border-radius: 100px;
    }

    .template-tip {
        width: 100%;
        margin-top: 0.25rem;
        margin-bottom: 0.25rem;
        font-size: smaller;
        color: #ea5455;
    }
</style>
<link rel="stylesheet" href="{{ admin_asset('vendor/dcat-admin/dcat/plugins/bootstrap-icons/bootstrap-icons.min.css') }}">
<link rel="stylesheet" href="{{ admin_asset('vendor/dcat-admin/dcat/plugins/bootstrap-fileinput/css/fileinput.min.css') }}">
<script src="{{ admin_asset('vendor/dcat-admin/dcat/plugins/bootstrap-fileinput/js/fileinput.min.js')}}"></script>

@php
    $isPunctualPay = false;
    
    if (env('APP_NAME', 'Laravel') == 'PunctualPay') {
        $isPunctualPay = true;
    }
    
@endphp
<div class="register-box" style="display:none;">
    <div class="card">
        <div class="card-body register-card-body shadow-100" style="height: auto !important;{{ $isPunctualPay ? 'max-height: 750px;' : 'max-height: 800px;' }}overflow: auto">
            <div class="login-logo mb-2">
                {{ __('kyc.fields.title') }}
            </div>
            <form class="was-validated" method="POST" id="register-form" action="{{ admin_url('auth/registerKyc') }}">
                <div class="form-group">
                    <label for="type">{{ __('kyc.fields.company_entity') }}</label>
                    <select id="type" class="custom-select is-valid" name="type" required>
                        <option selected value="0">{{ __('kyc.fields.company_type.chinese_mainland') }}</option>
                        <option value="1">{{ __('kyc.fields.company_type.chinese_hongkong') }}</option>
                        <option value="2">{{ __('kyc.fields.company_type.overseas') }}</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="business_type">{{ __('kyc.fields.business_type') }}</label>
                    <select id="business_type" class="custom-select is-valid" name="business_type" required>
                        <option selected value="0">{{ __('kyc.fields.business_select.order') }}</option>
                        <option value="1">{{ __('kyc.fields.business_select.virtual') }}</option>
                    </select>
                </div>
                <div class="form-row">
                    <div class="form-group col-md-6">
                        <label for="company_name">{{ __('kyc.fields.company_name') }}</label>
                        <input type="text" class="form-control" name="company_name" id="company_name" placeholder="{{ __('kyc.fields.company_name') }}" required>
                        <!-- <div class="invalid-feedback">Company name must be filled in</div> -->
                    </div>
                    <div class="form-group col-md-6">
                        <label for="address">{{ __('kyc.fields.registered_address') }}</label>
                        <input type="text" class="form-control" name="address" id="address" placeholder="{{ __('kyc.fields.registered_address') }}" required>
                    </div>
                </div>
                <div class="form-row">
                    <div class="form-group col-md-6">
                        <label for="office_phone">{{ __('kyc.fields.office_phone') }}</label>
                        <input type="text" class="form-control" name="office_phone" id="office_phone" placeholder="{{ __('kyc.fields.office_phone') }}" required>
                    </div>
                    <div class="form-group col-md-6">
                        <label for="office_address">{{ __('kyc.fields.office_address') }}</label>
                        <input type="text" class="form-control" name="office_address" id="office_address" placeholder="{{ __('kyc.fields.office_address') }}" required>
                    </div>
                </div>
                <div class="form-group">
                    <label for="certificate_no" id="certificate_no">{{ __('kyc.fields.mainland_type_info.certificate_no') }}</label>
                    <input type="text" class="form-control" name="certificate_no" required>
                </div>
                <div class="form-group">
                    <label id="certificate_img">{{ __('kyc.fields.mainland_type_info.certificate_img') }}</label>
                    <div class="file-loading">
                        <input class="fileUpload" type="file" is-verification="true" data-name="certificate_img" multiple>
                    </div>
                    <div class="template-tip">{{ __('kyc.fields.tip.img_restrictions') }}</div>
                </div>
                <div class="form-group" id="register_img" hidden="hidden">
                    <label>{{ __('kyc.fields.register_img') }}</label>
                    <div class="file-loading">
                        <input class="fileUpload" type="file" data-name="register_img" multiple>
                    </div>
                    <div class="template-tip">{{ __('kyc.fields.tip.img_restrictions') }}</div>
                </div>
                <div class="form-row">
                    <div class="form-group col-md-3">
                        <label for="enforcer_name" id="enforcer_name">{{ __('kyc.fields.mainland_type_info.enforcer_name') }}</label>
                        <input type="text" class="form-control" name="enforcer_name" placeholder="{{ __('kyc.fields.mainland_type_info.enforcer_name') }}" required>
                    </div>
                    <div class="form-group col-md-9">
                        <label for="enforcer_code" id="enforcer_code">{{ __('kyc.fields.mainland_type_info.enforcer_code') }}</label>
                        <div class="file-loading">
                            <input class="fileUpload" type="file" is-verification="true" data-name="enforcer_code" multiple>
                        </div>
                        <div class="template-tip">{{ __('kyc.fields.tip.img_restrictions') }}</div>
                    </div>
                </div>
                <div class="form-group">
                    <label for="enforcer_hold_code" id="enforcer_hold_code">{{ __('kyc.fields.mainland_type_info.enforcer_hold_code') }}</label>
                    <div class="file-loading">
                        <input class="fileUpload" type="file" is-verification="true" data-name="enforcer_hold_code" multiple>
                    </div>
                    <div class="template-tip">{{ __('kyc.fields.tip.img_restrictions') }}</div>
                </div>

                <div class="form-group">
                    <label id="shareholder_info">{{ __('kyc.fields.mainland_shareholder_info.info') }}</label>
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th scope="col" style="width: 15%;text-transform:none">{{ __('kyc.fields.mainland_shareholder_info.shareholder_name') }}</th>
                                <th scope="col" style="text-transform:none" id="shareholder_code">{{ __('kyc.fields.mainland_shareholder_info.shareholder_code') }}</th>
                                <th scope="col">{{ __('kyc.fields.operation') }}</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td><input type="text" class="form-control" name="shareholder_info[0][shareholder_name]" placeholder="{{ __('kyc.fields.mainland_shareholder_info.shareholder_name') }}" required></td>
                                <td>
                                    <div class="file-loading">
                                        <input class="fileUpload" type="file" is-verification="true" data-name="shareholder_info[0][shareholder_code]" multiple>
                                    </div>
                                    <div class="template-tip">{{ __('kyc.fields.tip.img_restrictions') }}</div>
                                </td>
                                <td>
                                </td>
                            </tr>
                            <tr hidden="hidden">
                                <td><input type="text" class="form-control" name="shareholder_info[1][shareholder_name]" placeholder="Name of shareholder"></td>
                                <td>
                                    <div class="file-loading">
                                        <input class="fileUpload" type="file" data-name="shareholder_info[1][shareholder_code]" multiple>
                                    </div>
                                    <div class="template-tip">{{ __('kyc.fields.tip.img_restrictions') }}</div>
                                </td>
                                <td>
                                    <input class="btn tr-hidden" type="button" value="{{ __('kyc.fields.delete') }}">
                                </td>
                            </tr>
                            <tr hidden="hidden">
                                <td><input type="text" class="form-control" name="shareholder_info[2][shareholder_name]" placeholder="Name of shareholder"></td>
                                <td>
                                    <div class="file-loading">
                                        <input class="fileUpload" type="file" data-name="shareholder_info[2][shareholder_code]" multiple>
                                    </div>
                                    <div class="template-tip">{{ __('kyc.fields.tip.img_restrictions') }}</div>
                                </td>
                                <td>
                                    <input class="btn tr-hidden" type="button" value="{{ __('kyc.fields.delete') }}">
                                </td>
                            </tr>
                            <tr hidden="hidden">
                                <td><input type="text" class="form-control" name="shareholder_info[3][shareholder_name]" placeholder="Name of shareholder"></td>
                                <td>
                                    <div class="file-loading">
                                        <input class="fileUpload" type="file" data-name="shareholder_info[4][shareholder_code]" multiple>
                                    </div>
                                    <div class="template-tip">{{ __('kyc.fields.tip.img_restrictions') }}</div>
                                </td>
                                <td>
                                    <input class="btn tr-hidden" type="button" value="{{ __('kyc.fields.delete') }}">
                                </td>
                            </tr>
                            <tr>
                                <td><input type="button" class="btn" role="button" value="{{ __('kyc.fields.add_to') }}" id="table-insert"></td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                <div class="form-group">
                    <label>{{ __('kyc.fields.equity_certificate_img') }}</label>
                    <div class="file-loading">
                        <input class="fileUpload" type="file" is-verification="true" data-name="equity_certificate_img" multiple>
                    </div>
                    <div class="template-tip">{{ __('kyc.fields.tip.img_restrictions') }}</div>
                </div>
                <div class="form-group">
                    <label>{{ __('kyc.fields.bank_account_img') }}</label>
                    <div class="file-loading">
                        <input class="fileUpload" type="file" is-verification="true" data-name="bank_account_img" multiple>
                    </div>
                    <div class="template-tip">{{ __('kyc.fields.tip.img_restrictions') }}</div>
                </div>
                <div class="form-group">
                    <label>{{ __('kyc.fields.merchant_apply_file') }}</label>
                    <div class="file-loading">
                        <input class="xlsxUpload" type="file" data-name="merchant_apply_file" multiple>
                    </div>
                    <div class="template-tip">{{ __('kyc.fields.tip.merchant_apply_file') }} {{ __('kyc.fields.tip.file_restrictions') }}</div>
                    <input type="button" class="btn" role="button" value="{{ __('kyc.fields.tip.template_one') }}" id="web-template-download">
                    <input type="button" class="btn" role="button" value="{{ __('kyc.fields.tip.template_two') }}" id="merchant-template-download">
                </div>

                <div class="form-row">
                    <div class="form-group col-md-5">
                        <label for="contacts_name">{{ __('kyc.fields.contacts_name') }}</label>
                        <input type="text" class="form-control" name="contacts_name" id="contacts_name" placeholder="{{ __('kyc.fields.contacts_name') }}" required>
                    </div>
                    <div class="form-group col-md-5">
                        <label for="contacts_phone">{{ __('kyc.fields.contacts_phone') }}</label>
                        <input type="text" class="form-control" name="contacts_phone" placeholder="{{ __('kyc.fields.contacts_phone') }}" required>
                    </div>
                    <div class="form-group col-md-2">
                        <label for="contacts_position">{{ __('kyc.fields.contacts_position') }}</label>
                        <input type="text" class="form-control" name="contacts_position" placeholder="{{ __('kyc.fields.contacts_position') }}" required>
                    </div>
                </div>
                <div class="form-group">
                    <label for="contacts_email" id="contacts_email">{{ __('kyc.fields.contacts_email') }}</label>
                    <input type="email" class="form-control" name="contacts_email" placeholder="<EMAIL>" required>
                </div>
                <div class="form-group">
                    <a href="javascript:;" id="random_password">生成随机密码：(生成后，请注意复制下方密码保存)</a>
                </div>
                <div class="form-group">
                    <label for="show_random_password" id="show_random_password"></label>
                </div>
                <div class="form-group">
                    <label for="contacts_email">{{ __('kyc.fields.password') }}</label>
                    <input type="password" class="form-control reg-password" name="password" id="password" minlength='8' required>
                    <div class="invalid-feedback invalid-feedback-password">{{ __('kyc.fields.tip.password_min') }}</div>
                </div>
                <div class="form-group">
                    <label for="contacts_email">{{ __('kyc.fields.confirm_password') }}</label>
                    <input type="password" class="form-control reg-confirm" name="confirm" required>
                    <div class="invalid-feedback invalid-feedback-confirm">{{ __('kyc.fields.tip.password_error') }}</div>
                </div>
                <button type="submit" class="btn float-right register-submit-btn {{$isPunctualPay ? 'register-btn-punctual' : ''}}">
                    {{ __('kyc.fields.submit') }}
                    &nbsp;
                    @if (!$isPunctualPay)
                        <i class="feather icon-arrow-right"></i>
                    @endif
                    
                </button>
            </form>
            <button target="login" class="btn float-right return-btn {{$isPunctualPay ? 'return-btn-punctual' : ''}}">
                @if (!$isPunctualPay)
                    <i class="feather icon-arrow-left"></i>
                @endif
                &nbsp;
                {{ __('kyc.fields.back_to_login') }}
            </button>
        </div>
    </div>
</div>

<script>
    var fileUploadOption = {
        uploadUrl: "uploadImgKyc",
        allowedFileExtensions: ['jpg', 'jpeg', 'png', 'pdf'],
        dropZoneEnabled: false,
        maxFileCount: 4,
        minFileCount: 1,
        maxFileSize: 10240,
        autoReplace: false,
        overwriteInitial: true,
        showBrowse: true,
        // initialPreview: initialPreview(),
        // initialPreviewConfig: initialPreviewConfig(),
        layoutTemplates: {
            actionUpload: '', //去除上传预览缩略图中的上传图片
            //actionZoom:'',   //去除上传预览缩略图中的查看详情预览的缩略图标
            //actionDownload:'' //去除上传预览缩略图中的下载图标
            actionDelete: '', //去除上传预览的缩略图中的删除图标
        },
        // showUploadedThumbs: false,
        // showUpload: false,
        previewFileType: ['image'],
        msgFilesTooMany: "选择上传的文件数量({n}) 超过允许的最大数值{m}！",
    }
    var xlsxUploadOption = {
        uploadUrl: "uploadXlsxKyc",
        allowedFileExtensions: ['xlsx'],
        dropZoneEnabled: false,
        maxFileCount: 2,
        minFileCount: 2,
        maxFileSize: 102400,
        autoReplace: false,
        overwriteInitial: true,
        showBrowse: true,
        layoutTemplates: {
            actionUpload: '', //去除上传预览缩略图中的上传图片
            actionDelete: '', //去除上传预览的缩略图中的删除图标
        },
        previewFileType: ['xsl'],
        msgFilesTooMany: "选择上传的文件数量({n}) 超过允许的最大数值{m}！",
    }

    var mainlandTypeInfo = {
        certificate_no: "{{ __('kyc.fields.mainland_type_info.certificate_no') }}",
        certificate_img: "{{ __('kyc.fields.mainland_type_info.certificate_img') }}",
        enforcer_name: "{{ __('kyc.fields.mainland_type_info.enforcer_name') }}",
        enforcer_code: "{{ __('kyc.fields.mainland_type_info.enforcer_code') }}",
        enforcer_hold_code: "{{ __('kyc.fields.mainland_type_info.enforcer_hold_code') }}",
        shareholder_code: "{{ __('kyc.fields.mainland_shareholder_info.shareholder_code') }}",
        contacts_email: "{{ __('kyc.fields.mainland_type_info.contacts_email') }}",
        shareholder_info: "{{ __('kyc.fields.mainland_shareholder_info.info') }}",
    }

    var hongkongTypeInfo = {
        certificate_no: "{{ __('kyc.fields.hongkong_type_info.certificate_no') }}",
        certificate_img: "{{ __('kyc.fields.hongkong_type_info.certificate_img') }}",
        enforcer_name: "{{ __('kyc.fields.hongkong_type_info.enforcer_name') }}",
        enforcer_code: "{{ __('kyc.fields.hongkong_type_info.enforcer_code') }}",
        enforcer_hold_code: "{{ __('kyc.fields.hongkong_type_info.enforcer_hold_code') }}",
        shareholder_code: "{{ __('kyc.fields.hongkong_shareholder_info.shareholder_code') }}",
        contacts_email: "{{ __('kyc.fields.hongkong_type_info.contacts_email') }}",
        shareholder_info: "{{ __('kyc.fields.hongkong_shareholder_info.info') }}",
    }

    var overseasTypeInfo = {
        certificate_no: "{{ __('kyc.fields.overseas_type_info.certificate_no') }}",
        certificate_img: "{{ __('kyc.fields.overseas_type_info.certificate_img') }}",
        enforcer_name: "{{ __('kyc.fields.overseas_type_info.enforcer_name') }}",
        enforcer_code: "{{ __('kyc.fields.overseas_type_info.enforcer_code') }}",
        enforcer_hold_code: "{{ __('kyc.fields.overseas_type_info.enforcer_hold_code') }}",
        shareholder_code: "{{ __('kyc.fields.overseas_shareholder_info.shareholder_code') }}",
        contacts_email: "{{ __('kyc.fields.overseas_type_info.contacts_email') }}",
        shareholder_info: "{{ __('kyc.fields.overseas_shareholder_info.info') }}",
    }

    var choiceType = [mainlandTypeInfo, hongkongTypeInfo, overseasTypeInfo];

    var dataNameMap = {
        bank_account_img: "{{ __('kyc.fields.bank_account_img') }}",
        certificate_img: "{{ __('kyc.fields.mainland_type_info.certificate_img') }}",
        enforcer_code: "{{ __('kyc.fields.mainland_type_info.enforcer_code') }}",
        enforcer_hold_code: "{{ __('kyc.fields.mainland_type_info.enforcer_hold_code') }}",
        shareholder_code: "{{ __('kyc.fields.mainland_shareholder_info.shareholder_code') }}",
        register_img: "{{ __('kyc.fields.register_img') }}",
        equity_certificate_img: "{{ __('kyc.fields.equity_certificate_img') }}",
    }

    var passwordRegex = /^(?![a-zA-Z]+$)(?!\d+$)(?!\W_+$)[a-zA-Z\d\W_]{8,14}$/u;

    Dcat.ready(function() {
        $("#web-template-download").click(function() {
            window.open("/download/Merchant Application & Agreement.xlsx");
        });
        $("#merchant-template-download").click(function() {
            window.open("/download/商戶入網申請表.xlsx");
        });

        $('#random_password').on('click', () => {
            $.ajax({
                url: '/get_random_password',
                type: 'GET',
                success: function(res) {
                    $('.help-block').parent().find('.box-body').text(res);
                    $('#show_random_password').text(res);
                    $('input[name=password]').val(res);
                    $('input[name=confirm]').val(res);
                }
            });
        });

        $("#type").change((event) => {
            let type = $(event.target).val();

            if (choiceType[type]) {
                switch (type) {
                    case '0':
                    case '2':
                        $("#register_img").attr("hidden", "hidden");
                        $("input[data-name='register_img']:first").attr('is-verification', false);
                        break;
                    case '1':
                        $("#register_img").removeAttr("hidden");
                        $("input[data-name='register_img']:first").attr('is-verification', true);
                        break;
                }
                dataNameMap['certificate_img'] = choiceType[type]['certificate_img'];
                dataNameMap['enforcer_code'] = choiceType[type]['enforcer_code'];
                dataNameMap['enforcer_hold_code'] = choiceType[type]['enforcer_hold_code'];
                dataNameMap['shareholder_code'] = choiceType[type]['shareholder_code'];
                $("#certificate_no").html(choiceType[type]['certificate_no']);
                $("#certificate_img").html(choiceType[type]['certificate_img']);
                $("#enforcer_name").html(choiceType[type]['enforcer_name']);
                $("#enforcer_code").html(choiceType[type]['enforcer_code']);
                $("#enforcer_hold_code").html(choiceType[type]['enforcer_hold_code']);
                $("#shareholder_code").html(choiceType[type]['shareholder_code']);
                $("#contacts_email").html(choiceType[type]['contacts_email']);
                $("#shareholder_info").html(choiceType[type]['shareholder_info']);
                //输入内容提示
                $('input[name="certificate_no"]').attr('placeholder', choiceType[type]['certificate_no']);
                $('input[name="certificate_img"]').attr('placeholder', choiceType[type]['certificate_img']);
                $('input[name="enforcer_name"]').attr('placeholder', choiceType[type]['enforcer_name']);
                $('input[name="enforcer_code"]').attr('placeholder', choiceType[type]['enforcer_code']);
                $('input[name="enforcer_hold_code"]').attr('placeholder', choiceType[type]['enforcer_hold_code']);
                $('input[name="shareholder_code"]').attr('placeholder', choiceType[type]['shareholder_code']);
                $('input[name="contacts_email"]').attr('placeholder', choiceType[type]['contacts_email']);
                $('input[name="shareholder_info"]').attr('placeholder', choiceType[type]['shareholder_info']);
            }
        });

        $("#table-insert").click(() => {
            let tr = $("tr[hidden='hidden']:first");
            if (tr[0]) {
                $("tr[hidden='hidden']:first").removeAttr("hidden");
            } else {
                alert("{{ __('kyc.fields.tip.shareholders_max') }}");
            }
        });

        $(".tr-hidden").click((event) => {
            $(event.target).parent().parent().attr("hidden", "hidden")
        });

        $('.reg-password').on('keyup', () => {

            let password = $('.reg-password').val();
            let confirm_password = $('.reg-confirm').val();

            //校验密码强度
            if (false == passwordRegex.test(password)) {
                $('.invalid-feedback-password').css('display', 'block')
                return false;
            } else {
                $('.invalid-feedback-password').css('display', 'none')
            }

            //验证密码一致性
                if (password != confirm_password) {
                $('.invalid-feedback-confirm').css('display', 'block')
                return false;
            } else {
                $('.invalid-feedback-confirm').css('display', 'none')
            }
        });

        $('.reg-confirm').on('keyup', () => {

                let password = $('.reg-password').val();
                let confirm_password = $('.reg-confirm').val();

                //验证密码一致性
                    if (password != confirm_password) {
                    $('.invalid-feedback-confirm').css('display', 'block')
                    return false;
                } else {
                    $('.invalid-feedback-confirm').css('display', 'none')
                }
        });

        $('#register-form').form({
            validate: true,
            before: (fields, form, opt) => {
                //验证图片内容
                var isAdopt  = validateImg();
                var password = '';
                var confirm_password = '';

                if (!isAdopt) {
                    return isAdopt;
                }
                //验证xlsx
                let size = $('.merchant_apply_file');
                if (size.length == 0) {
                    alert("{{ __('kyc.fields.merchant_apply_file') }}" + "{{ __('kyc.fields.xlsxTip') }}");
                    return false;
                }
                $.each(fields, (index, e) => {
                    if (e.name.indexOf("shareholder") >= 0) {
                        if (e.required && e.value == '') {
                            alert("{{ __('kyc.fields.tip.shareholder_name_empty') }}");
                            isAdopt = false;
                            return false;
                        }
                    }

                    if (e.name.indexOf("password") >= 0) {
                        password = e.value;
                    }

                    if (e.name.indexOf("confirm") >= 0) {
                        confirm_password = e.value;
                    }
                })

                //校验密码强度
                if (false == passwordRegex.test(password)) {
                    alert("{{ __('kyc.fields.tip.password_min') }}");
                    return false;
                }

                //验证密码一致性
                if (password != confirm_password) {
                    alert("{{ __('kyc.fields.tip.password_error') }}");
                    return false;
                }

                return isAdopt;
            },
            success: (data) => {
                if (!data.status) {
                    Dcat.error(data.message);
                    return false;
                }

                Dcat.success("{{ __('kyc.fields.tip.success') }}");
                setTimeout(function() {
                    Dcat.reload();
                }, 3000);

                return false;
            }
        });

        function validateImg() {
            let fileUpload = $('.fileUpload');
            let isSubmit = true;
            //上传图片验证
            $.each(fileUpload, (index, e) => {
                let dataName = $(e).attr('data-name');
                let isValidate = $(e).attr('is-verification');

                if (dataName.indexOf("shareholder") >= 0) {
                    dataName = 'shareholder_code';
                }

                if (isValidate == 'true') {
                    let size = $('.' + dataName);

                    if (size.length == 0) {
                        alert(dataNameMap[dataName] + "{{ __('kyc.fields.imgTip') }}");
                        isSubmit = false;
                        return false;
                    }
                }
            })

            return isSubmit;
        }

        $("button[target='register']").click(() => {
            $(".register-box").show(500);
            $(".login-box").hide(500);
        });

        $("button[target='login']").click(() => {
            $(".register-box").hide(500);
            $(".login-box").show(500);
        });

        $(".fileUpload").fileinput(fileUploadOption).on("fileuploaded", function(event, data, previewId, index) {
            // 文件上传成功
            let result = data.response;

            if (result.status == true) {
                let dataName = $(event.target).attr("data-name");
                if (dataName.indexOf("shareholder") >= 0) {
                    $(event.target).parent().prepend('<input type="hidden" class="shareholder_code" name="' + dataName + '[]" value ="' + result.img + '" />');
                } else {
                    $(event.target).parent().prepend('<input type="hidden" class="' + dataName + '" name="' + dataName + '[]" value ="' + result.img + '" />');
                }
            } else {
                $.modal.alertError(result.message);
            }
        }).on("filecleared", function(event, data, msg) {
            let dataName = $(event.target).attr("data-name");
            if (dataName) {
                $("[name='" + dataName + "[]']").remove();
            }
        });

        $(".xlsxUpload").fileinput(xlsxUploadOption).on("fileuploaded", function(event, data, previewId, index) {
            // 文件上传成功
            let result = data.response;

            if (result.status == true) {
                let dataName = $(event.target).attr("data-name");
                $(event.target).parent().prepend('<input type="hidden" class="' + dataName + '" name="' + dataName + '[]" value ="' + result.xlsx + '" />');
            } else {
                $.modal.alertError(result.message);
            }
        }).on("filecleared", function(event, data, msg) {
            let dataName = $(event.target).attr("data-name");
            if (dataName) {
                $("[name='" + dataName + "[]']").remove();
            }
        });

    });
</script>