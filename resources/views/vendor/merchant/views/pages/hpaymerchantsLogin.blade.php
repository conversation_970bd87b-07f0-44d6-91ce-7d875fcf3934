<style>
    .login-box {
        padding: 5px;
    }

    .login-card-body {
        padding: 1.5rem 1.8rem 1.6rem;
    }

    .card,
    .card-body {
        border-radius: 1.25rem
    }

    .login-btn {
        padding-left: 2rem !important;
        padding-right: 1.5rem !important;
    }

    .content {
        overflow-x: hidden;
    }

    .form-group .control-label {
        text-align: left;
    }

    .bg-40 {
        background: url(/media/bg/bg-12.png) no-repeat center center !important;
        z-index: 1;
    }

    .mb-2 {
        font-size: 28px;
        font-family: Source Han Sans CN;
        font-weight: 500;
        color: #000000;
    }

    .text-right {
        float: right;
    }

    .sign-btn {
        width: 80%;
        height: 40px;
        background: #FFFFFF;
        margin: 0px 10% !important;
        border-radius: 25px !important;
    }

    .login-vector {
        position: fixed;
        top: 0;
        left: 0;
        z-index: 2;
    }

    .login-vector img {
        width: 40%;
        margin: 10%;
    }

    .btn.btn-primary.btn-file {
        border-color: #414750;
    }

    .nav-language {
        position: fixed;
        top: 1.5rem;
        left: 90%;
        z-index: 3;
    }

    .nav-link {
        color: #000000;
    }

    @media screen and (max-width: 768px) {
        .nav-language,[target=register] {
            display: none;
        }
    }
</style>
<div class="login-vector">
    <img src="/media/logos/hpaymerchants-logo-vector.png">
</div>

<div class="nav-language">
    <li class="dropdown dropdown-language nav-item">
        <a class="dropdown-toggle nav-link" href="#" id="dropdown-flag" data-toggle="dropdown">
            <i class="flag-icon flag-icon-us"></i>
            <span class="selected-language">
                @switch(config('app.locale'))
                @case('zh_CN')
                简体中文
                @break
                @case('en')
                English
                @break

                @endswitch
            </span>
        </a>
        <ul class="dropdown-menu nav-menu" aria-labelledby="dropdown-flag" style="height:100px !important;">
            <li class="dropdown-item" href="#" data-language="zh_CN">
                <a><i class="flag-icon flag-icon-fr"></i> 简体中文</a>
            </li>
            <li class="dropdown-item" href="#" data-language="en">
                <a><i class="flag-icon flag-icon-us"></i> English</a>
            </li>
        </ul>
    </li>
</div>
<div class="login-page bg-40">
    <div class="login-box">
        <div class="card">
            <div class="card-body login-card-body shadow-100">
                <div class="login-logo mb-2">
                    {{ trans('admin.merchant_title') }}
                </div>
                <p class="login-box-msg mt-1 mb-1">{{ __('admin.welcome_back') }}</p>

                <form id="login-form" method="POST" action="{{ admin_url('auth/login') }}">

                    <input type="hidden" name="_token" value="{{ csrf_token() }}" />

                    <fieldset class="form-label-group form-group position-relative has-icon-left">
                        <input type="text" class="form-control {{ $errors->has('merchant_id') ? 'is-invalid' : '' }}" name="merchant_id" placeholder="{{ trans('admin.merchant_id') }}" value="{{ old('merchant_id') }}" required autofocus>

                        <div class="form-control-position">
                            <i class="feather icon-hash"></i>
                        </div>

                        <label for="merchant_id">{{ trans('admin.merchant_id') }}</label>

                        <div class="help-block with-errors"></div>
                        @if($errors->has('merchant_id'))
                        <span class="invalid-feedback text-danger" role="alert">
                            @foreach($errors->get('merchant_id') as $message)
                            <span class="control-label" for="inputError"><i class="feather icon-x-circle"></i> {{$message}}</span><br>
                            @endforeach
                        </span>
                        @endif
                    </fieldset>

                    <fieldset class="form-label-group form-group position-relative has-icon-left">
                        <input type="text" class="form-control {{ $errors->has('name') ? 'is-invalid' : '' }}" name="name" placeholder="{{ trans('admin.username') }}" value="{{ old('name') }}" required autofocus>

                        <div class="form-control-position">
                            <i class="feather icon-user"></i>
                        </div>

                        <label for="email">{{ trans('admin.username') }}</label>

                        <div class="help-block with-errors"></div>
                        @if($errors->has('username'))
                        <span class="invalid-feedback text-danger" role="alert">
                            @foreach($errors->get('username') as $message)
                            <span class="control-label" for="inputError"><i class="feather icon-x-circle"></i> {{$message}}</span><br>
                            @endforeach
                        </span>
                        @endif
                    </fieldset>

                    <fieldset class="form-label-group form-group position-relative has-icon-left">
                        <input minlength="5" maxlength="20" id="password" type="password" class="form-control {{ $errors->has('password') ? 'is-invalid' : '' }}" name="password" placeholder="{{ trans('admin.password') }}" required autocomplete="current-password">

                        <div class="form-control-position">
                            <i class="feather icon-lock"></i>
                        </div>
                        <label for="password">{{ trans('admin.password') }}</label>

                        <div class="help-block with-errors"></div>
                        @if($errors->has('password'))
                        <span class="invalid-feedback text-danger" role="alert">
                            @foreach($errors->get('password') as $message)
                            <span class="control-label" for="inputError"><i class="feather icon-x-circle"></i> {{$message}}</span><br>
                            @endforeach
                        </span>
                        @endif

                    </fieldset>
                    <div class="form-group d-flex justify-content-between align-items-center">
                        <div class="text-right">
                            <fieldset class="checkbox">
                                <div class="vs-checkbox-con vs-checkbox-primary">
                                    <input id="remember" name="remember" value="1" type="checkbox" {{ old('remember') ? 'checked' : '' }}>
                                    <span class="vs-checkbox">
                                        <span class="vs-checkbox--check">
                                            <i class="vs-icon feather icon-check"></i>
                                        </span>
                                    </span>
                                    <span> {{ trans('admin.remember_me') }}</span>
                                </div>
                            </fieldset>
                        </div>
                    </div>
                    <button type="submit" class="btn btn-primary float-right login-btn sign-btn">

                        {{ __('admin.login') }}
                        &nbsp;
                        <i class="feather icon-arrow-right"></i>
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
    Dcat.ready(function() {
        $('.dropdown-item').on('click', function() {
            let lang = $(this).attr('data-language')
            let token = $("#_token").val();
            $.ajax({
                url: '{{ url('merchant/setLang') }}/' + lang,
                type: 'post',
                data: {
                    '_token': token
                },
                success: function(data) {
                    if (data.code != 0) {
                        alert(data.msg)
                        return false;
                    } else {
                        window.location.reload();
                    }
                }
            });
        });
        // ajax表单提交
        $('#login-form').form({
            validate: true,
            success: function(data) {
                if (!data.status) {
                    Dcat.error(data.data.message);

                    return false;
                }

                Dcat.success(data.data.message);

                location.href = data.data.then.value;

                return false;
            }
        });
    });
</script>