<?php

use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| is assigned the "api" middleware group. Enjoy building your API!
|
*/

Route::prefix('v1')->namespace('Api')->name('api.v1.')->middleware(['throttle:' . config('api.rate_limits.access'), 'change-locale'])->group(function () {
    Route::any('orders/{mark}/syncNotify', 'OrderController@syncNotify')->name('syncNotify');
    Route::any('orders/{mark}/forwardSyncNotify', 'OrderController@forwardSyncNotify')->name('forwardSyncNotify');
    Route::any('orders/{mark}/asyncNotify', 'OrderController@asyncNotify')->name('asyncNotify');
    Route::any('orders/{mark}/notify', 'OrderController@notify')->name('notify');
    Route::any('orders/{mark}/verification', 'OrderController@verification')->name('verification');
    Route::any('orders/{mark}/paramsGet', 'OrderController@paramsGet')->name('paramsGet');
    Route::get('orders/{order_id}', 'OrderController@index');
    Route::get('orders/refundQuery/{refund_id}', 'OrderController@refundQuery');

    Route::any('virtual/{mark}/cardNotify', 'VirtualController@cardNotify');
    Route::any('virtual/{mark}/cardTradeNotify', 'VirtualController@cardTradeNotify');
    Route::any('virtual/{mark}/cardOperateNotify', 'VirtualController@cardOperateNotify');
    Route::any('virtual/{mark}/cardTransferNotify', 'VirtualController@cardTransferNotify');

    // 刷单
    Route::any('orders/swipeResult', 'OrderController@swipeResult')->name('swipeResult');
});

Route::prefix('v1')->namespace('Api')->name('api.v1.')->group(function () {
    Route::middleware(['auth:api'])->group(function () {

        // 订单
        Route::middleware(['change-locale'])->group(function () {
            Route::resource('orders', 'OrderController');
            Route::post('orders/refund', 'OrderController@refund')->name('refund');
            Route::post('orders/paymentCheckout', 'OrderController@paymentCheckout')->name('paymentCheckout');
        });

        // 收银台下单
        Route::any('cashier/orders', 'CashierController@orders');
    });

    // 收银台支付
    Route::post('cashier/pay', 'CashierController@pay');
});

Route::prefix('v1')->namespace('Api')->name('track.')->group(function () {
    Route::middleware('orderTrack')->group(function () {
        Route::post('orderTracks/create', 'OrderTrackController@addOrderTracks')->name('create');
        Route::put('orderTracks/{order_id}/change', 'OrderTrackController@updateOrderTrack')->name('change');
        Route::post('orderTracks/batchChange', 'OrderTrackController@updateOrderTracks')->name('batchChange');
        Route::get('orderTracks/{order_id}/query', 'OrderTrackController@getOrderTrack')->name('query');
        Route::post('orderTracks/batchQuery', 'OrderTrackController@getOrderTracks')->name('batchQuery');
    });

    Route::any('orderTracks/token', 'OrderTrackController@token')->name('token');
});

Route::prefix('v1')->namespace('Api')->name('api.v1.')->middleware(['throttle:' . config('api.rate_limits.access')])->group(function () {
    // 店匠
    Route::any('shoplazza/home', 'ShoplazzaController@home')->name('home');
    Route::any('shoplazza/redirect', 'ShoplazzaController@redirect')->name('redirect');
    Route::any('shoplazza/cashier', 'ShoplazzaController@cashier')->name('cashier');
    Route::any('shoplazza/pay/{mark?}', 'ShoplazzaController@pay')->name('pay');
    Route::any('shoplazza/{mark}/asyncNotify', 'ShoplazzaController@asyncNotify')->name('shopAsyncNotify');
    Route::any('shoplazza/{mark}/syncNotify', 'ShoplazzaController@syncNotify')->name('shopSyncNotify');
});

Route::prefix('v1')->namespace('Api')->name('api.v1.')->middleware(['throttle:' . config('api.rate_limits.access')])->group(function () {
    Route::any('localOrders/{mark}/syncNotify', 'LocalOrderController@syncNotify')->name('localSyncNotify');
    Route::any('localOrders/{mark}/asyncNotify', 'LocalOrderController@asyncNotify')->name('localAsyncNotify');
    Route::any('localOrders/{mark}/notify', 'LocalOrderController@notify')->name('localNotify');
    Route::any('localOrders/{mark}/verification', 'LocalOrderController@verification')->name('localVerification');
    Route::any('localOrders/swipeResult', 'LocalOrderController@swipeResult');
    Route::get('localOrders/{order_id}', 'LocalOrderController@index');
    Route::get('localOrders/refundQuery/{refund_id}', 'LocalOrderController@refundQuery');
});

Route::prefix('v1')->namespace('Api')->name('api.v1.')->group(function () {
    Route::middleware(['auth:api'])->group(function () {
        // 本地支付下单
        Route::resource('localOrders', 'LocalOrderController');
        Route::post('localOrders/refund', 'LocalOrderController@refund')->name('localRefund');
    });
});

Route::prefix('v1')->namespace('Api')->name('api.v1.')->middleware(['throttle:' . config('api.rate_limits.access')])->group(function () {
    //CDRN通知地址
    Route::any('cdrn/notice', 'CdrnController@notice');
    //M卡拒付预警Wintranx渠道通知地址
    Route::any('wintranx/notice', 'WintranxController@notice');
    //riskshieId预警系统通知地址
    Route::any('embracyShield/notice', 'EmbracyShieldController@notice');
});

Route::prefix('v1')->namespace('Api')->name('api.v1.')->middleware(['throttle:' . config('api.rate_limits.access'),'shellStand'])->group(function () {
    //壳站订单查询
    Route::post('shellStand/query', 'ShellStandController@query');
    Route::post('shellStand/orderList', 'ShellStandController@orderList');
});


Route::namespace('Api')->middleware(['throttle:' . config('api.rate_limits.access')])->group(function () {
    //获取白名单卡号订单
    Route::post('getTrx', 'WhiteListQueryController@getTrx');
});

//虚拟卡
Route::prefix('v1')->namespace('Api')->name('vcc.')->middleware(['throttle:' . config('api.rate_limits.access'), 'change-locale'])->group(function () {
    Route::middleware('openVirtual')->group(function () {
        Route::post('vcc/card/batch/apply', 'OpenVirtualController@batchApply')->name('batchApply'); // 申请卡批次
        Route::get('vcc/card/batch/info', 'OpenVirtualController@batchInfo')->name('batchInfo'); // 卡批次信息
        Route::get('vcc/card/list', 'OpenVirtualController@getCardVirtualInfo')->name('getCardVirtualInfo'); // 获取虚拟卡信息
        Route::post('vcc/card/recharge', 'OpenVirtualController@cardVirtualRecharge')->name('cardVirtualRecharge'); // 虚拟卡充值
        Route::post('vcc/card/refund', 'OpenVirtualController@cardVirtualRefund')->name('cardVirtualRefund'); // 虚拟卡退值
        Route::get('vcc/card/order/list', 'OpenVirtualController@getCardRechargeInfo')->name('getCardRechargeInfo'); // 获取卡订单(充值/退值)信息
        Route::post('vcc/card/balance', 'OpenVirtualController@cardBalanceCheck')->name('cardBalanceCheck'); // 虚拟卡余额查询
        Route::post('vcc/card/share/limit', 'OpenVirtualController@cardLimitAmount')->name('cardLimitAmount'); // 虚拟卡额度限制
        Route::post('vcc/card/destroy', 'OpenVirtualController@cardDestroy')->name('cardDestroy'); // 销卡
        Route::post('vcc/card/revoke', 'OpenVirtualController@cardRevoke')->name('cardRevoke'); // 撤销销卡
        Route::post('vcc/card/block', 'OpenVirtualController@cardBlock')->name('cardBlock'); // 卡冻结
        Route::post('vcc/card/unblock', 'OpenVirtualController@cardUnblock')->name('cardUnblock'); // 卡解冻
        Route::get('vcc/card/authorizations', 'OpenVirtualController@getCardTransactions')->name('getCardTransactions'); // 获取虚拟卡卡交易信息
//        Route::post('vcc/card/detail', 'OpenVirtualController@getCardVirtualDetail')->name('getCardVirtualDetail'); // 获取虚拟卡详情
        Route::post('vcc/cid/apply', 'OpenVirtualController@addCid')->name('addCid'); // 添加CID
        Route::get('vcc/cid/info', 'OpenVirtualController@getCidInfo')->name('getCidInfo'); // 获取CID信息
        Route::post('vcc/cid/recharge', 'OpenVirtualController@cidRecharge')->name('cidRecharge'); // CID充值
        Route::post('vcc/cid/transfer', 'OpenVirtualController@cidAmountTransfer')->name('cidAmountTransfer'); // CID转出
        Route::get('vcc/cid/order/list', 'OpenVirtualController@getAllCidRechargeInfo')->name('getAllCidRechargeInfo'); // 获取所有CID充值信息
        Route::get('vcc/cid/settle/list', 'OpenVirtualController@getAllCidSettleInfo')->name('getAllCidSettleInfo'); // 获取所有CID结算明细
        Route::get('vcc/balance/list', 'OpenVirtualController@getMerchantBalanceInfo')->name('getMerchantBalanceInfo'); // 获取mid余额
    });

    Route::get('vcc/token', 'OpenVirtualController@token')->name('token');
});
